"use client";

import { useEffect, useState } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/dashboard/components/ui/Select";

type Course = {
  id: string;
  title: string;
  price: number;
};

type CourseSelectorProps = {
  selectedCourse: string | null;
  onSelectCourse: (courseId: string) => void;
};

export default function CourseSelector({ selectedCourse, onSelectCourse }: CourseSelectorProps) {
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        // الحصول على الكورسات المدفوعة فقط
        const response = await fetch("/api/courses/paid");
        const data = await response.json();
        
        if (data.success) {
          setCourses(data.courses);
        } else {
          console.error("Failed to fetch courses:", data.message);
        }
      } catch (error) {
        console.error("Error fetching courses:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  return (
    <div className="space-y-2">
      <label htmlFor="course-select" className="block text-sm font-medium">
        اختر الكورس
      </label>
      <Select
        value={selectedCourse || ""}
        onValueChange={onSelectCourse}
        disabled={isLoading || courses.length === 0}
      >
        <SelectTrigger id="course-select" className="w-full">
          <SelectValue placeholder={isLoading ? "جاري تحميل الكورسات..." : "اختر كورس"} />
        </SelectTrigger>
        <SelectContent>
          {courses.map((course) => (
            <SelectItem key={course.id} value={course.id}>
              {course.title} - {course.price} $
            </SelectItem>
          ))}
          {courses.length === 0 && !isLoading && (
            <SelectItem value="no-courses" disabled>
              لا توجد كورسات مدفوعة
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
