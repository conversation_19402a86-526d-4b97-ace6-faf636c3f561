import CourseForm from "@/components/dashboard-workspace/courses-CRUD/CourseForm";
import { courseSchema } from "@/components/dashboard-workspace/courses-CRUD/courseSchema";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";

export default async function CourseUpdatePage(props: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await props.params;
  const course = await prisma.course.findUnique({ where: { id } });

  if (!course) return notFound();

  return (
    <PageLayout title="تعديل الكورس" description="">
      <CourseForm
        mode="update"
        defaultValues={course}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = courseSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const newPosterUrl = await handleFileUpdate(
            course.posterUrl,
            data.posterUrl,
            "course/image/",
          );

          await prisma.course.update({
            where: { id: course.id },
            data: { ...data, posterUrl: newPosterUrl },
          });

          revalidatePath(`/courses`);
          revalidatePath(`/admin/courses`);
          revalidatePath(`/courses/${course.id}`);
          if (data.previewInHomePage) revalidatePath(`/`);
          revalidatePath(`/admin/courses/lectures/${course.id}`);

          return { success: true, message: "تم تحديث الكورس بنجاح" };
        }}
      />
    </PageLayout>
  );
}
