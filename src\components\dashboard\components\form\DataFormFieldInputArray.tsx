import React from "react";
import FieldAttachments from "../ui/FieldAttachments";

import { DataFormFieldProps } from "./DataFormField";
import { Badge } from "../ui/Badge";
import { Plus, X } from "lucide-react";
import { Button } from "../ui/Button";
import { Input, inputStyles } from "../ui/Input";
import { cx } from "../../lib/utils-tremor";

export default function DataFormFieldInputArray<TData extends { [K: string]: any }>(props: DataFormFieldProps<TData>) {
  const [arrayValues, setArrayValues] = React.useState<string[]>(
    Array.isArray(props.defaultValue) ? props.defaultValue : [],
  );

  const [inputValue, setInputValue] = React.useState("");

  React.useEffect(
    () => setArrayValues(Array.isArray(props.defaultValue) ? props.defaultValue : []),
    [props.defaultValue],
  );

  const addToArray = React.useCallback((value: string) => {
    const val = value.trim();
    if (!val) return;

    setArrayValues((prev) => (!prev.includes(val) ? [...prev, val] : prev));
    setInputValue("");
  }, []);

  const handleKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Enter") {
        event.preventDefault();
        addToArray(event.currentTarget.value);
      }
    },
    [addToArray],
  );

  return (
    <FieldAttachments
      description={props.description}
      label={props.label}
      required={props.required}
      htmlFor={props.accessorKey}
      errorMessage={props.errorMessage}
    >
      <div className={cx(inputStyles(), "w-full py-4")}>
        {arrayValues.length ? (
          <div className="flex flex-wrap gap-2">
            {arrayValues.map((item) => {
              return (
                <Badge variant="neutral" key={item} className="group pl-1">
                  {item}
                  <Button
                    disabled={props.isPending}
                    className="h-full rounded-xs p-0 px-0.5 hover:text-red-500"
                    onClick={() => setArrayValues((prev) => prev.filter((i) => i !== item))}
                    variant="ghost"
                    type="button"
                  >
                    <X className="!size-3 opacity-80" />
                  </Button>
                </Badge>
              );
            })}
          </div>
        ) : (
          <div className="flex items-center justify-center py-0.5">
            <p className="text-sm text-gray-500">لم يتم إضافة عناصر حتى الآن</p>
          </div>
        )}
        <div className="relative mt-5 flex items-center">
          <input
            readOnly
            type="hidden"
            name={`@parse${props.accessorKey}`}
            value={arrayValues.length && JSON.stringify(arrayValues)}
          />
          <Input
            required={false}
            value={inputValue}
            id={props.accessorKey}
            onKeyDown={handleKeyDown}
            placeholder={props.placeholder}
            disabled={props.isPending}
            type={props.inputConfig?.type ?? "text"}
            autoComplete={props.inputConfig?.autoComplete}
            onChange={(e) => setInputValue(e.target.value)}
          />
          <Button
            type="button"
            variant="light"
            onClick={() => addToArray(inputValue)}
            className="absolute left-0.5 h-[calc(100%-6px)] rounded-sm"
          >
            <Plus />
          </Button>
        </div>
      </div>
    </FieldAttachments>
  );
}
