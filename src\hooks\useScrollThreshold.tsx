import { useState, useEffect } from "react";

export function useScrollThreshold(threshold: number = 80): boolean {
  const [isPastThreshold, setIsPastThreshold] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrolledPast = window.scrollY > threshold;
      setIsPastThreshold((prev) => (prev !== scrolledPast ? scrolledPast : prev));
    };

    window.addEventListener("scroll", handleScroll);

    // استدعاء الدالة مباشرةً عند التحميل لتحديد الحالة الأولية
    handleScroll();

    return () => window.removeEventListener("scroll", handleScroll);
  }, [threshold]);

  return isPastThreshold;
}
