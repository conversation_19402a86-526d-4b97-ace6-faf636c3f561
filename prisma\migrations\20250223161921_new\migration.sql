/*
  Warnings:

  - Changed the type of `entity` on the `Comment` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "EntityType" AS ENUM ('lecture', 'book', 'interview', 'article', 'blogPost');

-- AlterTable
ALTER TABLE "Comment" DROP COLUMN "entity",
ADD COLUMN     "entity" "EntityType" NOT NULL;

-- AddForeignKey
ALTER TABLE "Comment" ADD CONSTRAINT "Comment_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
