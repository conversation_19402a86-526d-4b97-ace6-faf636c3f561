import {
  ServicesConsultingIcon,
  ServicesShortProgramsIcon,
  ServicesTherapySessionsIcon,
  ServicesTreatmentProgramsIcon,
} from "../ui/svg/icons";

const items = [
  {
    title: "خدمة استشارية",
    description:
      "نوفر حلولاً استشارية عاطفية مخصصة تُقدّم بسرية تامة، لتلبية احتياجات الشخصيات البارزة الباحثة عن التميز والخصوصية.",
    Icon: ServicesConsultingIcon,
  },
  {
    title: "برامج تدريبية",
    description:
      "دورات تدريبية مكثفة وفعالة تركز على تطوير المهارات الشخصية والمهنية في وقت قصير، مع تحقيق نتائج ملموسة.",
    Icon: ServicesShortProgramsIcon,
  },
  {
    title: "جلسات علاجية",
    description:
      "جلسات علاجية مميزة تُقدم في المنزل أو أثناء السفر، مع التركيز على تقديم تجربة فاخرة تضمن الراحة والخصوصية.",
    Icon: ServicesTherapySessionsIcon,
  },

  {
    title: "برامج علاجية",
    description:
      "برامج علاجية مرنة ومكثفة مصممة لتناسب جداول الشخصيات القيادية والمجتمعية المزدحمة، مع ضمان تقديم نتائج فعّالة.",
    Icon: ServicesTreatmentProgramsIcon,
  },
];

function ServicesCard({
  title,
  description,
  Icon,
}: {
  title: string;
  description: string;
  Icon: React.FunctionComponent<{ className?: string }>;
}) {
  return (
    <div className="flex flex-col items-center gap-7 text-balance text-center max-md:py-10">
      <div className="rounded-full shadow-[0_0_18px_#00000025]">
        <Icon />
      </div>
      <div className="flex flex-col items-center gap-3">
        <p className="max-w-min text-nowrap bg-primary-gradient-x bg-clip-text text-2xl font-bold text-transparent">
          {title}
        </p>
        <p className="font-light">{description}</p>
      </div>
    </div>
  );
}

export default function Services() {
  return (
    <div className="relative overflow-x-clip max-sm:mt-16">
      <div className="absolute -top-5 right-0 h-12 w-60 rounded-l-full bg-primary-gradient-x lg:-top-7 lg:h-14 lg:w-96"></div>
      <div className="absolute -bottom-5 left-0 h-12 w-60 rounded-r-full bg-primary-gradient-x lg:-bottom-7 lg:h-14 lg:w-96"></div>
      <div className="relative grid grid-cols-1 place-content-center gap-10 divide-muted/10 rounded-md bg-background px-6 py-28 shadow-[0_0_25px_#00000030] max-sm:divide-y sm:grid-cols-2 lg:grid-cols-4">
        {items.map((item, index) => {
          return <ServicesCard {...item} key={index} />;
        })}
      </div>
    </div>
  );
}
