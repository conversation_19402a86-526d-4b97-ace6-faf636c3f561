"use client";
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types";
import { Lecture } from "@prisma/client";
import React from "react";
import PageLayout from "../PageLayout";
import DataTable from "@/components/dashboard/components/table/DataTable";
import TableImage from "../TableImage";
import TableFile from "../TableFile";
import TableArray from "../TableArray";
import FormatTime from "@/components/ui/format-time";

export default function LecturesTable({
  data,
  onDelete,
  courseId,
  courseTitle,
}: {
  data: Lecture[];
  courseId: string;
  courseTitle: string;
  onDelete?: DataTableOnDeleteFnDef<Lecture>;
}) {
  return (
    <PageLayout title="محاور الكورس" description={`المحاور التابعة للكورس: "${courseTitle}"`}>
      <DataTable<Lecture>
        data={data}
        columnSearch={{ columnKey: "title", placeholder: "بحث بالاسم ..." }}
        createDataButton={{
          href: `/admin/courses/lectures/${courseId}/create`,
          label: "محور جديد",
        }}
        defaultPageSize={5}
        rowActions={{
          links: {
            items: [
              {
                label: "تعديل",
                basePath: `/admin/courses/lectures/${courseId}/update`,
              },
            ],
          },
          onDelete,
        }}
        createColumns={[
          { accessorKey: "title", columnLabel: "اسم المحور" },
          {
            accessorKey: "posterUrl",
            columnLabel: "صورة المحور",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "video",
            columnLabel: "فيديو",
            cell: ({ value }) => <TableFile fileType="video" href={value} />,
          },
          {
            accessorKey: "audio",
            columnLabel: "صوت",
            cell: ({ value }) => <TableFile fileType="audio" href={value} />,
          },
          {
            accessorKey: "pdf",
            columnLabel: "ملف PDF",
            cell: ({ value }) => <TableFile fileType="pdf" href={value} />,
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => <FormatTime dateInput={value} />,
          },
          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  );
}
