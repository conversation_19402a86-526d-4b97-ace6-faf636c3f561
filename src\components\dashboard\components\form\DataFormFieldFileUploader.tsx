import FieldAttachments from "../ui/FieldAttachments";
import FileUploader from "../fileUploader/FileUploader";
import { DataFormFieldProps } from "./DataFormField";

export default function DataFormFieldFileUploader<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>,
) {
  return (
    <FieldAttachments
      label={props.label}
      required={props.required}
      description={props.description}
      errorMessage={props.errorMessage}
      htmlFor={`@parse${props.accessorKey}`}
    >
      <FileUploader
        disabled={props.isPending}
        required={props.required}
        placeholder={props.placeholder}
        defaultValue={props.defaultValue}
        name={`@parse${props.accessorKey}`}
        dataType={props.fileUploaderConfig?.dataType}
        maxFiles={props.fileUploaderConfig?.maxFiles}
        instantUpload={props.fileUploaderConfig?.instantUpload}
        allowMultiple={props.fileUploaderConfig?.allowMultiple}
        acceptedFileTypes={props.fileUploaderConfig?.acceptedFileTypes}
      />
    </FieldAttachments>
  );
}
