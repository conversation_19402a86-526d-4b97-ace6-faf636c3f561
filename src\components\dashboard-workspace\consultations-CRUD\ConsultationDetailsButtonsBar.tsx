import { Button } from "@/components/dashboard/components/ui/Button";
import {
  Dialog,
  Di<PERSON><PERSON>ontent,
  DialogTrigger,
  DialogClose,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/dashboard/components/ui/Dialog";
import { Mail, MessageCircleCode, PhoneCallIcon, Trash, X } from "lucide-react";
import { ConsultationStateType, SetConsultationStateType } from "./Consultations";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { deleteConsultation } from "./db-queries";
import { Consultation } from "@prisma/client";
import React from "react";

export default function ConsultationDetailsButtonsBar({
  setData,
  activatedConsultation,
  setActivatedConsultation,
}: {
  activatedConsultation: ConsultationStateType;
  setActivatedConsultation: SetConsultationStateType;
  setData: React.Dispatch<React.SetStateAction<Consultation[]>>;
}) {
  const [, startTransition] = React.useTransition();
  const router = useRouter();
  if (!activatedConsultation) return null;

  return (
    <div className="flex w-full gap-x-0.5 border-b p-1.5">
      <Button
        type="button"
        variant="ghost"
        className="px-2.5"
        onClick={() => {
          if (activatedConsultation?.email) {
            window.location.href = `mailto:${activatedConsultation.email}`;
          }
        }}
      >
        <Mail />
      </Button>
      <Button
        type="button"
        variant="ghost"
        className="px-2.5"
        onClick={() => {
          if (activatedConsultation.phone) {
            window.open(`https://wa.me/${activatedConsultation.phone}`, "_blank");
          }
        }}
      >
        <MessageCircleCode />
      </Button>
      <Button
        type="button"
        variant="ghost"
        className="px-2.5"
        onClick={() => {
          if (activatedConsultation?.phone) {
            window.location.href = `tel:${activatedConsultation.phone}`;
          }
        }}
      >
        <PhoneCallIcon />
      </Button>
      <Dialog>
        <DialogTrigger asChild>
          <Button type="button" variant="ghost" className="mr-auto">
            <Trash className="text-red-500" />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تأكيد الحذف</DialogTitle>
            <DialogDescription>
              هذا الإجراء سيكون نهائيًا ولا رجعة فيه. لن يكون بإمكانك استرجاع أي من هذه البيانات، هل أنت متأكد من أنك
              ترغب في المتابعة؟
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button autoFocus={false} asChild variant="light">
                <Button variant="light">إلغاء</Button>
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button
                autoFocus={false}
                variant="destructive"
                onClick={() => {
                  startTransition(async () => {
                    const { success, message } = await deleteConsultation(activatedConsultation.id);
                    startTransition(() => {
                      if (success && message) toast.success(message);
                      if (!success && message) toast.error(message);
                      setActivatedConsultation(undefined);
                      setData((prev) => prev.filter((consultation) => consultation.id !== activatedConsultation.id));
                      router.refresh();
                    });
                  });
                }}
              >
                حــــذف
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Button type="button" variant="ghost" onClick={() => setActivatedConsultation(undefined)}>
        <X className="text-red-500" />
      </Button>
    </div>
  );
}
