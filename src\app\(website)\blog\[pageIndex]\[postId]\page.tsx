import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { getBlogPosts } from "@/utils/get-data-from-db"
import { siteName } from "@/utils/siteConfig"
import { BlogPost } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import Comments from "@/components/ui/comments/comments"
import FormatTime from "@/components/ui/format-time"
import PageDivider from "@/components/ui/page-divider"

type PostPageProps = { params: Promise<{ postId: string; pageIndex: string }> }

export const dynamic = "force-static"

const genarateData = async (props: PostPageProps) => {
  const params = await props.params
  const postId = params.postId
  const pageIndex = Number(params.pageIndex)

  const { data: response } = await getBlogPosts({ pageIndex })
  const data = response?.find((post) => post.id === postId)
  const related = response?.filter((post) => post.id !== postId)

  if (!data) return notFound()

  return { data, related, pageIndex }
}

export async function generateMetadata(
  props: PostPageProps
): Promise<Metadata> {
  const { data, pageIndex } = await genarateData(props)

  return {
    title: data.title,
    description: data.seoDescription,
    keywords: data.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      siteName: siteName,
      images: data.image,
      description: data.seoDescription,
      url: `/blog/${pageIndex}/${data.id}`,
      title: `${data.title} | د. ناهد باشطح`,
    },
  }
}

// ==================================================================================
// صفحة عرض منشور واحد من منشورات المدونة
// ==================================================================================
export default async function PostPage(props: PostPageProps) {
  const { data, related, pageIndex } = await genarateData(props)

  return (
    <div className="mb-28 px-3 pt-16 lg:px-6">
      <PageDivider
        pageContents={<PostPageContent {...data} />}
        titleMenu="منشورات ذات صلة"
        menuItems={related?.map((post) => (
          <PostMenuCard key={post.id} post={post} pageIndex={pageIndex} />
        ))}
        comments={
          <Comments
            entity="blogPost"
            entityId={data.id}
            pathRevalidate={`/blog/${pageIndex}/${data.id}`}
          />
        }
      />
    </div>
  )
}

// ==================================================================================
// مكون يعرض محتوى المنشور
// ==================================================================================
function PostPageContent(post: BlogPost) {
  return (
    <div className="space-y-7 py-8">
      <h1 className="text-3xl leading-relaxed font-bold drop-shadow-[0_2px_1px_#00000050] sm:text-4xl sm:leading-relaxed">
        {post.title}
      </h1>
      <div className="relative aspect-640/426 w-3/4 overflow-clip rounded-lg">
        <Image
          src={post.image}
          className="scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 75vw, 50vw"
          alt={post.title}
          fill
        />
      </div>
      <div
        className="textHTML"
        dangerouslySetInnerHTML={{ __html: post.content }}
      ></div>
    </div>
  )
}

// ==================================================================================
// مكون بطاقة المنشور في القائمة المنشورات ذات الصلة
// ==================================================================================
function PostMenuCard({
  post,
  pageIndex,
}: {
  post: BlogPost
  pageIndex: number
}) {
  return (
    <Link
      href={`/blog/${pageIndex}/${post.id}`}
      className="flex snap-start items-start gap-3 rounded-lg bg-linear-to-r py-3"
    >
      <div className="bg-muted/80 relative aspect-640/426 w-[30%] shrink-0 overflow-clip rounded-md">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 20vw, 7vw"
          src={post.image}
          alt={post.title}
          fill
        />
      </div>
      <div className="w-[64%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{post.title}</p>
        <p className="text-background/70 line-clamp-2 w-full text-xs">
          {post.seoDescription}
        </p>
        <p className="text-background/70 flex gap-1 pt-1 text-[10px]">
          <CalendarDays className="size-3" />{" "}
          <FormatTime dateInput={post.createdAt} />
        </p>
      </div>
    </Link>
  )
}
