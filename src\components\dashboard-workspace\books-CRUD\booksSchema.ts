import { z } from "zod";

export const booksSchema = z.object({
  id: z.string(),
  title: z.string().trim(),
  summary: z.string().trim(),
  coverImage: z.string().trim().url(),
  pagesCount: z
    .number()
    .or(
      z
        .string()
        .trim()
        .optional()
        .transform((val) => (val === "" ? 0 : Number(val)))
        .pipe(z.number()),
    )
    .optional()
    .default(0),
  price: z
    .number()
    .or(
      z
        .string()
        .trim()
        .optional()
        .transform((val) => (val === "" ? 0 : Number(val)))
        .pipe(z.number()),
    )
    .optional()
    .default(0),
  bookUrl: z.string().trim().url(),
  seoDescription: z.string().trim(),
  seokeywords: z.array(z.string().trim()),
});
