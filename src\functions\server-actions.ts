"use server";
import { verifySession } from "@/auth/dal";
import { deleteSession } from "@/auth/session";

export async function getCurrentUser() {
  const session = await verifySession();
  if (!session) {
    return undefined;
  }
  return session;
}

export async function deleteSessionAction() {
  "use server";
  const session = await verifySession();

  if (!session) {
    return { success: false, message: "ليس لديك حساب لتتمكن من تسجل الخروج منه" };
  }

  await deleteSession();
  return { success: true, message: "تم تسجيل الخروج بنجاح" };
}
