// ================================================================================== //
// استخراج بيانات الفورم الى كائن
// ================================================================================== //
export function fromEntries(formData: FormData) {
  const object = Object.fromEntries(
    Array.from(formData.entries()).filter(([key]) => !key.startsWith("$")),
  );
  return object;
}

export function formatTimeFunction(dateInput: string | Date): string {
  const inputDate = new Date(dateInput);
  const now = new Date();
  const diff = now.getTime() - inputDate.getTime();

  // تعريف الثوابت بالملّي ثانية
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day; // تقريباً
  const year = 365 * day; // تقريباً

  if (diff < minute) {
    // أقل من دقيقة
    return "الآن";
  } else if (diff < hour) {
    // أكثر من دقيقة وأقل من ساعة
    const minutes = Math.floor(diff / minute);
    return minutes === 1
      ? "قبل دقيقة"
      : minutes > 10
        ? `قبل ${minutes} دقيقة`
        : `قبل ${minutes} دقائق`;
  } else if (diff < 2 * hour) {
    // من 60 دقيقة وحتى أقل من ساعتين
    return "قبل ساعة";
  } else if (diff < day) {
    // من ساعتين وحتى أقل من 24 ساعة
    const hours = Math.floor(diff / hour);
    return hours === 1
      ? "قبل ساعة"
      : hours > 10
        ? `قبل ${hours} ساعة`
        : `قبل ${hours} ساعات`;
  } else if (diff < week) {
    // من 24 ساعة وحتى أقل من أسبوع
    const days = Math.floor(diff / day);
    return days === 1 ? "قبل يوم" : `قبل ${days} أيام`;
  } else if (diff < month) {
    // من أسبوع وحتى أقل من شهر (تقريباً)
    const weeks = Math.floor(diff / week);
    return weeks === 1 ? "قبل أسبوع" : `قبل ${weeks} أسابيع`;
  } else if (diff < year) {
    // من شهر وحتى أقل من سنة
    const months = Math.floor(diff / month);
    return months === 1
      ? "قبل شهر"
      : months > 10
        ? `قبل ${months} شهر`
        : `قبل ${months} أشهر`;
  } else {
    // أكثر من سنة: عرض التاريخ بصيغة "YYYY-MM-DD"
    const y = inputDate.getFullYear();
    const m = String(inputDate.getMonth() + 1).padStart(2, "0");
    const d = String(inputDate.getDate()).padStart(2, "0");
    return `${y}-${m}-${d}`;
  }
}
