"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSubMenu,
  DropdownMenuSubMenuContent,
  DropdownMenuSubMenuTrigger,
  DropdownMenuTrigger,
} from "@/components/dashboard/components/ui/DropdownMenu";
import { cx } from "@/components/dashboard/lib/utils-tremor";
import { Monitor, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import * as React from "react";
import { useAdminSession } from "../../auth/useAdminSession";
import { logOutAction } from "../../auth/server-actions";
import { useRouter } from "next/navigation";

export type DropdownUserProfileProps = {
  children: React.ReactNode;
  align?: "center" | "start" | "end";
};

export function DropdownUserProfile({ children, align = "start" }: DropdownUserProfileProps) {
  const { theme, setTheme } = useTheme();
  const adminSession = useAdminSession();
  const [isPending, startTransition] = React.useTransition();
  const router = useRouter();

  const handleLogOut = () => {
    startTransition(async () => {
      await logOutAction();
      startTransition(() => {
        router.refresh();
      });
    });
  };

  return (
    <>
      <DropdownMenu dir="rtl">
        <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
        <DropdownMenuContent align={align} className={cx("sm:min-w-[calc(var(--radix-dropdown-menu-trigger-width))]!")}>
          <DropdownMenuLabel>{adminSession?.email}</DropdownMenuLabel>
          <DropdownMenuGroup>
            <DropdownMenuSubMenu>
              <DropdownMenuSubMenuTrigger>الألوان</DropdownMenuSubMenuTrigger>
              <DropdownMenuSubMenuContent>
                <DropdownMenuRadioGroup
                  value={theme}
                  onValueChange={(value) => {
                    setTheme(value);
                  }}
                >
                  <DropdownMenuRadioItem aria-label="Switch to Light Mode" value="light" iconType="check">
                    <Sun className="size-4 shrink-0" aria-hidden="true" />
                    فاتح
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem aria-label="Switch to Dark Mode" value="dark" iconType="check">
                    <Moon className="size-4 shrink-0" aria-hidden="true" />
                    داكن
                  </DropdownMenuRadioItem>
                  <DropdownMenuRadioItem aria-label="Switch to System Mode" value="system" iconType="check">
                    <Monitor className="size-4 shrink-0" aria-hidden="true" />
                    النظام
                  </DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuSubMenuContent>
            </DropdownMenuSubMenu>
          </DropdownMenuGroup>
          {/* <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem>
              Changelog
              <ArrowUpRight
                className="mb-1 ml-1 size-3 shrink-0 text-gray-500 dark:text-gray-500"
                aria-hidden="true"
              />
            </DropdownMenuItem>
            <DropdownMenuItem>
              Documentation
              <ArrowUpRight
                className="mb-1 ml-1 size-3 shrink-0 text-gray-500"
                aria-hidden="true"
              />
            </DropdownMenuItem>
            <DropdownMenuItem>
              Join Slack community
              <ArrowUpRight
                className="mb-1 ml-1 size-3 shrink-0 text-gray-500"
                aria-hidden="true"
              />
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator /> */}
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem disabled={isPending} onClick={handleLogOut}>
              تسجيل الخروج
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
