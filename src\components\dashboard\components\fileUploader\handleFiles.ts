import { CopyObjectCommand, DeleteObjectCommand, S3Client } from "@aws-sdk/client-s3";

const S3 = new S3Client({
  region: "auto",
  apiVersion: "v4",
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.eu.r2.cloudflarestorage.com`, // endpoints for S3 clients

  credentials: {
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY!, // Secret Access Key
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID!, // Access Key ID
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID!, // ID from url
  },
});

export async function deleteFileInR2(props: { fileUrl: string }) {
  const commandDelete = new DeleteObjectCommand({
    Key: props.fileUrl.replace(`${process.env.CLOUDFLARE_PUBLIC_URL}/`, ""),
    Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
  });

  await S3.send(commandDelete);
  return null;
}

export async function moveFileInR2({
  fileUrl,
  moveTo,
  moveFrom,
}: {
  fileUrl: string;
  moveFrom: string;
  moveTo: string;
}) {
  const bucket = process.env.CLOUDFLARE_BUCKET_NAME;
  const oldKey = fileUrl.replace(`${process.env.CLOUDFLARE_PUBLIC_URL}/`, "");
  const newKey = oldKey.replace(moveFrom, moveTo);

  // Step 1: Copy to new location
  await S3.send(
    new CopyObjectCommand({
      Bucket: bucket,
      CopySource: encodeURIComponent(`${bucket}/${oldKey}`),
      Key: newKey,
    }),
  );

  await S3.send(
    new DeleteObjectCommand({
      Bucket: bucket,
      Key: oldKey,
    }),
  );

  return `${process.env.CLOUDFLARE_PUBLIC_URL}/${newKey}`;
}

/**
 * استخدم هذه الدالة عند تحديث الملفات
 * يمكنها التأكد من ان الملف قد تغير او لا
 * يمكنها نقل الملف من المسار المؤقت الى المسار الذي قمت بتحديده
 * اذا كان الملف قد تغير سترجع الدالة رابط الملف الجديد
 * إذا لم يتغير الملف سترجع الدالة رابط الملف القديم
 */
export async function handleFileUpdate<T extends string | null = string | null>(
  /** رابط السابق للملف */
  currentUrl: T,
  /** الرابط الجديد للملف */
  newUrl: T,
  /** المسار الذي تريد نقل الملف اليه */
  moveTo: `${string}/`,
  /** المسار الذي سيتم نقل الملف منه @default "tmp/" */
  moveFrom = "tmp/",
): Promise<T> {
  if (currentUrl !== newUrl) {
    const handle = newUrl
      ? await moveFileInR2({
          fileUrl: newUrl,
          moveFrom,
          moveTo,
        })
      : null;

    if (currentUrl && currentUrl !== "") await deleteFileInR2({ fileUrl: currentUrl });

    return handle as T;
  }
  return currentUrl;
}
