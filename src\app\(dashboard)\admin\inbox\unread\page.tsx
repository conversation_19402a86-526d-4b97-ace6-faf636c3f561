import Consultations from "@/components/dashboard-workspace/consultations-CRUD/Consultations";
import { getConsultationUnReadCache } from "@/components/dashboard-workspace/consultations-CRUD/db-queries";
import React from "react";

export default async function InBoxUnreadpage() {
  const { data, nextCursor } = await getConsultationUnReadCache();

  return <Consultations serverData={data} mode="unRead" nextCursor={nextCursor} />;
}
