"use server";

import {
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

type S3PresignerProps = {
  fileName: string;
  fileType: string;
  fileSize: number;
};

const S3 = new S3Client({
  region: "auto",
  apiVersion: "v4",
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.eu.r2.cloudflarestorage.com`, // endpoints for S3 clients

  credentials: {
    secretAccessKey: process.env.CLOUDFLARE_SECRET_ACCESS_KEY!, // Secret Access Key
    accessKeyId: process.env.CLOUDFLARE_ACCESS_KEY_ID!, // Access Key ID
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID!, // ID from url
  },
});

export async function fileUploaderS3Presigner(props: S3PresignerProps) {
  const { fileName, fileType, fileSize } = props;

  const Key = `tmp/${fixedFileName(fileName)}`;
  const commandInput: PutObjectCommandInput = {
    Key,
    Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
    ContentLength: fileSize,
    ContentType: fileType,
    Metadata: {
      "x-amz-meta-test": "my-test",
    },
  };

  const commandPut = new PutObjectCommand(commandInput);

  // لجلب رابط الملفات الخاصة
  // const commandGet = new GetObjectCommand(commandInput);
  // const fileUrl = await getSignedUrl(s3, commandGet, { expiresIn: 3600 });

  const signedUrl = await getSignedUrl(S3, commandPut, { expiresIn: 7200 });
  const fileUrl = `${process.env.CLOUDFLARE_PUBLIC_URL}/${Key}`;

  return { signedUrl, fileUrl };
}

export async function fileUploaderDeleteFile(props: { fileUrl: string }) {
  const commandDelete = new DeleteObjectCommand({
    Key: props.fileUrl.replace(`${process.env.CLOUDFLARE_PUBLIC_URL}/`, ""),
    Bucket: process.env.CLOUDFLARE_BUCKET_NAME,
  });

  await S3.send(commandDelete);
  return null;
}

function fixedFileName(nameFile: string, digitsLength = 5): string {
  const fileName = nameFile.replace(/\s+/g, "-"); // Replace spaces with hyphens
  const randomDigits = Array.from({ length: digitsLength }, () =>
    Math.floor(Math.random() * 10),
  ).join("");

  const dotIndex = fileName.lastIndexOf(".");
  if (dotIndex === -1) {
    // ما فيه امتداد
    return `${fileName}-${randomDigits}`;
  }

  const name = fileName.slice(0, dotIndex);
  const extension = fileName.slice(dotIndex);

  return `${name}-${randomDigits}${extension}`;
}
