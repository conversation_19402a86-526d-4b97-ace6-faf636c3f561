"use client";

import { useState, useEffect, useTransition } from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { cn } from "@/lib/utils";
import { CheckCircle, Loader2, Mail, RefreshCw } from "lucide-react";
import { resendVerificationAction } from "@/app/(website)/auth/waiting-verify/resend-verification-action";
import { toast } from "sonner";
import Image from "next/image";
import authImage from "@/../public/auth.jpg";
import { Badge } from "@/components/ui/badge";

type WaitingVerificationFormProps = {
  verificationTokenExpiryMinutes: number;
  className?: string;
  email: string;
  userName: string;
  canResendVerification: boolean;
  timeLeftSeconds: number;
};

export function WaitingVerificationForm({
  className,
  email,
  userName,
  timeLeftSeconds,
  canResendVerification,
  verificationTokenExpiryMinutes,
}: WaitingVerificationFormProps) {
  const [isPending, startTransition] = useTransition();
  const [countdown, setCountdown] = useState<number>(timeLeftSeconds);
  const [canResend, setCanResend] = useState<boolean>(canResendVerification);
  const [emailSent, setEmailSent] = useState<boolean>(true);

  // Format countdown time as HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    } else {
      return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
    }
  };

  // لا نحتاج إلى فحص أولي لأننا نحصل على المعلومات مباشرة من الصفحة

  // Handle countdown timer
  useEffect(() => {
    // Only start countdown if we have a value and can't resend yet
    if (countdown !== null && countdown > 0 && !canResend) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  // Handle resend verification email
  const handleResendVerification = () => {
    if (!canResend || isPending) return;

    startTransition(async () => {
      try {
        setEmailSent(false);
        const result = await resendVerificationAction(email, userName);

        if (result.success) {
          toast.success(result.message);
          setEmailSent(true);
          setCanResend(false);
          // بعد إرسال البريد الإلكتروني بنجاح، نعيد تعيين العد التنازلي باستخدام المتغير المركزي
          setCountdown(verificationTokenExpiryMinutes * 60); // تحويل الدقائق إلى ثواني
        } else {
          toast.error(result.message);
        }

        // eslint-disable-next-line
      } catch (_error) {
        toast.error("حدث خطأ أثناء إرسال رابط التحقق. يرجى المحاولة مرة أخرى.");
      }
    });
  };

  return (
    <div className={cn("flex w-full items-center justify-center **:leading-relaxed", className)}>
      <Card className="w-full max-w-sm overflow-hidden rounded-xl md:grid md:max-w-3xl md:grid-cols-2">
        <CardContent className="p-0">
          <div className="flex h-full flex-col justify-between p-6">
            <div className="flex flex-col space-y-2 text-center">
              <div className="flex justify-center">
                <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
                  <Mail className="h-8 w-8 text-primary" />
                </div>
              </div>
              <h1 className="text-2xl font-bold text-primary">تأكيد البريد الإلكتروني</h1>
              <p className="text-base text-secondary">
                تم إرسال رابط التحقق إلى بريدك الإلكتروني
                <br />
                <Badge variant="green" className="mt-2 py-1 text-foreground">
                  {email}
                </Badge>
              </p>
              <p className="my-5 text-sm text-secondary">
                يرجى التحقق من بريدك الإلكتروني (بما في ذلك مجلد البريد غير المرغوب فيه) لتأكيد حسابك.
              </p>
            </div>

            <div className="mt-6 space-y-4">
              {!canResend && countdown > 0 ? (
                <div className="flex flex-col items-center space-y-2 text-center">
                  <p className="text-sm text-secondary">لم تستلم البريد الإلكتروني؟ يمكنك طلب إرسال رابط جديد بعد:</p>
                  <div className="flex h-10 min-w-20 items-center justify-center rounded-md bg-primary/10 px-3 font-mono text-lg font-semibold text-primary">
                    {formatTime(countdown)}
                  </div>
                </div>
              ) : (
                <Button onClick={handleResendVerification} disabled={isPending || !canResend} className="w-full">
                  {isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري إرسال رابط جديد...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      إرسال رابط تحقق جديد
                    </>
                  )}
                </Button>
              )}

              {emailSent && (
                <div className="flex items-center justify-center rounded-md bg-green-50 p-2 text-sm text-green-600">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  تم إرسال رابط التحقق بنجاح
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <div className="relative hidden bg-muted md:block">
          <Image
            src={authImage}
            placeholder="blur"
            alt="Image"
            className="absolute inset-0 h-full w-full object-cover"
          />
        </div>
      </Card>
    </div>
  );
}
