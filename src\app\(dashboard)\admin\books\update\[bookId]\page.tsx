import BooksForm from "@/components/dashboard-workspace/books-CRUD/BooksForm";
import { booksSchema } from "@/components/dashboard-workspace/books-CRUD/booksSchema";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { dataLimits } from "@/utils/siteConfig";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";
import React from "react";

export default async function page(props: { params: Promise<{ bookId: string }> }) {
  const { bookId } = await props.params;
  const book = await prisma.book.findUnique({ where: { id: bookId } });
  if (!book) return notFound();

  return (
    <PageLayout title="تعديل الكتاب">
      <BooksForm
        mode="update"
        defaultValues={book}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = booksSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const [bookUrl, coverImage] = await Promise.all([
            handleFileUpdate(book.bookUrl, data.bookUrl, "books/pdf/"),
            handleFileUpdate(book.coverImage, data.coverImage, "books/image/"),
          ]);

          const [updatedBook, booksBefore] = await prisma.$transaction([
            prisma.book.update({
              where: { id: data.id },
              data: { ...data, bookUrl, coverImage },
            }),

            prisma.book.count({
              where: {
                createdAt: {
                  gt: book.createdAt,
                },
              },
            }),
          ]);

          const pageIndex = Math.floor(booksBefore / dataLimits.books) + 1;

          revalidatePath("/admin/books");
          revalidatePath(`/media-experience/books/${pageIndex}`);
          revalidatePath(`/media-experience/books/${pageIndex}/${updatedBook.id}`);

          return { success: true, message: "تم تعديل الكتاب بنجاح" };
        }}
      />
    </PageLayout>
  );
}
