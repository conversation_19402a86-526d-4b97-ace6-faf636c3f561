import React, { Fragment } from "react";

export default function TableArray({ arr }: { arr: string[] }) {
  return (
    <div className="flex flex-nowrap items-center gap-x-0.5 whitespace-nowrap text-blue-500 dark:text-blue-400">
      <code>
        <pre>
          <span className="text-lg">]</span>
        </pre>
      </code>

      <div className="flex flex-nowrap whitespace-nowrap text-gray-500 dark:text-gray-400">
        {arr.map((e) => (
          <Fragment key={e}>
            <span className="h-7 overflow-clip pt-1.5">{e}</span>
            <span className="mr-0.5 ml-2 h-7 overflow-clip text-lg font-bold text-blue-500 last:hidden dark:text-blue-400">
              ,
            </span>
          </Fragment>
        ))}
      </div>
      <code>
        <pre>
          <span className="text-lg">[</span>
        </pre>
      </code>
    </div>
  );
}
