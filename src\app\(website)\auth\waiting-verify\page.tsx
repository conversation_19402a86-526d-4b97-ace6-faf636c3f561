import Loading from "@/components/ui/loading";
import prisma from "@/lib/prisma";
import Link from "next/link";

import { WaitingVerificationForm } from "@/app/(website)/auth/waiting-verify/waiting-verification-form";
import { siteName, verificationTokenExpiryMinutes } from "@/utils/siteConfig";
import { Button } from "@/components/ui/button";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: `في انتظار تأكيد البريد الإلكتروني | ${siteName}`,
  description: "انتظار تأكيد البريد الإلكتروني في موقع اكاديمية د. ناهد باشطح",
  robots: {
    index: false,
    follow: false,
  },
};

export default function WaitingVerifyPage({ searchParams }: { searchParams: Promise<{ email?: string }> }) {
  return (
    <div className="container my-8 flex min-h-[calc(100vh-200px)] w-full items-center justify-center py-8">
      <Suspense
        fallback={
          <div className="flex h-96 w-full max-w-sm flex-col gap-6 rounded-xl bg-background md:max-w-3xl">
            <Loading />
          </div>
        }
      >
        <WaitingVerifyContent searchParams={searchParams} />
      </Suspense>
    </div>
  );
}

async function WaitingVerifyContent({ searchParams }: { searchParams: Promise<{ email?: string }> }) {
  const email = (await searchParams).email;
  if (!email) {
    return (
      <div className="flex h-96 w-full max-w-sm flex-col items-center justify-center gap-6 rounded-xl bg-background md:max-w-3xl">
        <p className="font-semibold text-destructive">
          هل وصلت الى هنا عن طريق الخطأ؟ انقر على الزر أدناه للعودة إلى الصفحة الرئيسية.
        </p>
        <Button className="mt-4" asChild>
          <Link replace href="/">
            العودة إلى الرئيسية
          </Link>
        </Button>
      </div>
    );
  }

  const user = await prisma.user.findUnique({
    where: { email, emailVerified: false },
  });

  if (!user) {
    return (
      <div className="flex h-96 w-full max-w-sm flex-col items-center justify-center gap-6 rounded-xl bg-background md:max-w-3xl">
        <p className="font-semibold text-destructive">
          هل وصلت الى هنا عن طريق الخطأ؟ انقر على الزر أدناه للعودة إلى الصفحة الرئيسية.
        </p>

        <Button className="mt-4" asChild>
          <Link replace href="/">
            العودة إلى الرئيسية
          </Link>
        </Button>
      </div>
    );
  }

  // حساب الوقت المتبقي للإرسال باستخدام المتغير المركزي
  let canResendVerification = true;
  let timeLeftSeconds = 0;

  if (user.verificationTokenExpiry) {
    const now = new Date();

    // التحقق مما إذا كان رمز التحقق لا يزال صالحًا
    if (user.verificationTokenExpiry > now) {
      // لا يمكن إعادة الإرسال طالما أن الرمز الحالي لا يزال صالحًا
      canResendVerification = false;

      // حساب الوقت المتبقي حتى انتهاء صلاحية الرمز الحالي (بالثواني)
      timeLeftSeconds = Math.ceil((user.verificationTokenExpiry.getTime() - now.getTime()) / 1000);

      // التأكد من أن الوقت المتبقي لا يتجاوز الحد الأقصى المحدد في المتغير المركزي
      const maxTimeLeftSeconds = verificationTokenExpiryMinutes * 60;
      if (timeLeftSeconds > maxTimeLeftSeconds) {
        timeLeftSeconds = maxTimeLeftSeconds;
      }
    }
  }

  return (
    <WaitingVerificationForm
      email={email}
      userName={user.name}
      timeLeftSeconds={timeLeftSeconds}
      canResendVerification={canResendVerification}
      verificationTokenExpiryMinutes={verificationTokenExpiryMinutes}
    />
  );
}
