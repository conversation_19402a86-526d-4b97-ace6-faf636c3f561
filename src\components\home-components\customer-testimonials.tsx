import TitleSection from "../ui/title-section";

import { QuoteIcon } from "../ui/svg/icons";
import { getTestimonials } from "@/utils/get-data-from-db";

export default async function CustomersTestimonials() {
  const testimonials = await getTestimonials();
  if (!testimonials || !testimonials.length) return null;

  return (
    <div className="py-32 md:py-44">
      <TitleSection
        title={<h2>شهادات عملاء سابقين</h2>}
        description="نعتز بثقة عملائنا ونفخر بمشاركتكم تجاربهم معنا. إليكم بعض الشهادات من أشخاص خاضوا رحلتهم العلاجية تحت إشراف الدكتورة ناهد باشطح، حيث لمسوا الفرق وشعروا بالتحسن بفضل الرعاية المهنية والاهتمام الشخصي."
        classDescription="max-sm:text-sm max-sm:leading-relaxed"
        classContainer="lg:max-w-4xl"
      />
      <div className="flex items-center justify-center gap-14 py-12 text-center text-sm leading-loose max-sm:flex-col sm:flex-wrap md:gap-16 md:py-20">
        {testimonials.map(({ id, testimony }) => (
          <div
            key={id}
            className="relative flex aspect-square max-w-72 items-center justify-center rounded-bl-3xl rounded-br-lg rounded-tl-lg rounded-tr-3xl bg-secondary px-7 py-10 text-background shadow-lg shadow-black/40"
          >
            <QuoteIcon className="absolute -top-6 right-5 size-16" />
            <QuoteIcon className="absolute -bottom-6 right-[calc(50%-20px)] size-16 origin-center rotate-180" />
            <p className="line-clamp-6">{testimony}</p>
          </div>
        ))}
      </div>
    </div>
  );
}
