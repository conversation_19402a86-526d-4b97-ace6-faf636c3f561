"use client";

import { toast } from "sonner";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";
import { Skeleton } from "../ui/skeleton";
import { subscribeAction } from "./Subscribe-action";
import { useTransition } from "react";
import { useRouter } from "next/navigation";
import useSession from "@/hooks/useSession";
import { SubscriptionInstructionsPaidCourseDialog } from "./subscribe-course-dialog";

type SubscribeCourseButtonProps = { courseId: string; coursePrice: number; subscriptionInstructions: string };

// ==================================================================================
// مكون زر اشتراك او شراء الكورس
// ==================================================================================
export function SubscribeCourseButton({ courseId, coursePrice, subscriptionInstructions }: SubscribeCourseButtonProps) {
  const isPaid = coursePrice > 0;
  const session = useSession();
  const [isPending, startTransition] = useTransition();
  const isSubscribed = session?.purchasesIds.includes(courseId);
  const router = useRouter();

  // تتعامل مع النقر على زر الإشتراك
  function handleSubscribe() {
    startTransition(async () => {
      if (!session) {
        const callbackUrl = `/courses/${courseId}`;
        router.push(`/auth/login?callback-url=${callbackUrl}`, { scroll: true });
        return;
      }
      const subscribe = await subscribeAction({ courseId });

      if (!subscribe.success) {
        toast.error(subscribe.message);
        return;
      }

      toast.success(subscribe.message);
      window.location.reload();
      window.scroll({ top: 0 });
      return;
    });
  }

  // تمنع الزر من الضهور حتا يتم التحقق من ما اذا كان المستخدم قد اشترك في الكورس او لا
  if (session === null) {
    return (
      <Skeleton className="flex h-10 w-32 items-center justify-center">
        <Loader2 className="size-6 animate-spin text-background" />
      </Skeleton>
    );
  }

  if (!isPaid) {
    return (
      <Button onClick={!isSubscribed ? handleSubscribe : undefined} disabled={isPending || isSubscribed}>
        {isSubscribed ? "تم الاشتراك" : `اشتراك مجاني`}
      </Button>
    );
  }

  return (
    <SubscriptionInstructionsPaidCourseDialog subscriptionInstructions={subscriptionInstructions}>
      <Button disabled={isPending || isSubscribed} className="bg-paid text-paid-foreground">
        {isSubscribed ? "تم الإشتراك" : `إشتراك ${coursePrice}$`}
      </Button>
    </SubscriptionInstructionsPaidCourseDialog>
  );
}
