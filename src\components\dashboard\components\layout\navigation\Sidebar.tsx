"use client";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerTitle,
  DrawerDescription,
} from "@/components/dashboard/components/ui/Drawer";
import { useIsMobile } from "@/components/dashboard/components/layout/navigation/useMobile";
import { cx, focusRing } from "@/components/dashboard/lib/utils-tremor";
import * as VisuallyHidden from "@radix-ui/react-visually-hidden";
import { PanelRight, X } from "lucide-react";
import Link from "next/link";
import * as React from "react";
import { Button } from "../../ui/Button";
import { SidebarContext, SidebarContextType, useSidebar } from "./SidebarContext";

// This component is based on shadcn's sidebar component

const SIDEBAR_COOKIE_NAME = "sidebar:state";
const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;
const SIDEBAR_WIDTH = "17rem";

const SidebarProvider = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div"> & {
    defaultOpen?: boolean;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
  }
>(({ defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props }, ref) => {
  const isMobile = useIsMobile();
  const [openMobile, setOpenMobile] = React.useState(false);

  const [_open, _setOpen] = React.useState(defaultOpen);
  const open = openProp ?? _open;
  const setOpen = React.useCallback(
    (value: boolean | ((value: boolean) => boolean)) => {
      const openState = typeof value === "function" ? value(open) : value;
      if (setOpenProp) {
        setOpenProp(openState);
      } else {
        _setOpen(openState);
      }

      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;
    },
    [setOpenProp, open],
  );

  const toggleSidebar = React.useCallback(() => {
    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);
  }, [isMobile, setOpen, setOpenMobile]);

  const state = open ? "expanded" : "collapsed";

  const contextValue = React.useMemo<SidebarContextType>(
    () => ({
      state,
      open,
      setOpen,
      isMobile,
      openMobile,
      setOpenMobile,
      toggleSidebar,
    }),
    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar],
  );

  return (
    <SidebarContext.Provider value={contextValue}>
      <div
        style={
          {
            "--sidebar-width": SIDEBAR_WIDTH,
            ...style,
          } as React.CSSProperties
        }
        className={cx("relative flex min-h-svh w-full", className)}
        ref={ref}
        {...props}
      >
        {children}
      </div>
    </SidebarContext.Provider>
  );
});
SidebarProvider.displayName = "SidebarProvider";

const Sidebar = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(
  ({ className, children, ...props }, ref) => {
    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();

    if (isMobile) {
      return (
        <SidebarMobile openMobile={openMobile} setOpenMobile={setOpenMobile}>
          {children}
        </SidebarMobile>
      );
    }

    return (
      <div
        ref={ref}
        className={cx(
          "group peer sticky top-0 right-0 hidden h-svh w-(--sidebar-width) shrink-0 bg-transparent transition-[width] duration-150 ease-in-out will-change-transform md:block",
          "data-[collapsible=true]:w-0",
        )}
        data-state={state}
        data-collapsible={state === "collapsed" ? true : false}
      >
        {/* This is what handles the sidebar gap on desktop */}
        {/* group-data-[collapsible=true]:group-data-[scroll-locked=1]/body:right-[10px] */}
        <div
          className={cx(
            "absolute inset-y-0 z-10 hidden w-(--sidebar-width) transition-[right,right,width] duration-150 ease-in-out will-change-transform md:flex",
            "right-0 group-data-[collapsible=true]:right-[calc(var(--sidebar-width)*-1)]",
            "border-l border-gray-200 dark:border-gray-800",
            className,
          )}
          {...props}
        >
          <div data-sidebar="sidebar" className="bg-sidebar flex h-full w-full flex-col">
            {children}
          </div>
        </div>
      </div>
    );
  },
);

type SidebarMobileProps = {
  openMobile: boolean;
  setOpenMobile: (open: boolean) => void;
  children: React.ReactNode;
};
function SidebarMobile({ children, openMobile, setOpenMobile }: SidebarMobileProps) {
  return React.useMemo(
    () => (
      <Drawer open={openMobile} onOpenChange={setOpenMobile}>
        <DrawerContent
          // data-sidebar="sidebar"
          // data-mobile="true"
          dir="rtl"
          className="bg-gray-50 p-0 text-gray-900"
        >
          <VisuallyHidden.Root>
            <DrawerTitle>Sidebar</DrawerTitle>
            <DrawerDescription>Sidebar</DrawerDescription>
          </VisuallyHidden.Root>
          <div className="relative flex h-full w-full flex-col">
            <DrawerClose className="absolute top-4 left-4" asChild>
              <Button
                variant="ghost"
                className="p-2! text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-50"
              >
                <X className="size-5 shrink-0" aria-hidden="true" />
              </Button>
            </DrawerClose>
            {children}
          </div>
        </DrawerContent>
      </Drawer>
    ),
    [openMobile, children, setOpenMobile],
  );
}

Sidebar.displayName = "Sidebar";

const SidebarTrigger = React.forwardRef<React.ComponentRef<"button">, React.ComponentPropsWithRef<"button">>(
  ({ className, onClick, ...props }, ref) => {
    const { toggleSidebar } = useSidebar();

    return (
      <button
        ref={ref}
        data-sidebar="trigger"
        className={cx(
          "group inline-flex rounded-md p-1.5 hover:bg-gray-200/50 dark:hover:bg-gray-900",
          focusRing,
          className,
        )}
        onClick={(event) => {
          onClick?.(event);
          toggleSidebar();
        }}
        {...props}
      >
        <PanelRight className="size-[18px] shrink-0 text-gray-700 dark:text-gray-300" aria-hidden="true" />
        <span className="sr-only">Toggle Sidebar</span>
      </button>
    );
  },
);
SidebarTrigger.displayName = "SidebarTrigger";

const SidebarFooter = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(({ className, ...props }, ref) => {
  return <div ref={ref} data-sidebar="footer" className={cx("flex flex-col gap-2 p-3", className)} {...props} />;
});
SidebarFooter.displayName = "SidebarFooter";

const SidebarContent = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="content"
      className={cx("flex min-h-0 flex-1 flex-col gap-2 overflow-hidden", className)}
      {...props}
    />
  );
});
SidebarContent.displayName = "SidebarContent";

const SidebarHeader = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="header"
      className={cx(
        "flex h-16 w-full flex-col items-start justify-center overflow-clip text-gray-900 dark:text-gray-50",
        className,
      )}
      {...props}
    />
  );
});
SidebarHeader.displayName = "SidebarHeader";

const SidebarLink = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<"a"> & {
    children: React.ReactNode;
    icon?: React.JSX.Element;
    isActive?: boolean;
    notifications?: number | boolean;
  }
>(({ children, isActive, icon, notifications, className, ...props }, ref) => {
  return (
    <Link
      ref={ref}
      href={props.href ?? "#"}
      aria-current={isActive ? "page" : undefined}
      data-active={isActive}
      className={cx(
        "flex items-center justify-between rounded-md p-2 text-base transition hover:bg-gray-200/50 sm:text-sm dark:hover:bg-gray-900",
        "text-gray-900 dark:text-gray-400 dark:hover:text-gray-50",
        "data-[active=true]:text-blue-600 dark:data-[active=true]:text-blue-500",
        focusRing,
        className,
      )}
      {...props}
    >
      <span className="flex items-center gap-x-2.5">
        {icon && (
          <div className="[&_svg]:size-[18px] [&_svg]:shrink-0" aria-hidden="true">
            {icon}
          </div>
        )}
        {children}
      </span>
      {notifications && (
        <span className="inline-flex h-5 min-w-5 items-center justify-center rounded-xs bg-blue-100 px-1 text-sm font-medium text-blue-600 sm:text-xs dark:bg-blue-500/10 dark:text-blue-500">
          {notifications }
        </span>
      )}
    </Link>
  );
});
SidebarLink.displayName = "SidebarLink";

const SidebarGroup = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="group"
      className={cx("relative flex w-full min-w-0 flex-col p-3", className)}
      {...props}
    />
  );
});
SidebarGroup.displayName = "SidebarGroup";

const SidebarGroupContent = React.forwardRef<HTMLDivElement, React.ComponentProps<"div">>(
  ({ className, ...props }, ref) => (
    <div ref={ref} data-sidebar="group-content" className={cx("w-full text-sm", className)} {...props} />
  ),
);
SidebarGroupContent.displayName = "SidebarGroupContent";

const SidebarMenu = React.forwardRef<HTMLUListElement, React.ComponentProps<"ul">>(({ className, ...props }, ref) => (
  <ul ref={ref} data-sidebar="menu" className={cx("flex w-full min-w-0 flex-col gap-1", className)} {...props} />
));
SidebarMenu.displayName = "SidebarMenu";

const SidebarMenuItem = React.forwardRef<HTMLLIElement, React.ComponentProps<"li">>(({ ...props }, ref) => (
  <li ref={ref} {...props} />
));
SidebarMenuItem.displayName = "SidebarMenuItem";

const SidebarSubLink = React.forwardRef<
  HTMLAnchorElement,
  React.ComponentProps<"a"> & {
    children: React.ReactNode;
    isActive?: boolean;
  }
>(({ isActive, children, className, ...props }, ref) => {
  return (
    <Link
      ref={ref}
      href={props.href ?? "#"}
      aria-current={isActive ? "page" : undefined}
      data-active={isActive}
      className={cx(
        "relative flex gap-2 rounded-md py-1.5 pr-9 pl-3 text-base transition sm:text-sm",
        "text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50",
        "data-[active=true]:bg-white data-[active=true]:text-blue-600 data-[active=true]:shadow-xs data-[active=true]:ring-1 data-[active=true]:ring-gray-200 dark:data-[active=true]:bg-gray-900 dark:data-[active=true]:text-blue-500 dark:data-[active=true]:ring-gray-800",
        focusRing,
        className,
      )}
      {...props}
    >
      {isActive && (
        <div
          className="absolute top-1.5 right-4 bottom-1.5 w-px rounded-full bg-blue-500 dark:bg-blue-500"
          aria-hidden="true"
        />
      )}
      {children}
    </Link>
  );
});
SidebarSubLink.displayName = "SidebarSubLink";

const SidebarMenuSub = React.forwardRef<HTMLUListElement, React.ComponentProps<"ul">>(
  ({ className, ...props }, ref) => (
    <ul
      ref={ref}
      data-sidebar="menu-sub"
      className={cx("relative space-y-1 border-l border-transparent", className)}
      {...props}
    />
  ),
);
SidebarMenuSub.displayName = "SidebarMenuSub";

export {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarLink,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarProvider,
  SidebarSubLink,
  SidebarTrigger,
  useSidebar,
};
