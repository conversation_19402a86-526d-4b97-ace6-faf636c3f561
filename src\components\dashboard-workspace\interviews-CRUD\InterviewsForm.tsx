import React from "react";
import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Interview } from "@prisma/client";

export default function InterviewForm({
  mode,
  defaultValues,
  onAction,
}: {
  onAction?: DataFormOnActionDef<Interview>;
  mode: "create" | "update";
  defaultValues?: Partial<Interview>;
}) {
  return (
    <DataForm<Interview>
      fields={[
        {
          accessorKey: "title",
          label: "عنوان المقابلة",
          placeholder: "ادخل عنوان هذا المقابلة",
        },
        {
          accessorKey: "description",
          label: "وصف المقابلة",
          fieldType: "textarea",
          placeholder: "اكتب نبذه ملخصة عن المقابلة ...",
        },
        {
          accessorKey: "videoUrl",
          label: "رابط الفيديو",
          placeholder: "ادخل رابط الفيديو",
          description: "يجب أن يكون رابط الفيديو من اليوتيوب",
          inputConfig: { type: "url" },
        },
        {
          accessorKey: "thumbnail",
          label: "صورة مصغرة",
          fieldType: "fileUploader",
          fileUploaderConfig: { acceptedFileTypes: ["image/jpeg", "image/png", "image/webp"] },
        },

        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف المقابلة لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description: "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },

        { accessorKey: "id", label: "", fieldType: "hidden" },
      ]}
      mode={mode}
      onAction={onAction}
      defaultValues={defaultValues}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/interviews"
      cancelButtonLabel="إلغاء"
    />
  );
}
