import { verifySession } from "@/auth/dal";
import { comparePasswords, hashPassword } from "@/auth/password-hash";
import {
  ProfileForm,
  ProfileFieldItem,
  ProfileResultSubmitAction,
} from "@/components/profile-components/profile-form";

import { fromEntries } from "@/functions/utils-fonctions";
import prisma from "@/lib/prisma";
import { redirect } from "next/navigation";
import { cache } from "react";
import { z } from "zod";

const items: ProfileFieldItem[] = [
  {
    dir: "ltr",
    type: "password",
    name: "currentPassword",
    label: "كلمة المرور الحالية",
    placeholder: "كلمة المرور الحالية",
    autoComplete: "current-password",
  },
  {
    dir: "ltr",
    type: "password",
    name: "newPassword",
    label: "كلمة المرور الجديدة",
    autoComplete: "new-password",
    placeholder: "كلمة المرور الجديدة",
  },
  {
    dir: "ltr",
    type: "password",
    label: "تأكيد كلمة المرور",
    name: "confirmPassword",
    autoComplete: "new-password",
    placeholder: "تأكيد كلمة المرور الجديدة",
  },
];

// دالة الكاش للبحث عن المستخدم بواسطة البريد الألكتروني
const findUserById = cache(async (email: string) => {
  return prisma.user.findUnique({ where: { email } });
});

async function submitAction(formData: FormData): Promise<ProfileResultSubmitAction> {
  "use server";

  // التأكد من بيانات الجلسة
  const session = await verifySession();
  const callbackUrl = encodeURIComponent("/profile/account-security");
  if (!session) {
    redirect(`/auth/login?callback-url=${callbackUrl}`);
  }

  const request = fromEntries(formData) as {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  };

  const schema = z
    .object({
      currentPassword: z
        .string()
        .trim()
        .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
      newPassword: z
        .string()
        .trim()
        .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
      confirmPassword: z.string().trim(),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: "كلمتا المرور غير متطابقتين",
      path: ["confirmPassword"],
    });

  const { success, data, error } = schema.safeParse(request);

  if (!success) {
    return {
      state: { success, message: "البيانات المدخلة غير صحيحة" },
      errors: error?.flatten().fieldErrors,
    };
  }

  const findUser = await findUserById(session.email);
  if (!findUser) redirect(`/auth/login?callback-url=${callbackUrl}`);

  const verifyPassword = await comparePasswords(data.currentPassword, findUser?.password);

  if (!verifyPassword) {
    return {
      state: { success: false, message: "كلمة المرور الحالية غير صحيحة" },
      errors: { currentPassword: ["كلمة المرور الحالية غير صحيحة"] },
    };
  }

  const newPassword = await hashPassword(data.newPassword);

  await prisma.user.update({
    where: { email: session.email },
    data: { password: newPassword },
  });

  return { state: { success: true, message: "تم تعديل كلمة المرور بنجاح" } };
}

// ====================================================================================
// صفحة تعديل كلمة المرور
// ====================================================================================
export default async function AccountSecurityPage() {
  const session = await verifySession();
  if (!session) redirect("/auth/login");

  return (
    <div>
      <h1 className="text-2xl font-semibold">أمان الحساب</h1>
      <ProfileForm submitAction={submitAction} items={items} />
    </div>
  );
}
