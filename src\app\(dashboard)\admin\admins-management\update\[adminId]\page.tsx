// import AdminForm from "@/components/dashboard-workspace/admins-CRUD/AdminForm";
// import { adminSchema } from "@/components/dashboard-workspace/admins-CRUD/AdminSchema";
// import { createAdminSession } from "@/components/dashboard/components/auth/admin-session";
// import { getAdminSession } from "@/components/dashboard/components/auth/server-actions";
// import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
// import prisma from "@/lib/prisma";
// import { revalidatePath } from "next/cache";
// import { notFound } from "next/navigation";

import PageLayout from "@/components/dashboard-workspace/PageLayout";
import React from "react";

// eslint-disable-next-line
export default async function UpdateAdminPage(props: { params: Promise<{ adminId: string }> }) {
  // const { adminId } = await props.params;
  // const admin = await prisma.admin.findUnique({
  //   where: { id: adminId },
  //   omit: { password: true },
  // });

  // if (!admin) return notFound();

  return (
    <PageLayout title="تعديل المشرف">
      <div></div>
      {/* <AdminForm
        mode="update"
        defaultValues={admin}
        onAction={async ({ data: request }) => {
          "use server";
          const adminSession = await getAdminSession();

          const { success, data, error } = adminSchema.omit({ password: true }).safeParse(request);

          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const adminData = await prisma.admin.findUnique({ where: { id: data.id } });
          if (!adminData) return { success: false, message: "حدث خطأ اثناء البحث عن حسابك" };

          // لتأكد من عدم وجود مشرف بنفس عنوان البريد
          // مع التأكد من ان البريد المرسل لا يساوي البريد المخزن في قاعدة البيانات
          if (adminData.email !== data.email) {
            const isNotUnique = await prisma.admin.findUnique({
              where: { email: data.email },
            });

            if (isNotUnique) return { success: false, errors: { email: ["البريد الإلكتروني مستخدم من قبل"] } };
          }

          // انشاء المشرف
          const avatarUrl = await handleFileUpdate(adminData.avatarUrl, data.avatarUrl, "admins/");
          const newAdmin = await prisma.admin.update({
            where: { id: data.id },
            data: { ...data, avatarUrl },
            omit: { password: true },
          });

          if (adminSession?.id === data.id) {
            await createAdminSession(newAdmin);
          }

          revalidatePath("/admin/admins-management");
          return { success: true, message: "تم تعديل بيانات الحساب بنجاح" };
        }}
      /> */}
    </PageLayout>
  );
}
