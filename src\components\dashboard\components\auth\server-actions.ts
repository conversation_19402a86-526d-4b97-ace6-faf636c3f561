"use server"

import { redirect } from "next/navigation"

import prisma from "@/lib/prisma"

import {
  createAdminSession,
  deleteAdminSession,
  verifyAdminSession,
} from "./admin-session"

export async function getAdminSession() {
  const adminSession = await verifyAdminSession()

  const admin = await prisma.admin.findUnique({
    where: { id: adminSession?.id },
  })

  if (!admin || admin?.status === "disabled") {
    await deleteAdminSession()
    redirect("/dashboard-login")
  }

  await createAdminSession({
    id: admin.id,
    name: admin.name,
    email: admin.email,
    status: admin.status,
    accessiblePages: admin.accessiblePages,
    avatarUrl: admin.avatarUrl,
    role: admin.role,
    pushSubscription: admin.pushSubscription,
  })

  return adminSession
}

export async function logOutAction() {
  await deleteAdminSession()
}
