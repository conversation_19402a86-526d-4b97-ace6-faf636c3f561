-- CreateEnum
CREATE TYPE "AdminStatus" AS ENUM ('activated', 'disabled');

-- CreateEnum
CREATE TYPE "AdminRole" AS ENUM ('superadmin', 'admin');

-- CreateTable
CREATE TABLE "Admin" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "accessiblePages" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "avatarUrl" TEXT,
    "status" "AdminStatus" NOT NULL DEFAULT 'activated',
    "role" "AdminRole" NOT NULL DEFAULT 'admin',

    CONSTRAINT "Admin_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Admin_email_key" ON "Admin"("email");
