'use client';

import { useState, useEffect } from 'react';
import { subscribeUser, unsubscribeUser } from '@/app/actions';

function urlBase64ToUint8Array(base64String: string) {
  const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export default function PushNotificationManager() {
  const [isSupported, setIsSupported] = useState(false);
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);

  useEffect(() => {
    if ('serviceWorker' in navigator && 'PushManager' in window) {
      setIsSupported(true);
      registerServiceWorker();
    }
  }, []);

  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
      updateViaCache: 'none',
    });
    const sub = await registration.pushManager.getSubscription();
    setSubscription(sub);
  }

  async function subscribeToPush() {
    const registration = await navigator.serviceWorker.ready;
    const sub = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(
        process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!
      ),
    });
    setSubscription(sub);
    const serializedSub = JSON.parse(JSON.stringify(sub));
    await subscribeUser(serializedSub);
  }

  async function unsubscribeFromPush() {
    await subscription?.unsubscribe();
    setSubscription(null);
    await unsubscribeUser();
  }

  if (!isSupported) {
    return <p className="text-sm text-red-500">الإشعارات غير مدعومة في هذا المتصفح</p>;
  }

  return (
    <div className="mt-4">
      <h3 className="text-lg font-medium">إشعارات الاستشارات</h3>
      {subscription ? (
        <div className="mt-2">
          <p className="text-sm text-green-600">أنت مشترك في إشعارات الاستشارات الجديدة</p>
          <button 
            onClick={unsubscribeFromPush}
            className="mt-2 rounded-md bg-red-100 px-3 py-1 text-sm text-red-700 hover:bg-red-200"
          >
            إلغاء الاشتراك
          </button>
        </div>
      ) : (
        <div className="mt-2">
          <p className="text-sm text-gray-600">اشترك لتلقي إشعارات عند وصول استشارات جديدة</p>
          <button 
            onClick={subscribeToPush}
            className="mt-2 rounded-md bg-blue-100 px-3 py-1 text-sm text-blue-700 hover:bg-blue-200"
          >
            اشتراك
          </button>
        </div>
      )}
    </div>
  );
}