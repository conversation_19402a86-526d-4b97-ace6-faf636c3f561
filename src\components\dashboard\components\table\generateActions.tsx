import { DataTableCreateColumnsDef, DataTableOptionsDef, DataTableRowActionsDef } from "./types";

type Props<TData extends { [K: string]: any }> = {
  createColumns: DataTableCreateColumnsDef<TData>[];
  rowActions?: DataTableRowActionsDef<TData>;
};

export function generateActions<TData extends { [K: string]: any }>(props: Props<TData>) {
  const { createColumns, rowActions } = props;
  const { options } = rowActions ?? {};

  // الاجراءات النهائية
  const actions: DataTableRowActionsDef<TData> = {
    options: generateOptions({ createColumns, options }),
    ...rowActions,
  };

  return actions;
}

/**
 * نمرر لهذه الدالة بيانات الأعمدة وبيانات إجراءات الخيارات ثم تستخرج الدالة بيانات فلاتر الخيارات التي تم السماح لها بتنفيذ الإجراءات من كل الأعمدة
 */
type GenerateOptionsProps<TData extends { [K: string]: any }> = {
  createColumns: DataTableCreateColumnsDef<TData>[];
  options?: DataTableRowActionsDef<TData>["options"];
};
function generateOptions<TData extends { [K: string]: any }>(props: GenerateOptionsProps<TData>) {
  const extractOptions = props.createColumns
    .map(({ accessorKey, columnLabel, valueOptions }) => {
      const { options, enableActionChange } = valueOptions ?? {};

      if (!enableActionChange || !options) return null;

      return { columnKey: accessorKey as string, label: columnLabel as string, options };
    })
    .filter((option): option is DataTableOptionsDef<TData> => option !== null);

  const options = [...extractOptions, ...(props.options ?? [])];
  return options.length ? options : undefined;
}
