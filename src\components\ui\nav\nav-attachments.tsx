"use client";

import { useScrollThreshold } from "@/hooks/useScrollThreshold";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

export function NavContainer({ children }: { children: ReactNode }) {
  const isScroll = useScrollThreshold(70);

  return (
    <header className="fixed top-0 right-0 left-0 z-40 flex max-h-min items-center justify-center lg:group-data-[scroll-locked=1]/body:right-[10px]">
      <nav
        className={cn(
          "flex w-full max-w-(--breakpoint-2xl) items-center justify-between rounded-b-lg border-border/0 transition-all duration-500",
          isScroll &&
            "border-border/10 bg-background/90 shadow-[0px_2px_10px_-1px_#00000020] backdrop-blur-md backdrop-brightness-125",
        )}
      >
        {children}
      </nav>
    </header>
  );
}

export function NavItemContainer({ itemPath, children }: { children: ReactNode; itemPath: string }) {
  const currentPath = usePathname();

  const isEqualPath =
    currentPath === "/"
      ? currentPath === itemPath
      : currentPath.startsWith(
          `/${itemPath
            .split("/")
            .filter((e) => e !== "")
            .at(0)}`,
        ) && itemPath !== "/";

  return (
    <div
      data-path={isEqualPath}
      className={cn(
        "group peer relative max-w-fit bg-secondary bg-clip-text text-nowrap text-transparent hover:bg-primary-gradient-x",
        isEqualPath && "bg-primary-gradient-x drop-shadow-md",
      )}
    >
      {children}
      <div
        className={cn(
          "pointer-events-none absolute -bottom-1.5 hidden h-0.5 w-0 rounded-full bg-primary-gradient-x transition-all duration-200 group-hover:w-full group-hover:opacity-100 md:block",
          isEqualPath ? "w-full opacity-100" : "opacity-30",
        )}
      />
    </div>
  );
}

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";
import useSession from "@/hooks/useSession";
import { LogIn, LogOut, Settings, User, UserPlus2 } from "lucide-react";
import { deleteSessionAction } from "@/functions/server-actions";
import { toast } from "sonner";
import Link from "next/link";
import { Skeleton } from "../skeleton";

export function UserAvatar() {
  const session = useSession();

  return (
    <div>
      <DropdownMenu dir="rtl">
        <DropdownMenuTrigger disabled={session === null} className="ml-3 rounded-full md:ml-6">
          {session === null ? <Skeleton className="size-10 rounded-full" /> : <Avatar name={session?.name} />}
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {session ? (
            <>
              <DropdownMenuLabel className="truncate">{session.name}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile/profile-data">
                  الإعدادات
                  <DropdownMenuShortcut>
                    <Settings />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:bg-destructive/10 focus:text-destructive"
                onClick={async () => {
                  const logOut = await deleteSessionAction();

                  if (!logOut.success) {
                    toast.error(logOut.message);
                    return;
                  }

                  toast.success(logOut.message);
                  window.location.reload();
                }}
              >
                تسجيل الخروج
                <DropdownMenuShortcut>
                  <LogOut />
                </DropdownMenuShortcut>
              </DropdownMenuItem>
            </>
          ) : (
            <>
              <DropdownMenuItem asChild>
                <Link href="/auth/login">
                  تسجيل الدخول
                  <DropdownMenuShortcut>
                    <LogIn />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/auth/signup">
                  إنشاء حساب
                  <DropdownMenuShortcut>
                    <UserPlus2 />
                  </DropdownMenuShortcut>
                </Link>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

function Avatar({ name }: { name?: string }) {
  return (
    <div>
      <div className="relative flex size-10 shrink-0 items-center justify-center rounded-full bg-secondary stroke-none">
        <span className="sr-only">الحساب الشخصي</span>
        {!name ? (
          <User className="size-5 text-background" />
        ) : (
          <>
            <span className="pb-1 text-sm font-bold text-background">{name?.at(0)}</span>
            <span className="absolute bottom-0 left-0 flex size-3">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-green-400 opacity-75"></span>
              <span className="relative inline-flex size-3 rounded-full bg-green-500"></span>
            </span>
          </>
        )}
      </div>
    </div>
  );
}
