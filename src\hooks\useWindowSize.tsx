import { useState, useEffect } from "react";

export type WindowSizeProps = "xs" | "sm" | "md" | "lg" | undefined;

export default function useWindowSize(currentValue?: WindowSizeProps): {
  xs: boolean;
  sm: boolean;
  md: boolean;
  lg: boolean;
} {
  const [size, setSize] = useState<WindowSizeProps>(currentValue);

  function getSize(width: number): "xs" | "sm" | "md" | "lg" {
    if (width > 1024) return "lg";
    if (width > 768) return "md";
    if (width > 640) return "sm";
    return "xs"; // أقل من 640px
  }

  useEffect(() => {
    const handleResize = () => {
      const newSize = getSize(window.innerWidth);
      if (newSize !== size) {
        setSize(newSize);
        document.cookie = `window-size=${newSize}; path=/admin/dashboard`;
      }
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [size]);

  return {
    xs: size === "xs" || size === "sm" || size === "md" || size === "lg",
    sm: size === "sm" || size === "md" || size === "lg",
    md: size === "md" || size === "lg",
    lg: size === "lg",
  };
}
