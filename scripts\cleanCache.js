// Script to delete the .next/cache directory 
// before building
import fs from "fs";
import path from "path";
import { execSync } from "child_process";

// Path to the .next/cache directory
const cachePath = path.join(".next", "cache");

console.log("🧹 Cleaning Next.js cache...");

try {
  // Check if the directory exists
  if (fs.existsSync(cachePath)) {
    if (process.platform === "win32") {
      // On Windows, use rmdir /s /q
      execSync(`rmdir /s /q "${cachePath}"`, { stdio: "inherit" });
    } else {
      // On Unix-like systems, use rm -rf
      execSync(`rm -rf "${cachePath}"`, { stdio: "inherit" });
    }
    console.log("✅ Cache directory deleted successfully!");
  } else {
    console.log(`❌ Cache directory not found at ${cachePath}`);
  }
} catch (error) {
  console.error("❌ Error deleting cache directory:", error.message);
  process.exit(1);
}
