import { Button } from "../ui/Button";
import { cx } from "../../lib/utils-tremor";
import { Table } from "@tanstack/react-table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/Select";
import {  ChevronLeft, ChevronRight, ChevronsLeftIcon, ChevronsRightIcon } from "lucide-react";

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
}

export function DataTablePagination<TData>({ table }: DataTablePaginationProps<TData>) {
  const paginationButtons = [
    {
      icon: ChevronsRightIcon,
      onClick: () => table.setPageIndex(0),
      disabled: !table.getCanPreviousPage(),
      srText: "First page",
      mobileView: "hidden sm:block",
    },
    {
      icon: ChevronRight,
      onClick: () => table.previousPage(),
      disabled: !table.getCanPreviousPage(),
      srText: "Previous page",
      mobileView: "",
    },
    {
      icon: ChevronLeft,
      onClick: () => table.nextPage(),
      disabled: !table.getCanNextPage(),
      srText: "Next page",
      mobileView: "",
    },
    {
      icon: ChevronsLeftIcon,
      onClick: () => table.setPageIndex(table.getPageCount() - 1),
      disabled: !table.getCanNextPage(),
      srText: "Last page",
      mobileView: "hidden sm:block",
    },
  ];

  const { pagination } = table.getState();

  const currentPage = pagination.pageIndex;
  const pageCount = table.getPageCount();

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-gray-500 tabular-nums">
        <Select
          dir="rtl"
          value={`${pagination.pageSize}`}
          onValueChange={(value) => {
            table.setPageSize(Number(value));
          }}
        >
          <SelectTrigger className="w-16 sm:h-[30px]">
            <SelectValue placeholder={pagination.pageSize} />
          </SelectTrigger>
          <SelectContent className="w-28" side="top" align="start">
            {[5, 10, 20, 30, 40, 50].map((pageSize) => (
              <SelectItem className="py-1.5" key={pageSize} value={`${pageSize}`}>
                {pageSize}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center gap-x-6 lg:gap-x-8">
        <p className="hidden gap-x-1.5 text-sm text-gray-500 tabular-nums sm:flex">
          الصفحة{" "}
          <span className="font-medium text-gray-900 dark:text-gray-50">{currentPage + 1}</span> من{" "}
          <span className="font-medium text-gray-900 dark:text-gray-50">{pageCount}</span>
        </p>
        <div className="flex items-center gap-x-1.5">
          {paginationButtons.map((button, index) => (
            <Button
              key={index}
              variant="secondary"
              className={cx(button.mobileView, "p-2.5 sm:p-1.5")}
              onClick={() => {
                button.onClick();
                table.resetRowSelection();
              }}
              disabled={button.disabled}
            >
              <span className="sr-only">{button.srText}</span>
              <button.icon aria-hidden="true" />
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
