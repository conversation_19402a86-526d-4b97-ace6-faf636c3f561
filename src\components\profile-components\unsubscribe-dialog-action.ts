import "server-only";

import { verifySession } from "@/auth/dal";
import { comparePasswords } from "@/auth/password-hash";
import { fromEntries } from "@/functions/utils-fonctions";
import { redirect } from "next/navigation";
import { z } from "zod";
import { ResultUnsubscribeAction } from "./unsubscribe-dialog";
import prisma from "@/lib/prisma";
import { createSession } from "@/auth/session";

type props = {
  orderId: string;
  formData: FormData;
  unsubscribeType: "courseOrders" | "bookOrders";
  unsubscribeId: string;
  userPassword: string;
};

export async function unsubscribeFunction({
  orderId,
  formData,
  unsubscribeId,
  unsubscribeType,
  userPassword,
}: props): Promise<ResultUnsubscribeAction> {
  // التأكد من بيانات الجلسة
  const session = await verifySession();
  if (!session) {
    const callbackUrl = encodeURIComponent("/profile/cours-subscriptions");
    redirect(`/auth/login?callback-url=${callbackUrl}`);
  }

  const request = fromEntries(formData);
  const schema = z.object({
    password: z.string().trim().min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
    unsubscribeType: z.union([z.literal("courseOrders"), z.literal("bookOrders")]),
    unsubscribeId: z.string(),
  });

  const { success, data, error } = schema.safeParse({
    password: request.password,
    unsubscribeType,
    unsubscribeId,
  });

  if (!success) {
    return {
      state: { success: false, message: "خطأ اثناء معالجة البيانات" },
      errors: { password: error.flatten().fieldErrors.password },
    };
  }

  const verifyPassword = await comparePasswords(data.password, userPassword);

  if (!verifyPassword) {
    return {
      errors: { password: ["كلمة المرور غير صحيحة"] },
      state: { success: false, message: "كلمة المرور غير صحيحة" },
    };
  }

  // const orderId =
  //   data.unsubscribeType === "courseOrders"
  //     ? user?.courseOrders.find((e) => e.courseId === data.unsubscribeId)?.id
  //     : user?.bookOrders.find((e) => e.bookId === data.unsubscribeId)?.id;

  if (!orderId) {
    return {
      state: {
        success: false,
        message: `انت غير مشترك في هذا ${data.unsubscribeType === "courseOrders" ? "الكورس" : "الكتاب"}`,
      },
    };
  }

  await prisma.user.update({
    where: { id: session.id },
    data: { [data.unsubscribeType]: { disconnect: { id: orderId } } },
  });

  await createSession({
    ...session,
    purchasesIds: [...session.purchasesIds.filter((e) => e !== data.unsubscribeId)],
  });

  return { state: { success: true, message: "تم إلغاء الاشتراك بنجاح" } };
}
