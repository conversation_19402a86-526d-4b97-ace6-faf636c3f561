"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Loader2, LogIn, OctagonAlert } from "lucide-react";
import { loginAction } from "./login-action";
import { FormEvent, useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

import authImage from "@/../public/auth.jpg";
import Image from "next/image";
import { toast } from "sonner";
import Link from "next/link";
import InputField from "@/components/ui/input-field";

type Errors = { email?: string[]; password?: string[] };

export function LoginForm({ className }: { className?: string }) {
  const [isPending, startTransition] = useTransition();
  const [errors, setErrors] = useState<Errors | undefined>();

  function handleSubmit(event: FormEvent<HTMLFormElement>) {
    startTransition(async () => {
      event.preventDefault();
      const formData = new FormData(event.currentTarget);

      const login = await loginAction(formData);
      if (login.state.success) {
        toast.success(login.state.message);
        window.location.reload();
        return;
      }
      setErrors(login.errors);
      toast.error(login.state.message);
    });
  }

  return (
    <div
      dir="rtl"
      className={cn(
        "flex w-full max-w-sm flex-col gap-6 md:max-w-3xl [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary",
        className,
      )}
    >
      <Card className="overflow-hidden">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-6 md:p-8" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">تسجيل الدخول</h1>
                <p className="mt-2 max-w-80 px-10 text-sm text-muted">
                  قم بتسجيل الدخول الى حسابك الخاص او قم بـ <Link href="/auth/signup">إنشاء حساب جديد</Link>
                </p>
              </div>
              <InputField
                required
                dir="ltr"
                name="email"
                type="email"
                label="البريد الالكتروني"
                placeholder="<EMAIL>"
                errorMessage={errors?.email?.at(0)}
                disabled={isPending}
              />
              <div className="grid gap-2">
                <div className="flex items-center">
                  <label htmlFor="password" className="text-sm">
                    كلمة المرور
                  </label>
                  <a href="#" className="mr-auto text-xs underline-offset-2 hover:underline">
                    نسيت كلمة المرور؟
                  </a>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  dir="ltr"
                  className="text-right"
                  required
                  disabled={isPending}
                />
                {errors?.password && (
                  <div className="flex items-center gap-2 text-xs text-destructive">
                    <OctagonAlert className="size-4" />
                    <p>{errors.password[0]}</p>
                  </div>
                )}
              </div>
              <Button type="submit" className="w-full" disabled={isPending}>
                تسجيل الدخول
                {isPending ? <Loader2 className="animate-spin" /> : <LogIn />}
              </Button>
              <div className="max-w-80 text-center text-xs text-balance text-muted">
                بالنقر على متابعة فإنك توافق على <a href="#">شروط الخدمة</a> و <a href="#">سياسة الخصوصية</a> الخاصة
                بنا.
              </div>
            </div>
          </form>
          <div className="relative hidden bg-muted md:block">
            <Image
              src={authImage}
              placeholder="blur"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
