-- DropFore<PERSON>Key
ALTER TABLE "BookOrder" DROP CONSTRAINT "BookOrder_userId_fkey";

-- DropForeignKey
ALTER TABLE "CourseOrder" DROP CONSTRAINT "CourseOrder_userId_fkey";

-- AlterTable
ALTER TABLE "BookOrder" ALTER COLUMN "userId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "CourseOrder" ALTER COLUMN "userId" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "CourseOrder" ADD CONSTRAINT "CourseOrder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BookOrder" ADD CONSTRAINT "BookOrder_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
