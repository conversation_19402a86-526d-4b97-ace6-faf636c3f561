## تنزيل المكتبات والتبعيات

```bash
npm i jose bcryptjs server-only zod fuse.js next-themes tailwind-merge tailwind-variants lucide-react clsx @radix-ui/react-slot @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-label @radix-ui/react-select prettier-plugin-tailwindcss@latest prettier @radix-ui/react-checkbox @tanstack/react-table @tanstack/match-sorter-utils @atlaskit/pragmatic-drag-and-drop @atlaskit/pragmatic-drag-and-drop-flourish @atlaskit/pragmatic-drag-and-drop-hitbox @atlaskit/pragmatic-drag-and-drop-live-region @atlaskit/pragmatic-drag-and-drop-react-drop-indicator tiny-invariant @radix-ui/react-popover @radix-ui/react-switch use-debounce cmdk react-filepond filepond filepond-plugin-file-validate-type
```

## تبعيات لميزة تغيير ترتيب أعمدة الجدول

```bash
npm i --legacy-peer-deps
```

## أضافة متغيرات البيئة

```.env
DASHBOARD_SESSION_SECRET="استبدل هذا النص برمز الحماية"
NODE_ENV=development
```