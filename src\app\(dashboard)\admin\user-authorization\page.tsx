import PageLayout from "@/components/dashboard-workspace/PageLayout";
import UserAuthorizationForm from "@/components/dashboard-workspace/user-authorization/UserAuthorizationForm";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export default function UserAuthorizationPage() {
  return (
    <PageLayout title="اهداء كورس مدفوع للمستخدم" description="تنبية: تم إنشاء هذه الصفحة بشكل مؤقت">
      <UserAuthorizationForm
        onSearch={async ({ data }) => {
          "use server";

          const { searchTerm } = data;
          if (!searchTerm || searchTerm.trim() === "") {
            return {
              success: false,
              message: "الرجاء إدخال اسم المستخدم أو البريد الإلكتروني للبحث",
            };
          }

          // البحث عن المستخدم بالاسم أو البريد الإلكتروني
          const users = await prisma.user.findMany({
            where: {
              OR: [{ name: { contains: searchTerm } }, { email: { contains: searchTerm } }],
            },
            include: {
              courseOrders: {
                include: {
                  course: true,
                },
              },
            },
          });

          if (users.length === 0) {
            return {
              success: false,
              message: "لم يتم العثور على أي مستخدم بهذا الاسم أو البريد الإلكتروني",
            };
          }

          return {
            success: true,
            data: users,
            message: `تم العثور على ${users.length} مستخدم`,
          };
        }}
        onAuthorize={async ({ data }) => {
          "use server";

          const { userId, courseId } = data;

          if (!userId || !courseId) {
            return {
              success: false,
              message: "يرجى تحديد المستخدم والكورس",
            };
          }

          // التحقق من وجود المستخدم
          const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
              courseOrders: {
                where: { courseId: courseId },
              },
            },
          });

          if (!user) {
            return {
              success: false,
              message: "لم يتم العثور على المستخدم",
            };
          }

          // التحقق من وجود الكورس
          const course = await prisma.course.findUnique({
            where: { id: courseId },
          });

          if (!course) {
            return {
              success: false,
              message: "لم يتم العثور على الكورس",
            };
          }

          // التحقق مما إذا كان المستخدم مصرح له بالفعل
          if (user.courseOrders.length > 0) {
            return {
              success: false,
              message: "المستخدم مصرح له بالفعل بالوصول إلى هذا الكورس",
            };
          }

          // إنشاء طلب كورس جديد بحالة مدفوع
          await prisma.courseOrder.create({
            data: {
              status: "PAID",
              price: course.price,
              course: { connect: { id: courseId } },
              user: { connect: { id: userId } },
            },
          });

          revalidatePath("/admin/user-authorization");

          return {
            success: true,
            message: "تم تصريح المستخدم بنجاح للوصول إلى الكورس",
          };
        }}
      />
    </PageLayout>
  );
}
