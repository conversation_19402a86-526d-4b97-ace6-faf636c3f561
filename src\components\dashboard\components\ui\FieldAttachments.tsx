import { cx } from "../../lib/utils-tremor";
import { Label } from "./Label";

// هذه المكونات هي ملحقات للحقول وتم فصلها في ملف منفصل لغرض تسهيل التعديل
export default function FieldAttachments({
  description,
  errorMessage,
  warningMessage,
  children,
  required = true,
  className,
  htmlFor,
  label,
}: {
  description?: string;
  errorMessage?: string[];
  warningMessage?: string[];
  children: React.ReactNode;
  className?: string;
  required?: boolean;
  htmlFor: string;
  label: string;
}) {
  return (
    <div className="@container w-full">
      <div
        className={cx(
          "grid w-full grid-cols-1 justify-items-start gap-3.5 @min-3xl:grid-cols-4 @min-3xl:items-start",
          className,
        )}
      >
        <FieldLabel htmlFor={htmlFor} label={label} required={required} />

        <div className="w-full @min-3xl:col-span-3">
          {children}
          <FieldDescription description={description} />
          <FieldError errorMessage={errorMessage} warningMessage={warningMessage} />
        </div>
      </div>
    </div>
  );
}

function FieldError({
  errorMessage,
  warningMessage,
}: {
  errorMessage?: string[];
  warningMessage?: string[];
}) {
  return (
    <>
      <div
        className={cx(
          "my-0 h-0 overflow-clip opacity-0 transition-all duration-200",
          !!errorMessage && "my-2 h-4 min-h-fit opacity-100",
        )}
      >
        <p className="text-xs text-red-500 dark:text-red-600">{errorMessage?.[0]}</p>
      </div>
      <div
        className={cx(
          "my-0 h-0 overflow-clip opacity-0 transition-all duration-200",
          !!warningMessage && !errorMessage && "my-2 h-4 min-h-fit opacity-100",
        )}
      >
        <p className="text-xs text-orange-500 dark:text-orange-500">{warningMessage?.[0]}</p>
      </div>
    </>
  );
}

function FieldLabel({
  required,
  htmlFor,
  label,
}: {
  required: boolean;
  htmlFor: string;
  label: string;
}) {
  return (
    <Label
      htmlFor={htmlFor}
      className="font-medium @min-3xl:flex @min-3xl:h-[38px] @min-3xl:items-center"
    >
      {label}
      {required && <span className="text-red-500 dark:text-red-600">*</span>}
    </Label>
  );
}

function FieldDescription({ description }: { description?: string }) {
  if (!description) return null;
  return <p className="text-sm mt-1.5 text-gray-500">{description}</p>;
}
