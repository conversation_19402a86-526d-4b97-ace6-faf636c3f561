/*
  Warnings:

  - You are about to drop the column `videoId` on the `Interview` table. All the data in the column will be lost.
  - You are about to drop the column `content` on the `Lecture` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Interview" DROP COLUMN "videoId",
ADD COLUMN     "videoUrl" TEXT NOT NULL DEFAULT 'https://www.youtube.com/embed/HTMDNZOlUq4?si=xck9Tafeb8in9zgy';

-- AlterTable
ALTER TABLE "Lecture" DROP COLUMN "content";

-- CreateIndex
CREATE INDEX "Article_createdAt_idx" ON "Article"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "BlogPost_createdAt_idx" ON "BlogPost"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "Book_createdAt_idx" ON "Book"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "BookOrder_userId_idx" ON "BookOrder"("userId");

-- CreateIndex
CREATE INDEX "Comment_entity_entityId_createdAt_idx" ON "Comment"("entity", "entityId", "createdAt" DESC);

-- CreateIndex
CREATE INDEX "Course_createdAt_idx" ON "Course"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "CourseOrder_userId_idx" ON "CourseOrder"("userId");

-- CreateIndex
CREATE INDEX "Interview_createdAt_idx" ON "Interview"("createdAt" DESC);

-- CreateIndex
CREATE INDEX "Lecture_courseId_createdAt_idx" ON "Lecture"("courseId", "createdAt" DESC);
