"use client";
import { ChevronLeft } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Fragment } from "react";
import { SidebarItemsType } from "../LayoutTypes";

type Props = { sidebarItems: SidebarItemsType };

export function Breadcrumbs({ sidebarItems }: Props) {
  const pathname = usePathname();
  const breadcrumbsItems = findMatchingItems(pathname, sidebarItems);

  return (
    <>
      <nav aria-label="Breadcrumb" className="mr-3">
        <ol role="list" className="flex items-center gap-3 text-sm">
          {breadcrumbsItems.map((item, index, allItems) => {
            const isEnd = index === allItems.length - 1;

            if (isEnd)
              return (
                <li className="flex" key={item.href}>
                  <div className="flex items-center">
                    <span className="text-gray-900 dark:text-gray-50">
                      {item.label}
                    </span>
                  </div>
                </li>
              );

            return (
              <Fragment key={item.href}>
                <li className="flex">
                  <Link
                    href={item.href}
                    className="text-gray-500 transition hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  >
                    {item.label}
                  </Link>
                </li>
                <ChevronLeft
                  className="size-4 shrink-0 text-gray-600 dark:text-gray-400"
                  aria-hidden="true"
                />
              </Fragment>
            );
          })}
        </ol>
      </nav>
    </>
  );
}

function generatePathArray(path: string): string[] {
  return path
    .split("/")
    .filter(Boolean) // لإزالة العناصر الفارغة الناتجة عن أول "/"
    .reduce<string[]>((acc, segment, index, array) => {
      const fullPath = "/" + array.slice(0, index + 1).join("/");
      acc.push(fullPath);
      return acc;
    }, []);
}

function findMatchingItems(
  path: string,
  sidebar: SidebarItemsType,
): { label: string; href: string }[] {
  const paths = generatePathArray(path);
  const matchedItems: { label: string; href: string }[] = [];

  // البحث في navigation
  sidebar.navigation.forEach((item) => {
    if (paths.includes(item.href)) {
      matchedItems.push({ label: item.label, href: item.href });
    }
  });

  // البحث في navigation2
  sidebar.navigation2.forEach((navItem) => {
    navItem.children.forEach((child) => {
      if (paths.includes(child.href)) {
        matchedItems.push({ label: child.label, href: child.href });
      }
    });
  });

  return matchedItems;
}
