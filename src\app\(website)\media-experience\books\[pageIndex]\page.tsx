import Link from "next/link";
import Image from "next/image";
import FormatTime from "@/components/ui/format-time";

import { BookOpenText, CalendarDays } from "lucide-react";
import { Pagination } from "@/components/ui/pagination";
import { getBooks } from "@/utils/get-data-from-db";
import { notFound } from "next/navigation";
import { Metadata } from "next";
import { Book } from "@prisma/client";

type BooksPageProps = { params: Promise<{ pageIndex: string }> };

// ==================================================================================
// صفحة تعرض جميع الكتب
// ==================================================================================
export async function generateMetadata(props: BooksPageProps): Promise<Metadata> {
  const pageIndex = Number((await props.params).pageIndex);

  const { data, pagination } = await getBooks({ pageIndex });
  const { currentPage, totalPages } = pagination;
  const titleBooks = data.flatMap((book) => book.title);

  // العلامات الوصفية للترقيم الصفحي
  const isPrevious = currentPage > 1;
  const isNext = totalPages > currentPage;
  const next = isNext ? `/media-experience/books/${currentPage + 1}` : undefined;
  const previous = isPrevious ? `/media-experience/books/${currentPage - 1}` : undefined;

  return {
    title: `كتب د. ناهد باشطح [${pageIndex}]`,
    description: titleBooks.join(" || "),
    pagination: { previous, next },
    keywords: titleBooks,
  };
}

export default async function BooksPage(props: BooksPageProps) {
  const pageIndex = Number((await props.params).pageIndex);

  if (!pageIndex) return notFound();

  const { data, pagination } = await getBooks({ pageIndex });
  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>;

  return (
    <>
      {data?.map((book) => <BookCard key={book.id} book={book} pageIndex={pageIndex} />)}
      <Pagination url="/media-experience/books" {...pagination} />
    </>
  );
}

// ==================================================================================
// مكون بطاقة الكتاب
// ==================================================================================
function BookCard({ pageIndex, book }: { pageIndex: number; book: Book }) {
  return (
    <div className="w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="flex gap-3 px-4 pt-3 md:pt-5">
        <div className="relative  aspect-[1/1.5] w-16 shrink-0 overflow-clip rounded-md border border-muted/0 bg-muted sm:w-20">
          <Image
            sizes="(max-width: 768px) 30vw, 7vw"
            src={book.coverImage}
            className="h-auto w-full object-cover"
            alt={book.title}
            fill
          />
        </div>
        <div className="space-y-2 md:space-y-3">
          <Link
            href={`/media-experience/books/${pageIndex}/${book.id}`}
            className="text-lg font-bold text-primary underline hover:text-primary/90"
          >
            {book.title}
          </Link>
          <p className="line-clamp-3 text-sm">{book.summary}</p>
        </div>
      </div>
      <div className="flex w-full gap-2 rounded-sm bg-muted/10 px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-nowrap text-xs">
          <CalendarDays className="size-4" />{" "}
          <FormatTime className="w-full max-w-32 truncate" dateInput={book.createdAt} />
        </span>
        <div className="min-h-max w-px bg-muted/30" />
        <span className="flex flex-nowrap items-center gap-2 text-nowrap text-xs">
          <BookOpenText className="size-4" />{" "}
          <span className="w-full max-w-32 truncate">{book.pagesCount} صفحة</span>
        </span>
      </div>
    </div>
  );
}
