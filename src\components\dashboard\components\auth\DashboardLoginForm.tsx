"use client";

import { LogIn } from "lucide-react";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { useState, useTransition } from "react";
import { Badge } from "../ui/Badge";
import FieldAttachments from "../ui/FieldAttachments";
import { useRouter } from "next/navigation";

type LoginActionResult = {
  success: boolean;
  errors?: { password?: string[]; email?: string[] };
  message?: string;
};

export type LoginActionType = (formData: FormData) => Promise<LoginActionResult>;

type Props = {
  loginAction: LoginActionType;
};

export default function AdminLoginForm({ loginAction }: Props) {
  const [isPending, startTransition] = useTransition();
  const [state, setState] = useState<LoginActionResult | undefined>(undefined);
  const router = useRouter();

  return (
    <form
      onSubmit={(e) => {
        startTransition(async () => {
          e.preventDefault();
          const formData = new FormData(e.currentTarget);
          const action = await loginAction(formData);
          startTransition(() => {
            if (action.success) {
              router.replace("/admin");
              return;
            }
            setState(action);
          });
        });
      }}
      className="mt-6 space-y-5"
    >
      <FieldAttachments label="البريد الإلكتروني" htmlFor="email" errorMessage={state?.errors?.email}>
        <Input dir="ltr" id="email" name="email" type="email" autoComplete="email" placeholder="<EMAIL>" />
      </FieldAttachments>
      <FieldAttachments label="كلمة المرور" htmlFor="password" errorMessage={state?.errors?.password}>
        <Input dir="ltr" id="password" name="password" type="password" placeholder="Password" autoComplete="password" />
      </FieldAttachments>
      {state?.message && <Badge variant="error">{state?.message}</Badge>}
      <Button type="submit" isLoading={isPending} className="mt-4 w-full">
        تسجيل الدخول <LogIn />
      </Button>
    </form>
  );
}
