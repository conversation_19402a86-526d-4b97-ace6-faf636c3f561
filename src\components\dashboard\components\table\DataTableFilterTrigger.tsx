import React from "react";
import { cx, focusRing } from "../../lib/utils-tremor";
import { ChevronDown, Plus } from "lucide-react";
import { Badge } from "../ui/Badge";
import { PopoverTrigger } from "../ui/Popover";

export default function DataTableFilterTrigger({
  selectedValues,
  onClearFilter,
  children,
  options,
}: {
  children: React.ReactNode;
  onClearFilter: () => void;
  options: { label: string; value: string | number }[];
  selectedValues: Set<string | number>;
}) {
  const selectedValueSize = selectedValues?.size;

  return (
    <PopoverTrigger asChild >
      <button
        className={cx(
          "flex w-full items-center gap-x-2 rounded-md border border-dashed border-gray-300 px-2 font-medium whitespace-nowrap text-gray-600 group-[state=open]:bg-gray-50 hover:bg-gray-50 sm:w-fit sm:text-xs dark:border-gray-700 dark:text-gray-400 dark:data-[state=open]:bg-gray-900 hover:dark:bg-gray-900",
          focusRing,
        )}
      >
        <span
          aria-hidden="true"
          onClick={(e) => {
            if (selectedValueSize > 0) {
              e.stopPropagation();
              onClearFilter();
            }
          }}
        >
          <Plus
            className={cx(
              "size-5 shrink-0 transition sm:size-4",
              selectedValueSize > 0 && "rotate-45 cursor-pointer hover:text-red-500",
            )}
            aria-hidden="true"
          />
        </span>
        <span className="py-1.5">{children}</span>
        {selectedValueSize > 0 && (
          <>
            <span className="block h-4 w-px bg-gray-300 dark:bg-gray-700" aria-hidden="true" />
            <div className="flex gap-1">
              {selectedValueSize > 2 ? (
                <Badge
                  variant="default"
                  className="aspect-square rounded-xs px-2 py-0.5 font-normal ring-0"
                >
                  {selectedValueSize}
                </Badge>
              ) : (
                options
                  ?.filter((e) => selectedValues.has(e.value))
                  ?.map((e) => (
                    <Badge
                      variant="default"
                      key={e.value}
                      className="rounded-xs px-1.5 py-0.5 font-normal ring-0"
                    >
                      {e.label}
                    </Badge>
                  ))
              )}
            </div>
          </>
        )}
        <ChevronDown
          className="mr-auto size-5 shrink-0 text-gray-500 sm:size-4"
          aria-hidden="true"
        />
      </button>
    </PopoverTrigger>
  );
}
