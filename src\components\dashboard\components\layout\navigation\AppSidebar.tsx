"use client";

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarLink,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarSubLink,
  useSidebar,
} from "@/components/dashboard/components/layout/navigation/Sidebar";

import { Divider } from "@/components/dashboard/components/ui/Divider";
import { Input } from "@/components/dashboard/components/ui/Input";
import { cx, focusRing } from "@/components/dashboard/lib/utils-tremor";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Navigation2ChildrenType, Navigation2Type, SidebarItemsType } from "../LayoutTypes";
import { UserProfile } from "./UserProfile";
import { ChevronDown } from "lucide-react";

type AppSidebarProps = {
  sidebarItems: SidebarItemsType;
  openMenuId?: string;
  logo: React.ReactNode;
};

export function AppSidebar({ sidebarItems, openMenuId = "", logo }: AppSidebarProps) {
  const [navigation2, setNavigation2] = useState(sidebarItems.navigation2);
  const { setOpenMobile } = useSidebar();
  const [openMenus, setOpenMenus] = useState<string[]>([openMenuId]);
  const [isSearch, setIsSearch] = useState(false);
  const { navigation } = sidebarItems;
  const pathname = usePathname();

  const toggleMenu = (id: string) => {
    setOpenMenus((prev: string[]) => (prev.includes(id) ? prev.filter((item: string) => item !== id) : [...prev, id]));
  };

  const handleOnSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = await fuseSearch({
      navigation2: sidebarItems.navigation2,
      searchValue: e.target.value,
    });

    setNavigation2(search ?? sidebarItems.navigation2);

    const groubActive = sidebarItems.navigation2.find((item) =>
      item.children.find((child) => pathname.startsWith(child.href)),
    );
    sidebarItems.navigation2?.map((item) =>
      setOpenMenus((prev) => (!e.target.value && groubActive ? [groubActive.id] : [...prev, item.id])),
    );
  };

  useEffect(() => {
    const groubActive = sidebarItems.navigation2.find((item) =>
      item.children.find((child) => pathname.startsWith(child.href)),
    );

    // if (!groubActive) {
    //   document.cookie = `sidebar:openMenuId=${""}; path=/; max-age=${60 * 60 * 24 * 7}`;
    //   return;
    // }

    // document.cookie = `sidebar:openMenuId=${groubActive.id}; path=/; max-age=${60 * 60 * 24 * 7}`;
    setOpenMenus((prev) => (groubActive && !prev.includes(groubActive?.id) ? [...prev, groubActive.id] : prev));
  }, [pathname, sidebarItems]);

  return (
    <Sidebar className="dark:bg-gray-925 bg-gray-50">
      {React.useMemo(
        () => (
          <SidebarHeader className="px-3 py-4">{logo}</SidebarHeader>
        ),
        [logo],
      )}
      <SidebarContent>
        {/* حقل البحث */}
        <SidebarGroup>
          <SidebarGroupContent>
            <Input
              onFocus={() => setIsSearch(true)}
              onBlur={() => setIsSearch(false)}
              required={false}
              id="search"
              name="search"
              type="search"
              onChange={handleOnSearch}
              placeholder="بحث في القائمة..."
              className="sm:[&>input]:py-1.5"
            />
          </SidebarGroupContent>
        </SidebarGroup>
        <div className="overflow-x-clip overflow-y-auto">
          {/* مجموعة الرئيسية والبريد */}
          {React.useMemo(
            () => (
              <>
                <SidebarGroup className="pt-0">
                  <SidebarGroupContent>
                    <SidebarMenu className="space-y-1">
                      {navigation.map((item) => (
                        <SidebarMenuItem key={item.href}>
                          <SidebarLink
                            onClick={() => setOpenMobile(false)}
                            href={item.href}
                            isActive={pathname === item.href}
                            icon={item.icon}
                            notifications={item.notifications}
                          >
                            {item.label}
                          </SidebarLink>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </SidebarGroup>
                <div className="px-3">
                  <Divider className="my-0 py-0" />
                </div>
              </>
            ),
            [navigation, pathname, setOpenMobile],
          )}
          {/* مجموعة الصفحات الاخرى */}
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-4">
                {navigation2.length ? (
                  navigation2.map((item) => {
                    const isExpanded = openMenus.includes(item.id);

                    return (
                      <SidebarMenuItem key={item.id}>
                        {/* @CHRIS/SEV: discussion whether to componentize (-> state mgmt) */}
                        <button
                          onClick={() => toggleMenu(item.id)}
                          className={cx(
                            "flex w-full items-center justify-between gap-x-2.5 rounded-md p-2 text-base text-gray-900 transition hover:bg-gray-200/50 sm:text-sm dark:text-gray-400 dark:hover:bg-gray-900 dark:hover:text-gray-50",
                            focusRing,
                          )}
                        >
                          <div className="flex items-center gap-2.5">
                            <div className="[&_svg]:size-[18px] [&_svg]:shrink-0" aria-hidden="true">
                              {item.icon}
                            </div>
                            {item.label}
                          </div>
                          <ChevronDown
                            className={cx(
                              isExpanded ? "rotate-0" : "rotate-90",
                              "size-4 shrink-0 transform text-gray-400 transition-transform duration-150 ease-in-out dark:text-gray-600",
                            )}
                            aria-hidden="true"
                          />
                        </button>
                        <ChildrenSidebarMenuSub
                          isSearch={isSearch}
                          setOpenMobile={setOpenMobile}
                          childrenItems={item.children}
                          isExpanded={isExpanded}
                          pathname={pathname}
                        />
                      </SidebarMenuItem>
                    );
                  })
                ) : (
                  <span className="block w-full text-center">لا توجد نتائج</span>
                )}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </div>
      </SidebarContent>
      <SidebarFooter>
        <div className="border-t border-gray-200 dark:border-gray-800" />
        <UserProfile />
      </SidebarFooter>
    </Sidebar>
  );
}

function ChildrenSidebarMenuSub({
  pathname,
  isSearch,
  isExpanded,
  childrenItems,
  setOpenMobile,
}: {
  isSearch: boolean;
  pathname: string;
  isExpanded: boolean;
  setOpenMobile: (open: boolean) => void;
  childrenItems: Navigation2ChildrenType[];
}) {
  const ref = React.useRef<HTMLDivElement>(null);

  return (
    <div
      style={{
        height: isExpanded && !isSearch ? ref.current?.scrollHeight : undefined,
      }}
      ref={ref}
      className={cx(
        "p-px opacity-100",
        isSearch ? "" : "overflow-hidden transition-all duration-150 will-change-transform",
        !isExpanded && !isSearch && "h-0! opacity-0",
      )}
    >
      <SidebarMenuSub>
        <div className="absolute inset-y-0 right-4 w-px bg-gray-300 dark:bg-gray-800" />
        {childrenItems?.map((child) => (
          <SidebarMenuItem className="mt-1" key={child.href}>
            <SidebarSubLink
              onClick={() => setOpenMobile(false)}
              href={child.href}
              isActive={pathname.startsWith(child.href)}
            >
              {child.label}
            </SidebarSubLink>
          </SidebarMenuItem>
        ))}
      </SidebarMenuSub>
    </div>
  );
}

const fuseSearch = async ({ navigation2, searchValue }: { navigation2: Navigation2Type[]; searchValue: string }) => {
  const Fuse = (await import("fuse.js")).default;
  if (searchValue === "" || !searchValue) return;

  return navigation2
    .map((item) => {
      const fuse = new Fuse(item.children, {
        keys: ["label"],
        includeScore: true,
        findAllMatches: true,
        threshold: 0.3,
        ignoreDiacritics: true,
      });

      const children = fuse.search(searchValue)?.map((e) => ({ ...e.item }));
      if (!children.length) return null;
      return { ...item, children };
    })
    .filter((e) => e !== null);
};
