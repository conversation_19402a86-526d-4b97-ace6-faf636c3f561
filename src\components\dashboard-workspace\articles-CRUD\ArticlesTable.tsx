"use client";
import PageLayout from "../PageLayout";
import DataTable from "@/components/dashboard/components/table/DataTable";
import { Article } from "@prisma/client";
import TableArray from "../TableArray";
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types";
import FormatTime from "@/components/ui/format-time";

export default function ArticlesTable({
  data,
  onDelete,
}: {
  data: Article[];
  onDelete?: DataTableOnDeleteFnDef<Article>;
}) {
  return (
    <PageLayout title="المقالات">
      <DataTable<Article>
        data={data}
        defaultPageSize={5}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/articles/update", label: "تعديل" }],
          },
        }}
        columnSearch={{ columnKey: "title" }}
        createDataButton={{ href: "/admin/articles/create", label: "اضافة مقال" }}
        createColumns={[
          { accessorKey: "title", columnLabel: "عنوان المقال" },
          {
            accessorKey: "article",
            columnLabel: "المحتوى",
            cell: ({ value }) => <div className="max-w-[200px] overflow-hidden text-ellipsis">{value}</div>,
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => <FormatTime dateInput={value} />,
          },

          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  );
}
