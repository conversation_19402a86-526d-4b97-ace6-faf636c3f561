import { Skeleton } from "@/components/dashboard/components/ui/Skeleton";
import React from "react";

export default function ConsultationCardSkeleton({
  ref,
  hasMore,
  length = 2,
  isLoading = false,
}: {
  isOnView: boolean;
  hasMore?: boolean;
  isLoading?: boolean;
  length?: number;
  ref?: React.RefObject<null>;
}) {
  const arr = Array.from({ length }, (_, i) => i + 1);
  return React.useMemo(
    () =>
      hasMore && (
        <div ref={ref}>
          {arr.map((i) => (
            <div key={i} className="mb-3 h-26 grow rounded-lg border p-3 **:select-none last:mb-0">
              <div>
                <div className="flex items-center justify-between gap-x-2">
                  <div className="flex w-full max-w-max py-1">
                    <Skeleton className="h-4 w-32" playAnimate={isLoading ?? false} />
                  </div>
                  <Skeleton className="h-2.5 w-12" playAnimate={isLoading ?? false} />
                </div>
                <div className="mt-1.5 py-1">
                  <Skeleton className="h-3 w-24" playAnimate={isLoading ?? false} />
                </div>
                <div className="mt-2 py-1">
                  <Skeleton className="h-3 w-full" playAnimate={isLoading ?? false} />
                </div>
              </div>
            </div>
          ))}
          {/* <div className="size-px" ref={ref}></div> */}
        </div>
      ),
    [hasMore, arr, ref, isLoading],
  );
}
