import CourseCard from "@/components/ui/course-card";
import TitleSection from "@/components/ui/title-section";

import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers";
import { getCourses } from "@/utils/get-data-from-db";
import { Suspense } from "react";
import { Metadata } from "next";

export const dynamic = "force-static";

// =======================================
// صفحة الكورسات
// =======================================
export const metadata: Metadata = {
  title: "كورسات د. ناهد باشطح",
  description:
    "اكتشف كورسات د. ناهد باشطح لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات. تقنيات فعّالة لتحقيق التوازن النفسي والجسدي.",
};

export default function CoursesPage() {
  return (
    <div className="min-h-svh pt-16">
      <div className="relative bg-secondary py-32">
        <DividerRedTop />
        <TitleSection
          title={<h1>الكــــورســـات</h1>}
          description="مجموعة دورات شاملة لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات، من خلال تقنيات فعّالة لتحقيق التوازن النفسي والجسدي."
          classTitle="text-background"
          classDescription="text-background"
        />
        <DividerRedBottom />
      </div>
      <Suspense fallback={<div>جاري التحميل ...</div>}>
        <Courses />
      </Suspense>
    </div>
  );
}

async function Courses() {
  const courses = await getCourses();

  return (
    <div className="flex flex-col gap-24 py-24">
      {courses?.map((course) => <CourseCard key={course.id} {...course} />)}
    </div>
  );
}
