import TestimonyForm from "@/components/dashboard-workspace/testimonial-CRUD/TestimonyForm";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import prisma from "@/lib/prisma";

import { testimonySchema } from "@/components/dashboard-workspace/testimonial-CRUD/TestimonySchema";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";

export default async function TestimonyUpdatePage(props: { params: Promise<{ testimonyId: string }> }) {
  const { testimonyId } = await props.params;
  const testimony = await prisma.testimony.findUnique({ where: { id: testimonyId } });
  if (!testimony) return notFound();

  return (
    <PageLayout title="تعديل الشهادة">
      <TestimonyForm
        mode="update"
        defaultValues={testimony}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = testimonySchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          await prisma.testimony.update({ where: { id: data.id }, data });
          
          revalidatePath("/admin/testimonys");
          revalidatePath("/");

          return { success: true, message: "تم تعديل الشهادة بنجاح" };
        }}
      />
    </PageLayout>
  );
}
