"use client";

import { Card, CardContent } from "@/components/ui/card";
import { FormEvent, useState, useTransition } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

import authImage from "@/../public/auth.jpg";
import Image from "next/image";
import { toast } from "sonner";
import { signupAction } from "./signup-action";
import InputField from "@/components/ui/input-field";

type ErrorsType = { name?: string[]; email?: string[]; password?: string[] };

export function SignupForm({ className }: { className?: string }) {
  const [isPending, startTransition] = useTransition();
  const [message, setMessage] = useState<string | undefined>(undefined);
  const [errors, setErrors] = useState<ErrorsType | undefined>();

  function handleSubmit(event: FormEvent<HTMLFormElement>) {
    startTransition(async () => {
      event.preventDefault();
      const formData = new FormData(event.currentTarget);

      const response = await signupAction(formData);
      if (response.state.success) {
        toast.success(response.state.message);
        return;
      }
      setErrors(response.errors);
      setMessage(response.state?.message);
    });
  }

  return (
    <div
      dir="rtl"
      className={cn(
        "flex w-full max-w-sm flex-col gap-6 md:max-w-3xl [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary",
        className,
      )}
    >
      <Card className="overflow-hidden">
        <CardContent className="grid p-0 md:grid-cols-2">
          <form className="p-6 md:p-8" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">إنشاء حساب</h1>
                <p className="mt-2 max-w-80 px-10 text-sm text-muted">
                  يسعدنا انضمامك إلينا، قم بإدخال بياناتك أدناه لإنشاء حسابك
                </p>
              </div>
              <InputField
                required
                name="name"
                type="name"
                label="الاسم"
                disabled={isPending}
                placeholder="مثال : محمد الحداد"
                errorMessage={errors?.name?.at(0)}
              />
              <InputField
                required
                dir="ltr"
                name="email"
                type="email"
                label="البريد الالكتروني"
                disabled={isPending}
                className="text-right"
                placeholder="<EMAIL>"
                errorMessage={errors?.email?.at(0)}
              />
              <InputField
                required
                dir="ltr"
                name="password"
                type="password"
                label="كلمة المرور"
                disabled={isPending}
                className="text-right"
                errorMessage={errors?.password?.at(0)}
              />
              {message && <p className="text-xs leading-relaxed text-destructive">{message}</p>}
              <Button type="submit" className="w-full" disabled={isPending}>
                انشاء الحساب
              </Button>
              <div className="max-w-80 text-center text-xs text-balance text-muted">
                بالنقر على متابعة فإنك توافق على <a href="#">شروط الخدمة</a> و <a href="#">سياسة الخصوصية</a> الخاصة
                بنا.
              </div>
            </div>
          </form>
          <div className="relative hidden bg-muted md:block">
            <Image
              src={authImage}
              placeholder="blur"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
