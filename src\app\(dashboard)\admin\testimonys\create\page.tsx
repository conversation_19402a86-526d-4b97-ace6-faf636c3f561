import prisma from "@/lib/prisma";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import TestimonyForm from "@/components/dashboard-workspace/testimonial-CRUD/TestimonyForm";

import { revalidatePath } from "next/cache";
import { testimonySchema } from "@/components/dashboard-workspace/testimonial-CRUD/TestimonySchema";

export default function CreateTestimonyPage() {
  return (
    <PageLayout title="إضافة شهادة">
      <TestimonyForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server";

          const totalTestimonys = await prisma.testimony.count();
          if (totalTestimonys >= 3) {
            return { success: false, message: "لا يمكنك إضافة أكثر من 3 شهادات" };
          }

          const { success, data, error } = testimonySchema.omit({ id: true }).safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };


          await prisma.testimony.create({ data });

          revalidatePath("/admin/testimonys");
          revalidatePath("/");

          return { success: true, message: "تم إضافة الشهادة بنجاح" };
        }}
      />
    </PageLayout>
  );
}
