"use server";

import prisma from "@/lib/prisma";
import { createSession } from "../../../../auth/session";

type VerifyEmailResult = {
  success: boolean;
  message: string;
};

export async function verifyEmailAction(token: string): Promise<VerifyEmailResult> {
  if (!token) {
    return {
      success: false,
      message: "رمز التحقق غير صالح أو مفقود.",
    };
  }

  try {
    // البحث عن المستخدم بواسطة رمز التحقق
    const user = await prisma.user.findFirst({
      where: {
        verificationToken: token,
      },
    });

    if (!user) {
      return {
        success: false,
        message: "رمز التحقق غير صالح أو منتهي الصلاحية.",
      };
    }

    // التحقق من صلاحية الرمز
    if (user.emailVerified) {
      return {
        success: true,
        message: "تم تأكيد بريدك الإلكتروني بالفعل. يمكنك الآن تسجيل الدخول.",
      };
    }

    // التحقق من تاريخ انتهاء صلاحية الرمز
    if (user.verificationTokenExpiry && user.verificationTokenExpiry < new Date()) {
      return {
        success: false,
        message: "انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد عن طريق تسجيل الدخول.",
      };
    }

    // تحديث حالة التحقق للمستخدم
    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        emailVerified: true,
        verificationToken: null,
        verificationTokenExpiry: null,
      },
    });

    // ================================================================================== //
    // انشاء توكن للمستخدم
    // ================================================================================== //
    await createSession({
      id: user.id,
      purchasesIds: [],
      name: user.name,
      email: user.email,
    });

    return {
      success: true,
      message: "تم تأكيد بريدك الإلكتروني بنجاح. يمكنك الآن تسجيل الدخول.",
    };
  } catch (error) {
    console.error("Error verifying email:", error);
    return {
      success: false,
      message: "حدث خطأ أثناء التحقق من البريد الإلكتروني. يرجى المحاولة مرة أخرى.",
    };
  }
}
