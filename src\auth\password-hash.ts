import { compare, hash } from "bcryptjs";

export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  const hashedPassword = await hash(password, saltRounds);
  return hashedPassword;
}

// ================================================================================== //
// فك تشفير كلمة المرور
// ================================================================================== //
export async function comparePasswords(
  password: string,
  hashedPassword: string,
): Promise<boolean> {
  const isMatch = await compare(password, hashedPassword);
  return isMatch;
}
