import { cn } from "@/lib/utils";

// ========================================================================
// ايقونات مواقع التواصل الخاصة بجزئية التذييل
// ========================================================================
export function FacebookIcon({ className }: { className?: string }) {
  return (
    <svg
      width={412}
      height={412}
      viewBox="0 0 412 412"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("*:fill-background", className)}
    >
      <path d="M411.1 205.444v155c0 4.4-.1 8.8-1 13.2-4.7 21.5-23.4 37-45.3 37.6H50.9c-5.7 0-11.4-.4-16.9-2.1-20.1-6-32.8-24.2-33.8-42-.1-2.2-.2-4.4-.2-6.6v-310c0-4.2.1-8.5 1-12.6 4.8-21.8 23.2-37.1 45.6-37.9 1.5-.1 2.9 0 4.4 0h308.9c5.4 0 10.6.4 15.8 1.8 21.1 5.6 35.1 24.9 35.2 44.9v6.6c.2 50.6.2 101.3.2 152.1zm-392 .2v158.9c.4 14.3 11.7 25.7 24.4 27.2 2.4.3 4.8.4 7.1.4h309.5c1.5 0 2.9 0 4.4-.1 14.1-1 24.9-10.7 27.2-24.6.4-2.7.4-5.5.4-8.2v-307.3c0-1.8 0-3.7-.1-5.5-1.1-16.9-14.8-27-27.5-27.2-1.8 0-3.7-.1-5.5-.1H51.7c-1.8 0-3.7 0-5.5.1-11.1.2-23 8.4-26.3 21.5-.9 3.5-.9 6.9-.9 10.4.1 51.5.1 103 .1 154.5z" />
      <path d="M209.3 280.345v49.1c0 1.7 0 3.3-.1 5-.1 1-.7 1.6-1.8 1.7-.5.1-1.1.1-1.6.2-11.6.7-23.2.3-34.7-1.5-1.5-.2-2.9-.6-4.3-.9-1-.2-1.6-.9-1.6-2-.1-1.7-.1-3.3-.1-5v-95.4c0-7.6.7-6.9-6.7-6.9H133c-4.5-.2-6.5-2.1-6.5-6.4-.1-10.5-.1-21 0-31.4 0-4.6 1.8-6.4 6.3-6.5 7.4-.2 14.7 0 22.1-.1 3.3-.1 6.6.8 9.8-.7.9-3.1.4-6.2.4-9.3.1-13.2 0-26.5 0-39.7 0-8.6 1.7-16.9 5.6-24.5 10.2-19.6 26.2-30.4 48.4-31.2 12.7-.4 25.4-.1 38.1-.1 1.1 0 2.2 0 3.3.2 2.2.5 3.6 1.9 3.9 4.1.2 1.1.2 2.2.2 3.3v28.7c0 6.7-1.3 7.9-8.2 8h-23.7c-3.4 0-6.6.5-9.7 1.8-9 4-13.4 11.2-13.7 20.9-.2 8.1-.1 16.2 0 24.3v11.6c0 2 .6 2.6 2.7 2.7 1.5.1 2.9.1 4.4.1h35.3c1.7 0 3.3 0 5 .1 4.9.6 7.9 4 7.9 8.9 0 .7-.1 1.5-.2 2.2-.9 7.8-1.9 15.7-2.8 23.5-.1.9-.3 1.8-.5 2.7-1 3.9-3.7 6.3-7.7 6.8-1.5.2-2.9.1-4.4.1h-34.8c-4.8 0-4.9.1-4.9 4.9-.1 16.8 0 33.8 0 50.7z" />
    </svg>
  );
}

export function InstagramIcon({ className }: { className?: string }) {
  return (
    <svg
      width={411}
      height={412}
      viewBox="0 0 411 412"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("*:fill-background", className)}
    >
      <path d="M0 205.5V49.4c0-5.4.4-10.6 2.1-15.8C8.3 14 25.9.7 46.7 0h313.301c5.7 0 11.4.4 16.9 2.1 21.3 6.4 33.9 25.7 34.1 44.1V361.8c0 4.4-.2 8.8-1.3 13.1-5.1 20.9-23.6 35.9-45.1 36.4h-313.4c-5.9 0-11.7-.4-17.4-2.1C13 403-.1 383.5 0 364.4c.1-20.6 0-41.2 0-61.8v-97.1zM205.802 19.2h-152.8c-2.2 0-4.4 0-6.6.1-12.8.3-24.1 10.1-26.7 22.4-.6 2.9-.7 5.8-.7 8.8V364.4c.5 13.8 10.6 25.2 24.3 27.4 2.7.4 5.5.4 8.2.4h307.9c1.7 0 3.3.1 5 0 13.7-.8 24.4-10.1 27.2-23.3.6-2.7.5-5.5.5-8.2V50.6v-3.9c-.8-16.1-14-26.9-26.7-27.3-2.2-.1-4.4-.1-6.6-.1-51.1-.1-102.1-.1-153-.1z" />
      <path d="M205.501 310.1h-54.1c-11.7 0-22.1-3.6-31.1-10.8-12.5-10.1-18.7-23.3-19.1-39.3-.1-3.7 0-7.4 0-11v-94.9c0-6.3.7-12.5 2.7-18.5 7-20.9 26.9-34.4 47.2-34.3 36.4.1 72.8.1 109.2 0 25.4-.1 49.4 20.4 49.9 49.4.3 16.5.1 33.1.1 49.6v59c0 10.4-2.7 20-8.6 28.6-10 14.4-23.9 22.1-41.5 22.2-18.3.1-36.5 0-54.7 0zm-86.3-104.6v2.8c0 16.4 0 32.7.1 49.1 0 12.9 8.6 28 25.4 33.3 4.1 1.3 8.3 1.6 12.5 1.6h75.6c8.1 0 16.2.1 24.3 0 16.1-.3 29.8-11.1 34-26.6 1-3.7 1.3-7.6 1.3-11.5v-97.1-2.8c-.8-16-10.8-29.1-25.7-33.6-4.1-1.2-8.3-1.6-12.5-1.6h-99.8c-17 .7-30.8 12.5-34.3 29.1-.7 3.1-.6 6.2-.6 9.3-.3 16-.3 32-.3 48z" />
      <path d="M259.701 205.7c.4 27.3-21.7 53.9-54.1 53.9-30.6 0-54-24.6-54-54.1.1-31.1 25.5-54 54.1-53.9 30.3.1 54.4 25.1 54 54.1zm-54.2 35.9c18.3.5 36.1-14.5 36.3-35.9.2-19.6-16-35.9-35.6-36.1-20-.2-36.3 15.7-36.5 35.7-.3 21.1 17.4 36.6 35.8 36.3zM274.001 147.7c.1 6.1-4.5 10.9-10.6 10.9-6.1.1-10.8-4.6-10.8-10.7 0-5.9 4.4-10.5 10.3-10.7 6.2-.1 11 4.5 11.1 10.5z" />
    </svg>
  );
}

export function TwitterIcon({ className }: { className?: string }) {
  return (
    <svg
      width={411}
      height={412}
      viewBox="0 0 411 412"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("*:fill-background", className)}
    >
      <path d="M411 206.1v152.8c0 5.3 0 10.6-1.3 15.9-5.1 20.9-23.5 35.9-45 36.5H51.3c-5.7 0-11.4-.3-16.9-1.9C14.1 403.6 0 384.6 0 364.5v-3.9-310c0-4.6.1-9.2 1.2-13.7C5.7 17.9 21.3 3.5 40.5.7c3.1-.5 6.2-.7 9.3-.7h311.1c13.2 0 25 3.8 34.8 12.9 9.4 8.8 14.6 19.7 15.1 32.6.1 2.8.1 5.5.1 8.3.1 50.8.1 101.6.1 152.3zM205.4 19.3H52.6c-2.2 0-4.4 0-6.6.1-12.6.7-23 9.4-26.2 21.7-.8 3.3-.9 6.5-.9 9.9v309.5c0 1.7 0 3.3.1 5 .8 13.2 10.8 24.2 24 26.3 2.9.5 5.9.5 8.8.5h307.3c1.7 0 3.3.1 5 0 13.7-.8 24.5-9.9 27.3-23.1.6-2.9.5-5.9.5-8.8v-309V47c-.5-14.6-12-26.5-26.5-27.5-2.4-.2-4.8-.1-7.2-.1-51-.1-101.9-.1-152.8-.1z" />
      <path d="M89.4 102c2.4-1 4.1-.7 5.7-.7h72.2c6.1 0 4.8-.5 8.1 4.1 13.5 18.8 27 37.7 40.5 56.5 1.5 2.1 3 4.1 4.6 6.3 2.2-.4 3.1-2.2 4.3-3.4 18.1-19.3 36.1-38.7 54.3-58 6.4-6.8 3.4-5.3 12.7-5.5 5.7-.1 11.4 0 17.1 0h4.3c.4 2.1-1.2 2.9-2.1 3.9-17.3 18.6-34.6 37.1-52 55.6L238.4 183c-.9.9-1.7 1.9-2.6 2.8-1.1 1.1-1.3 2.2-.3 3.4.9 1.2 1.7 2.4 2.6 3.6 26.4 36.9 52.9 73.8 79.3 110.7 1.2 1.6 2.3 3.3 3.4 5 .4.7-.1 1.5-.9 1.5-1.5.1-2.9.1-4.4.1h-71.7c-6.5 0-5 .4-8.5-4.4-14.8-20.6-29.5-41.2-44.3-61.9-4.5-6.2-3.6-6.3-8.7-.8-19.6 20.9-39.2 41.9-58.8 62.8-4.4 4.7-2.9 4.1-9.4 4.2H95.3c-1.3 0-2.6 0-3.9-.1-.8 0-1.3-.9-.8-1.5 1.1-1.3 2.2-2.5 3.3-3.7 25.1-26.9 50.2-53.7 75.3-80.6 5.1-5.4 4.9-3.8.6-9.8-25.4-35.6-50.9-71.1-76.4-106.7-1.3-1.6-2.5-3.4-4-5.6zm57.4 21.4c-4.2.1-8.5.1-12.7.1-.8 0-1.3.9-.9 1.5 1 1.5 2 3.1 3.1 4.6 36.7 51.3 73.4 102.5 110.1 153.8.6.9 1.3 1.8 1.9 2.7 1 1.6 2.4 2.1 4.2 2.1 7.7-.1 15.4 0 23.2 0 .4 0 .7 0 1.1-.1.8-.1 1.3-.9.9-1.5-1-1.5-2-3.1-3.1-4.6-36.7-51.3-73.4-102.5-110.1-153.8-.7-1-1.5-2.1-2.3-3.1-.8-1.1-1.9-1.6-3.3-1.6-4-.1-8.1-.1-12.1-.1z" />
    </svg>
  );
}

export function Tiktok({ className }: { className?: string }) {
  return (
    <svg
      width={412}
      height={411}
      viewBox="0 0 412 411"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("*:fill-background", className)}
    >
      <path d="M0 204.744v-155c0-4.4.2-8.8 1.2-13.1 5-20.9 23.4-36 44.9-36.6 1.8-.1 3.7 0 5.5 0h307.8c4.8 0 9.5 0 14.3 1.1 22 4.8 36.8 24.4 37.3 44.3.1 2.6.1 5.1.1 7.7v305c0 5.5 0 11-1.3 16.4-5.1 21.2-23.7 35.1-42.3 36.2-2.4.1-4.8.2-7.2.2H50.8c-5.2 0-10.3-.2-15.3-1.6-21.9-5.8-35.3-25.6-35.4-44.7v-5.5c-.1-51.5-.1-102.9-.1-154.4zm205.6 187.2h158.9c12.6-.5 23.2-9.1 26.6-21.2 1-3.6 1-7.3 1-10.9v-308.8-4.4c-.5-12.7-9.4-23.7-21.7-26.7-3.3-.8-6.5-.9-9.8-.9H46.7c-12.8.6-23.5 9.4-26.7 21.8-.9 3.4-.9 6.9-.9 10.4v313.2c.5 12.6 9.1 23.2 21.2 26.6 3.4 1 6.9 1 10.4 1 51.6-.1 103.2-.1 154.9-.1z" />
      <path d="M245.2 159.245c-.1 2.9-.2 4.8-.2 6.8 0 28.9.1 57.7-.1 86.6-.2 21.5-8.5 39.4-25.4 52.7-39.3 31-97.3 10.4-107.5-39.4-7.7-37.8 17.5-71.8 52.4-79 4.9-1 9.8-1.6 14.8-1.3 1.3.1 2.6.2 3.8.5 1.3.4 2.2 1.4 2.4 2.7.1.5.1 1.1.1 1.6 0 8.6.1 17.3 0 25.9 0 3.7-1.4 5-5.1 4.8-3.7-.2-7.4 0-10.9 1-13.4 3.6-23.9 16-23.4 31.6.5 15.2 11.4 27.1 26.1 30 14.9 2.9 32.6-6.7 36.5-25.8.7-3.3.7-6.6.7-9.9v-147.2-4.4c.2-4.7 1.1-5.6 6-5.8h26.5c.9 0 1.8 0 2.8.1 2.5.3 3.7 1.3 4.2 3.6.3 1.3.3 2.5.5 3.8 1.2 8.9 4.3 17.1 9.7 24.3 7.7 10.4 18.1 16.5 30.8 18.7 2.4.4 4.7.6 7.1 1 1.8.3 2.9 1.5 3.2 3.3.1.9.1 1.8.1 2.8v23.2c0 .9 0 1.8-.1 2.7-.3 1.7-1.6 2.9-3.3 3.2-.9.1-1.8.1-2.8.1-9.4-.5-18.6-1.9-27.5-5.1-7.2-2.6-13.7-6.2-19.1-11.6-.5-.2-1-.5-2.3-1.5z" />
    </svg>
  );
}

export function YouTubeIcon({ className }: { className?: string }) {
  return (
    <svg
      width={412}
      height={412}
      viewBox="0 0 412 412"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("*:fill-background", className)}
    >
      <path d="M205.3 411.2h-155c-5.4 0-10.6-.4-15.8-1.9-20.2-5.9-34-24-34.5-44.9v-3.3V50c0-3.7.2-7.3.9-11C4.6 18.8 21.3 3.3 40.7.7c2.9-.4 5.8-.7 8.8-.7h312.2c12.4 0 23.6 3.6 33 11.8 10.5 9.2 15.8 21 16.5 34.9.1 1.7 0 3.3 0 5v308.4c0 5.5-.4 11-1.9 16.4-5.6 20.2-23.7 34.3-44.6 34.9-2.2.1-4.4 0-6.6 0-50.9-.2-101.9-.2-152.8-.2zm.4-392.1H52.9c-2.2 0-4.4 0-6.6.1-13.6.4-25.1 11.3-26.8 24.2-.3 2.5-.5 5.1-.5 7.7v309c0 2.6.2 5.2.5 7.7 1.9 12.6 11.5 22.1 24 23.9 2.5.4 5.1.4 7.7.4H364c17.3-.7 28.1-14.8 28-28.5v-4.4V51.9c0-2 0-4-.1-6.1-.6-14.2-12.7-26.1-26.8-26.6-2.2-.1-4.4-.1-6.6-.1H205.7z" />
      <path d="M205.7 289.9h-83.9c-3.7 0-7.3-.1-10.9-1.2-14.2-4.3-21.8-17.2-21.8-28.1v-3.9-102.6c0-2.9-.1-5.9.6-8.8 2.9-13.5 14.8-23.5 28.6-24.2 1.5-.1 2.9 0 4.4 0h166.1c3.3 0 6.6.1 9.9.9 13.4 3.2 23 14.8 23.5 28.5.1 1.5 0 2.9 0 4.4v101.5c0 3.9-.1 7.7-1.2 11.5-3.6 12.5-15 21.5-28.1 22-1.8.1-3.7 0-5.5 0h-81.7zm-32.4-84.7v21.5c0 1.6.1 3.3.5 4.9 1.4 5.2 7.3 7.3 10.8 5.5 1-.5 2-.9 3-1.4 14.4-7.2 28.9-14.4 43.3-21.7 1.1-.6 2.3-1.1 3.4-1.9 4.5-3.3 4.5-9.8.1-13.2-.7-.6-1.5-1-2.4-1.4-15.1-7.6-30.3-14.9-45.3-22.7-7.2-3.7-14 1-13.5 8.4.1 1.1 0 2.2 0 3.3.1 6.3.1 12.5.1 18.7z" />
    </svg>
  );
}

// ========================================================================
// علامة الإقتباس
// ========================================================================
export function QuoteIcon({ className }: { className?: string }) {
  return (
    <svg
      width={79}
      height={58}
      viewBox="0 0 79 58"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M36.82 15.96c.316 2.458.179 4.98-.348 7.401-.558 2.56-1.533 5.012-2.681 7.364-5.948 12.191-16.779 21.876-29.495 26.383 6.695-4.89 10.988-12.578 11.879-20.86-6.327-.962-11.98-5.565-14.24-11.596C-.324 18.603.915 11.371 5.05 6.44 9.184 1.507 16.064-.924 22.354.323c6.29 1.248 11.737 6.122 13.718 12.265.353 1.1.6 2.23.748 3.372zM78.7 15.96c.315 2.458.178 4.98-.348 7.401-.559 2.56-1.533 5.012-2.682 7.364-5.947 12.191-16.778 21.876-29.495 26.383 6.696-4.89 10.989-12.578 11.88-20.86-6.328-.962-11.98-5.565-14.24-11.596-2.26-6.048-1.022-13.28 3.113-18.212C51.064 1.507 57.944-.924 64.234.323c6.29 1.248 11.737 6.122 13.717 12.265.353 1.1.6 2.23.748 3.372z"
        className="fill-primary"
      />
    </svg>
  );
}

// ========================================================================
// ايقونة بي دي اف
// ========================================================================
export function PdfIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      x={0}
      y={0}
      viewBox="0 0 351.6 367.3"
      xmlSpace="preserve"
      className={className}
    >
      <path
        fill="#EC676A"
        d="M56.5 172.5c.1-1.6.2-3.2.2-4.8V38.8c0-11.3 3.6-21.1 12.1-28.7C75.3 4.3 82.8 1 91.3 1 141 .8 190.7.9 240.4.9c.5 0 1 .2 1.4.3 0 30.9-.1 61.7 0 92.6 0 12.7 10.7 23.3 23.5 23.3h85.5c.8 1.1.4 2.4.4 3.6v208.4c0 17.8-10.3 31.8-26.6 36.5-3.4 1-6.8 1.3-10.4 1.3H93.8c-21.3 0-36.8-15.5-37.1-36.8-.1-5.7 0-11.5 0-17.2h227.5c6.1-.2 8.5-2.7 8.5-8.8V181.5c0-6.5-2.3-8.8-8.8-8.8H178.5c-40.5-.1-81.2-.1-122-.2z"
      />
      <path
        fill="#E4262B"
        d="M56.5 172.5c40.7 0 81.5 0 122.2.1h105.4c6.5 0 8.8 2.3 8.8 8.8V304c0 6.1-2.4 8.6-8.5 8.8H56.9c-15.5-.1-31-.1-46.5-.2-7.6 0-9.8-2.2-9.8-9.8V182.1c0-7.5 2.1-9.6 9.5-9.6 15.4.1 30.9.1 46.4 0zm68.1 69.9v31.5c0 4 1.1 5.2 5.1 5.2 6.5.1 13 .2 19.5-.1 11.9-.5 19.2-6.5 20.3-17.9 1.2-12.5 1.1-25.2 0-37.8-1-11.2-8.2-17.3-19.5-17.8-6.6-.3-13.2-.2-19.8-.1-4.6 0-5.5 1-5.5 5.5-.1 10.5-.1 21-.1 31.5zm-55.2-.2v31.5c0 3.9 1.6 5.4 5.5 5.4 3.8 0 5.2-1.4 5.2-5.2 0-6.7.1-13.5 0-20.2-.1-2.5.8-3.2 3.2-3.1 3.6.2 7.2.1 10.9 0 13.2-.4 20.8-8 21.1-21.1 0-1.5 0-3-.1-4.5-.5-11.6-7.5-18.8-19.1-19.4-7.1-.3-14.2-.2-21.4-.2-4.4 0-5.3 1-5.3 5.3v31.5zm113 0v31.5c0 4 1.4 5.4 5.3 5.4s5.3-1.5 5.3-5.5c0-8.1.1-16.2 0-24.4 0-2.3.6-3 2.9-2.9 4 .2 8 0 12 .1 3 .1 4-1.6 4-4.2s-1.2-4.2-4.1-4.2c-4.1.1-8.3-.1-12.4.1-2 .1-2.5-.6-2.5-2.6.1-5.9.2-11.8 0-17.6-.1-2.6.8-3.1 3.2-3.1 8.4.1 16.7 0 25.1.1 3.4.1 4.5-1.8 4.6-4.6 0-2.7-.8-4.9-4.3-4.8-11.4.1-22.7 0-34.1.1-4 0-5 1.2-5 5.2v31.4zM350.8 117.1c-28.5 0-57 .1-85.5 0-12.8 0-23.4-10.6-23.5-23.3V1.2c11.5 12.1 23.1 24.2 34.5 36.4l70.8 75.3c1.4 1.3 3 2.4 3.7 4.2z"
      />
      <path
        fill="#FEFEFE"
        d="M124.6 242.4v-31.5c0-4.5.9-5.5 5.5-5.5 6.6 0 13.2-.2 19.8.1 11.3.5 18.5 6.6 19.5 17.8 1.1 12.5 1.2 25.2 0 37.8-1 11.4-8.4 17.3-20.3 17.9-6.5.3-13 .2-19.5.1-4 0-5-1.2-5.1-5.2.1-10.6.1-21 .1-31.5zm10.7-.2v24.7c0 1.8.3 2.7 2.4 2.6 3.6-.2 7.3 0 10.9-.1 5.8-.3 9.6-3.4 10.1-9.1 1-12.1 1-24.2 0-36.3-.4-5.1-3.6-8-8.7-8.9-3.7-.7-7.5-.1-11.2-.4-2.7-.2-3.7.6-3.6 3.5.2 8 .1 16 .1 24zM69.4 242.2v-31.5c0-4.3.9-5.3 5.3-5.3 7.1 0 14.3-.2 21.4.2 11.6.6 18.6 7.8 19.1 19.4.1 1.5.1 3 .1 4.5-.4 13.2-7.9 20.7-21.1 21.1-3.6.1-7.3.2-10.9 0-2.4-.1-3.3.5-3.2 3.1.2 6.7.1 13.5 0 20.2 0 3.8-1.4 5.1-5.2 5.2-3.9 0-5.4-1.5-5.5-5.4v-31.5zm10.7-13.9v11.6c0 1.5.3 2.4 2 2.3 4.1-.1 8.3.1 12.3-.3 6.2-.6 9.5-4.3 10.1-10.5.1-1.7.1-3.5 0-5.2-.3-7.1-4.1-11-11.3-11.4-3.5-.2-7 0-10.5-.1-2.1-.1-2.9.6-2.8 2.7.3 3.7.2 7.3.2 10.9zM182.4 242.2v-31.5c0-4 1-5.2 5-5.2 11.4-.1 22.7 0 34.1-.1 3.5 0 4.3 2.2 4.3 4.8 0 2.8-1.2 4.7-4.6 4.6-8.4-.1-16.7 0-25.1-.1-2.4 0-3.3.5-3.2 3.1.2 5.9.1 11.7 0 17.6 0 1.9.5 2.6 2.5 2.6 4.1-.1 8.2 0 12.4-.1 2.9 0 4.1 1.6 4.1 4.2s-1 4.3-4 4.2c-4-.1-8 .1-12-.1-2.3-.1-3 .6-2.9 2.9.1 8.1.1 16.2 0 24.4 0 4-1.4 5.5-5.3 5.5s-5.3-1.4-5.3-5.4v-31.4z"
      />
      <path
        fill="#E4262B"
        d="M135.3 242.2c0-8 .1-16-.1-24-.1-2.9.8-3.7 3.6-3.5 3.7.3 7.5-.3 11.2.4 5.1.9 8.2 3.8 8.7 8.9 1 12.1 1 24.2 0 36.3-.5 5.7-4.3 8.8-10.1 9.1-3.6.2-7.3 0-10.9.1-2.1.1-2.5-.8-2.4-2.6v-24.7zM80.1 228.3c0-3.6.1-7.2 0-10.8-.1-2.2.7-2.8 2.8-2.7 3.5.2 7-.1 10.5.1 7.1.4 10.9 4.3 11.3 11.4.1 1.7.1 3.5 0 5.2-.5 6.3-3.9 9.9-10.1 10.5-4.1.4-8.2.1-12.3.3-1.7.1-2.1-.9-2-2.3-.2-4-.2-7.8-.2-11.7z"
      />
    </svg>
  );
}

export function HeroImageFramePhone({ className }: { className?: string }) {
  return (
    <svg
      viewBox="0 0 320 300"
      xmlns="http://www.w3.org/2000/svg"
      className={cn("fill-none", className)}
    >
      <mask
        id="a"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        width={320}
        height={300}
      >
        <path transform="matrix(-1 0 0 1 320 0)" fill="#3E384D" d="M0 0H320V300H0z" />
      </mask>
      <g mask="url(#a)">
        <g clipPath="url(#clip0_118_171)" filter="url(#filter0_d_118_171)" fill="#FAFAFA">
          <path d="M250.493 133.027c-.528 1.769-2.401 2.709-4.118 3.346-7.305 2.707-15.123 3.555-22.855 3.784-1.6.046-3.201.068-4.801.073-.779.004-1.559.002-2.34-.001-.683-.004-1.723-.198-2.343.045.53 2.327 3.347 4.397 5.633 4.467.98.031 1.941-.228 2.893-.459 5.685-1.379 11.585-1.832 17.161-3.603a65.227 65.227 0 01-19.87 4.901c.401 1.825 1.944 3.247 3.692 3.863 1.749.614 3.672.518 5.483.133 3.059-.648 5.9-2.077 8.6-3.668 1.799-1.061 3.552-2.2 5.303-3.338 2.057-1.335 4.14-2.692 5.78-4.523 1.236-1.372 2.235-3.224 1.782-5.02z" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M263.192 118.72a6.08 6.08 0 012.129 1.916c-2.773-.161-5.53 1.782-6.334 4.462-.321 1.068-.354 2.193-.387 3.313-.007.267-.015.534-.027.8-.098 2.257-.492 4.529-1.444 6.572-1.536 3.294-4.43 5.785-7.624 7.477-3.194 1.69-6.704 2.666-10.181 3.627 1.539-1.837 3.427-3.337 5.312-4.834 2.25-1.787 4.494-3.57 6.133-5.916.614-.878 1.136-1.834 1.438-2.865.321-1.102.383-2.268.283-3.411-.166-1.91-.779-3.764-1.658-5.463-.575-1.112-1.262-2.16-1.947-3.204l-.031-.047c-.977-1.487-1.962-2.985-3.203-4.255l-.164-.168c-.156-.158-.313-.316-.46-.482a3.904 3.904 0 01-.564-.78c-.08-.156-.146-.464-.146-.464s.161.056.3.121c.157.073.309.149.46.224.413.206.815.407 1.286.524 1.221.305 2.543.227 3.705-.268.314-.132.615-.292.915-.452.22-.117.439-.234.664-.34 3.21-1.515 7.395-.513 9.587 2.299.**************.161.211.199.264.399.529.652.741.251.211.546.358.84.506.***************.305.156zm-5.054-.3c0 .806-.647 1.46-1.446 1.46a1.454 1.454 0 01-1.447-1.46c0-.806.648-1.46 1.447-1.46s1.446.654 1.446 1.46z"
          />
          <path d="M249 137.255c-.235.297-.526.562-.824.837-2.042 1.879-4.635 3.213-7.005 4.617-2.575 1.525-5.184 2.99-7.855 4.334-2.11 1.062-4.211 2.34-6.504 2.954-.41.11-.913.105-.763.648.247.9 1.08 1.501 2.122 1.84 1.869.605 4.406.363 5.446-.52.735-.627 1.389-1.325 2.145-1.93 3.489-2.79 7.061-5.477 10.415-8.433 1.218-1.073 2.379-2.225 3.364-3.521 0 0 .897-1.425.897-2.972-.002-.001-.416.857-1.438 2.146zM219.669 95.15c3.289 6.098 8.122 11.195 13.165 15.915a178.958 178.958 0 009.673 8.419c1.981 1.602 4.018 3.184 5.591 5.196 1.571 2.013 2.655 4.549 2.397 7.099-1.181-2.8-3.262-5.131-5.602-7.052-2.338-1.921-4.947-3.472-7.492-5.103-4.462-2.858-8.772-5.992-12.608-9.662-1.967-1.883-3.836-3.948-4.981-6.426-1.218-2.637-1.532-5.77-.143-8.387z" />
          <path d="M215.331 100.389c1.501 2.216 2.643 4.531 4.442 6.577 1.785 2.031 3.795 3.852 5.887 5.555a79.507 79.507 0 009.275 6.509c4.909 2.948 10.268 5.47 13.974 9.859 1.042 1.233 1.978 2.831 1.555 4.395-3.475.733-6.949 1.465-10.425 2.2-3.294.695-6.668 1.393-10.003.934-3.335-.459-6.682-2.308-8.025-5.423 8.153 1.981 16.667 1.528 25.04 1.062-3.365-.638-6.821-.42-10.244-.517a55.842 55.842 0 01-11.386-1.499c-2.386-.569-4.789-1.322-6.763-2.789-1.974-1.468-3.472-3.772-3.44-6.247 7.549 4.831 16.625 6.462 25.456 7.846 3.039.477 6.17.968 8.792 2.588-1.377-1.998-3.986-2.612-6.354-3.069-7.462-1.442-15.029-2.88-21.882-6.192-2.427-1.172-4.803-2.624-6.447-4.772-1.644-2.147-2.439-5.117-1.464-7.648 3.736 4.954 9.095 8.437 14.702 11.029 5.609 2.592 11.54 4.386 17.304 6.608-2.429-1.975-5.457-2.999-8.357-4.158a79.8 79.8 0 01-13.215-6.779c-2.853-1.818-5.66-3.89-7.481-6.757-.883-1.39-1.489-2.979-1.705-4.619-.151-1.188-.299-3.921.764-4.693zM265.913 118.67a45.14 45.14 0 00-.399-1.008c-4.253-10.408-12.351-17.122-22.219-18.423-4.845-.639-9.963.131-14.414 2.169l-.437-.974c4.627-2.118 9.951-2.92 14.989-2.255 5.268.695 10.013 2.829 14.106 6.348 3.786 3.255 6.882 7.656 8.954 12.726.136.333.273.681.408 1.03l-.988.387zM213.576 138.681a32.834 32.834 0 01-3.267-12.993l1.057-.043a31.754 31.754 0 003.159 12.562l-.949.474zM238.815 153.338h-.181l.005-1.069h.176c7.396 0 14.556-2.673 19.667-7.351 5.436-4.972 8.526-12.399 8.068-19.379l1.056-.07c.241 3.664-.43 7.488-1.941 11.057a26.553 26.553 0 01-6.473 9.185c-5.302 4.851-12.721 7.627-20.377 7.627zM208.515 176.628h-2.734v-.369h.175c.085 0 .13-.084.13-.254a.558.558 0 00-.031-.169l-.913-2.675h-3.327c-.35.994-.526 1.734-.526 2.222 0 .266.083.478.251.637a.905.905 0 00.647.237h.403v.369h-3.409v-.369c.574-.03 1.091-.642 1.553-1.836l3.123-8.031h.449l2.695 7.853c.187.553.362.969.526 1.246.294.497.624.752.989.768v.371h-.001zm-3.54-3.935l-1.386-4.087-1.6 4.087h2.986zM217.713 174.754c-1.608 1.399-3.076 2.099-4.401 2.099-1.254 0-2.279-.471-3.076-1.415-.751-.896-1.127-1.987-1.127-3.274 0-2.028.857-3.618 2.573-4.764.817-.549 1.801-.823 2.955-.823a8.4 8.4 0 012.848.477v2.32h-.54c-.066-.743-.402-1.325-1.006-1.744-.544-.38-1.204-.569-1.98-.569-1.121 0-2.008.43-2.657 1.291-.588.785-.883 1.76-.883 2.928 0 1.224.33 2.258.99 3.098.71.907 1.657 1.361 2.841 1.361 1.122 0 2.277-.484 3.465-1.453v.468h-.002zM227.299 174.754c-1.609 1.399-3.077 2.099-4.402 2.099-1.253 0-2.279-.471-3.076-1.415-.751-.896-1.127-1.987-1.127-3.274 0-2.028.858-3.618 2.573-4.764.817-.549 1.802-.823 2.955-.823a8.4 8.4 0 012.848.477v2.32h-.54c-.066-.743-.401-1.325-1.006-1.744-.544-.38-1.203-.569-1.979-.569-1.122 0-2.008.43-2.657 1.291-.589.785-.883 1.76-.883 2.928 0 1.224.33 2.258.99 3.098.709.907 1.656 1.361 2.84 1.361 1.122 0 2.278-.484 3.466-1.453v.468h-.002zM237.105 176.628h-2.734v-.369h.175c.086 0 .13-.084.13-.254 0-.05-.01-.108-.03-.169l-.913-2.675h-3.328c-.349.994-.526 1.734-.526 2.222 0 .266.084.478.252.637a.904.904 0 00.647.237h.403v.369h-3.411v-.369c.574-.03 1.091-.642 1.553-1.836l3.122-8.031h.45l2.695 7.853c.187.553.362.969.526 1.246.294.497.624.752.989.768v.371zm-3.54-3.935l-1.385-4.087-1.6 4.087h2.985zM247.606 170.681c0 1.757-.566 3.188-1.695 4.291-1.128 1.104-2.568 1.656-4.314 1.656h-3.989v-.369c.451-.005.758-.095.917-.268.16-.175.239-.491.239-.952v-6.657c0-.46-.08-.775-.239-.945-.161-.169-.466-.259-.917-.268v-.369h5.985c2.675 0 4.013 1.293 4.013 3.881zm-1.432.999c0-3.022-1.265-4.534-3.792-4.534-.888 0-1.624.198-2.208.592v7.677c.812.477 1.604.715 2.376.715 1.168 0 2.081-.461 2.741-1.383.588-.821.883-1.843.883-3.067zM255.502 173.8l-.624 3.167h-.38c-.005-.225-.101-.339-.289-.339h-5.549v-.369c.456-.005.763-.093.922-.264.157-.171.235-.492.235-.958v-6.647c0-.461-.08-.778-.239-.949-.16-.171-.465-.263-.917-.274v-.369h5.436c.33 0 .499-.127.51-.385h.359v2.936h-.557c-.019-1.454-.888-2.183-2.604-2.183h-.577a13.933 13.933 0 00-.091 1.499c0 1.706.713 2.56 2.14 2.56h.745v.538l-2.794.077v4.419h.808c1.704 0 2.682-.819 2.931-2.459h.535zM269.345 176.628h-3.747v-.369c.396-.011.669-.092.818-.246.15-.153.225-.429.225-.823 0-.072-.005-.158-.014-.261l-.594-6.109c-.868 1.557-1.487 2.774-1.858 3.65l-1.912 4.518h-.342l-3.564-8.946-.258 2.761c-.116 1.246-.175 2.232-.175 2.96 0 1.62.401 2.451 1.204 2.498v.369h-3.442v-.369c.437-.103.786-.477 1.05-1.121.116-.278.264-.805.443-1.583.217-.957.378-1.987.479-3.088l.191-2.045c.006-.056.007-.162.007-.315 0-.369-.078-.618-.235-.745-.137-.117-.394-.182-.769-.193v-.369h2.49l3.267 8.131 1.872-4.466c.707-1.685 1.17-2.907 1.393-3.667h2.718v.369c-.412.011-.701.072-.868.186-.214.144-.32.41-.32.799 0 .061.002.12.008.176l.577 6.133c.061.666.148 1.096.259 1.29.178.323.544.492 1.097.508v.367zM278.853 167.167c-.552.011-1.159.585-1.819 1.721l-2.307 3.966v2.183c0 .788.385 1.197 1.157 1.222v.369h-3.723v-.369c.451-.005.758-.095.916-.268.161-.175.239-.491.239-.952v-2.184l-1.903-3.857c-.258-.522-.479-.904-.661-1.145-.336-.45-.683-.678-1.044-.684v-.369h2.765v.369c-.193.006-.289.074-.289.207 0 .051.01.097.03.139l2.392 4.795c1.234-1.948 1.849-3.366 1.849-4.257 0-.358-.125-.605-.373-.738-.178-.092-.467-.14-.868-.146v-.369h3.639v.367zM202.665 160.104c0 .801-.256 1.453-.77 1.957s-1.166.754-1.962.754h-1.812v-.167c.205-.002.345-.044.418-.123.073-.079.108-.225.108-.434v-3.034c0-.211-.035-.355-.108-.431-.073-.077-.213-.117-.418-.122v-.167h2.722c1.214-.002 1.822.588 1.822 1.767zm-.651.456c0-1.379-.574-2.067-1.724-2.067-.403 0-.738.09-1.004.27v3.499c.369.218.729.326 1.081.326.531 0 .947-.21 1.246-.63.268-.374.401-.84.401-1.398zM207.99 162.815h-.831c-.167 0-.315-.101-.442-.301l-1.371-2.158v1.734c0 .36.178.545.533.558v.167h-1.7v-.167c.206-.002.345-.044.418-.123.073-.079.109-.225.109-.434v-3.034c0-.211-.036-.355-.109-.431-.073-.077-.212-.117-.418-.122v-.167h2.203c.653 0 .979.257.979.77 0 .572-.367 1.071-1.1 1.496l.772 1.209c.346.542.665.821.959.837v.166h-.002zm-1.339-3.211c0-.277-.059-.52-.176-.729-.141-.25-.339-.375-.596-.375a.908.908 0 00-.533.175v1.462c.269.198.501.297.695.297.407 0 .61-.278.61-.83zM209.917 162.5a.31.31 0 01-.091.221.302.302 0 01-.437 0 .3.3 0 01-.093-.221.3.3 0 01.093-.221.298.298 0 01.437 0c.06.063.091.138.091.221zM218.945 158.502c-.422.04-.633.742-.633 2.106v2.369h-.162l-2.987-4.086v1.303c0 .605.021 1.06.062 1.363.093.689.286 1.053.578 1.089v.167H214.3v-.167c.421-.065.629-.87.629-2.414v-1.21c0-.196-.037-.331-.112-.405-.075-.073-.21-.113-.407-.117v-.167h1.136l2.517 3.424v-1.041c0-1.431-.211-2.169-.63-2.214v-.167h1.512v.167zM224.322 162.815h-1.243v-.167h.081c.039 0 .058-.038.058-.116a.262.262 0 00-.014-.077l-.415-1.219h-1.513c-.158.454-.239.791-.239 1.014 0 .12.038.218.114.29a.414.414 0 00.295.108h.183v.167h-1.551v-.167c.26-.015.496-.294.706-.837l1.42-3.662h.203l1.225 3.581c.086.252.164.441.239.567.134.226.284.343.449.351v.167h.002zm-1.61-1.793l-.63-1.863-.727 1.863h1.357zM230.049 162.815h-1.693v-.167c.203-.002.341-.044.416-.125.073-.081.11-.225.11-.432v-1.614h-2.111v1.614c0 .362.175.548.526.558v.168h-1.693v-.168c.206-.001.345-.043.418-.122.073-.079.109-.225.109-.434v-3.035c0-.21-.036-.354-.109-.43-.073-.077-.212-.117-.418-.122v-.168h1.693v.168c-.205.005-.344.045-.417.122-.074.078-.109.222-.109.43v1.203h2.111v-1.203c0-.21-.035-.354-.109-.43-.073-.077-.212-.117-.417-.122v-.168h1.693v.168c-.205.005-.345.045-.418.122-.073.078-.108.222-.108.43v3.035c0 .213.035.358.108.436.073.077.213.119.418.12v.166zM234.662 161.526l-.284 1.444h-.173c-.001-.103-.046-.155-.132-.155h-2.523v-.167c.207-.002.348-.044.419-.121.071-.079.107-.223.107-.436v-3.031c0-.21-.036-.354-.109-.432-.073-.077-.212-.119-.417-.124v-.167h2.472c.15 0 .226-.058.232-.175h.162v1.339h-.253c-.009-.664-.403-.995-1.184-.995h-.263a6.284 6.284 0 00-.041.684c0 .777.325 1.166.972 1.166h.339v.245l-1.27.034v2.014h.368c.775 0 1.22-.374 1.332-1.121h.246v-.002zM240.554 160.104c0 .801-.257 1.453-.77 1.957-.514.504-1.166.754-1.962.754h-1.813v-.167c.205-.002.344-.044.417-.123.073-.079.109-.225.109-.434v-3.034c0-.211-.036-.355-.109-.431-.073-.077-.212-.117-.417-.122v-.167h2.721c1.218-.002 1.824.588 1.824 1.767zm-.651.456c0-1.379-.574-2.067-1.724-2.067-.403 0-.738.09-1.004.27v3.499c.369.218.729.326 1.081.326.531 0 .947-.21 1.246-.63.269-.374.401-.84.401-1.398zM248.265 161.24c0 .441-.189.821-.567 1.136a1.854 1.854 0 01-1.205.441h-1.623v-.168c.205-.001.344-.043.417-.122.073-.079.109-.225.109-.434v-3.035c0-.21-.036-.354-.109-.43-.073-.077-.212-.117-.417-.122v-.168h2.094c.338 0 .593.047.761.141.223.124.335.34.335.651 0 .481-.326.917-.979 1.307.368.006.637.049.81.133.25.121.374.344.374.67zm-.824-1.643c0-.288-.069-.535-.21-.744a.742.742 0 00-.647-.354c-.229 0-.411.055-.548.167v1.629h.676c.485.002.729-.232.729-.698zm.159 2.07c0-.368-.11-.661-.332-.881-.221-.219-.515-.329-.879-.329h-.353v1.847c.237.189.485.284.744.284.258 0 .462-.09.613-.27.139-.165.207-.383.207-.651zM253.845 162.815h-1.243v-.167h.081c.039 0 .059-.038.059-.116a.267.267 0 00-.015-.077l-.415-1.219h-1.513c-.158.454-.238.791-.238 1.014 0 .12.037.218.114.29a.41.41 0 00.294.108h.184v.167h-1.552v-.167c.26-.015.496-.294.706-.837l1.42-3.662h.205l1.225 3.581c.086.252.164.441.239.567.134.226.284.343.449.351v.167zm-1.608-1.793l-.63-1.863-.727 1.863h1.357zM257.901 161.508c0 .393-.178.731-.537 1.014a1.721 1.721 0 01-1.097.396c-.303 0-.642-.074-1.018-.222v-1.262h.246c0 .323.089.605.269.848.197.267.45.4.758.4a.833.833 0 00.6-.22c.155-.146.231-.342.231-.587 0-.253-.116-.493-.349-.714-.095-.088-.318-.252-.669-.488a7.001 7.001 0 01-.658-.484.982.982 0 01-.239-.33.853.853 0 01-.084-.367c0-.367.168-.675.505-.925a1.68 1.68 0 011.022-.337c.205 0 .48.06.82.179v1.099h-.253a1.277 1.277 0 00-.246-.711.765.765 0 00-.64-.329.699.699 0 00-.482.175.618.618 0 00-.198.473c0 .238.134.479.401.722.1.092.33.259.692.504.32.214.526.372.623.473.203.207.303.438.303.693zM263.811 162.815h-1.693v-.167c.204-.002.343-.044.416-.125.073-.081.11-.225.11-.432v-1.614h-2.111v1.614c0 .362.175.548.526.558v.168h-1.692v-.168c.205-.001.344-.043.417-.122.073-.079.109-.225.109-.434v-3.035c0-.21-.036-.354-.109-.43-.073-.077-.212-.117-.417-.122v-.168h1.692v.168c-.205.005-.344.045-.417.122-.073.078-.109.222-.109.43v1.203h2.111v-1.203c0-.21-.035-.354-.108-.43-.073-.077-.213-.117-.418-.122v-.168h1.693v.168c-.205.005-.344.045-.418.122-.073.078-.108.222-.108.43v3.035c0 .213.035.358.108.436.074.077.213.119.418.12v.166zM268.765 159.508h-.246c-.007-.669-.359-1.006-1.052-1.006h-.188v3.588c0 .361.175.547.527.558v.167h-1.693v-.167c.205-.002.344-.044.417-.123.074-.079.109-.225.109-.434v-3.587h-.239c-.638 0-.968.335-.989 1.006h-.243v-1.332h.152c.03.105.074.159.135.159h3.025c.078 0 .117-.053.117-.159h.166v1.33h.002zM274.124 162.815h-1.243v-.167h.08c.039 0 .059-.038.059-.116a.262.262 0 00-.014-.077l-.416-1.219h-1.512c-.159.454-.239.791-.239 1.014 0 .12.038.218.114.29a.413.413 0 00.294.108h.184v.167h-1.551v-.167c.26-.015.495-.294.706-.837l1.419-3.662h.205l1.226 3.581c.085.252.164.441.239.567.133.226.283.343.449.351v.167zm-1.61-1.793l-.63-1.863-.727 1.863h1.357zM279.85 162.815h-1.692v-.167c.203-.002.342-.044.415-.125.074-.081.111-.225.111-.432v-1.614h-2.111v1.614c0 .362.174.548.526.558v.168h-1.693v-.168c.205-.001.344-.043.418-.122.073-.079.108-.225.108-.434v-3.035c0-.21-.035-.354-.108-.43-.074-.077-.213-.117-.418-.122v-.168h1.693v.168c-.205.005-.345.045-.418.122-.073.078-.108.222-.108.43v1.203h2.111v-1.203c0-.21-.036-.354-.109-.43-.073-.077-.212-.117-.417-.122v-.168h1.692v.168c-.205.005-.344.045-.417.122-.073.078-.109.222-.109.43v3.035c0 .213.036.358.109.436.073.077.212.119.417.12v.166zM213.977 181.524h-.25c0-.371-.11-.637-.328-.799-.189-.14-.476-.211-.861-.211h-.262a7.126 7.126 0 00-.038.688c0 .423.066.727.198.914.157.225.434.337.828.337h.287v.241l-1.273.029v1.381c0 .36.174.545.526.558v.167h-1.696v-.167c.205-.002.344-.044.417-.123.073-.079.109-.225.109-.434v-3.034c0-.211-.036-.355-.109-.431-.073-.077-.212-.117-.417-.122v-.167h2.621c.054 0 .086-.053.096-.155h.152v1.328zM219.898 182.376c0 .462-.129.903-.387 1.323a2.532 2.532 0 01-2.167 1.233 2.01 2.01 0 01-1.489-.611c-.401-.406-.603-.909-.603-1.506 0-.682.248-1.28.746-1.797.497-.514 1.082-.772 1.753-.772.774 0 1.376.317 1.81.952.224.328.337.72.337 1.178zm-.585.615c0-.43-.088-.833-.264-1.211-.194-.414-.462-.72-.806-.922-.33-.191-.639-.288-.924-.288-.455 0-.818.162-1.093.488-.259.308-.387.697-.387 1.166 0 .623.178 1.169.533 1.633.387.508.881.76 1.482.76a1.34 1.34 0 001.067-.492c.26-.311.392-.689.392-1.134zM225.226 184.827h-.831c-.167 0-.315-.101-.442-.3l-1.371-2.159v1.734c0 .36.178.545.533.558v.167h-1.7v-.167c.205-.002.344-.043.418-.123.073-.079.108-.225.108-.433v-3.035c0-.211-.035-.355-.108-.43-.074-.078-.213-.117-.418-.123v-.167h2.203c.652 0 .979.257.979.77 0 .573-.368 1.071-1.101 1.496l.773 1.21c.346.541.665.82.959.837v.165h-.002zm-1.341-3.211c0-.277-.059-.52-.176-.729-.141-.25-.339-.374-.596-.374a.907.907 0 00-.533.174v1.462c.269.198.501.297.695.297.409 0 .61-.277.61-.83zM232.59 181.521h-.246c-.007-.67-.359-1.007-1.052-1.007h-.188v3.588c0 .362.175.547.526.558v.167h-1.692v-.167c.205-.002.344-.043.417-.123.074-.079.109-.225.109-.433v-3.588h-.239c-.638 0-.97.335-.99 1.006h-.242v-1.332h.152c.03.105.074.159.135.159h3.025c.078 0 .117-.052.117-.159h.166v1.331h.002zM238.33 184.827h-1.692v-.167c.203-.002.341-.043.415-.124.074-.081.111-.225.111-.432v-1.615h-2.111v1.615c0 .361.174.547.526.558v.167h-1.693v-.167c.205-.002.344-.044.418-.123.073-.079.108-.225.108-.434v-3.034c0-.211-.035-.355-.108-.431-.074-.077-.213-.117-.418-.122v-.167h1.693v.167c-.205.005-.345.045-.418.122-.073.078-.108.222-.108.431v1.202h2.111v-1.202c0-.211-.036-.355-.109-.431-.073-.077-.212-.117-.417-.122v-.167h1.692v.167c-.205.005-.344.045-.417.122-.073.078-.109.222-.109.431v3.034c0 .213.036.359.109.436.073.077.212.119.417.121v.165zM242.946 183.538l-.284 1.444h-.173c-.002-.103-.046-.155-.132-.155h-2.523v-.167c.207-.002.347-.043.419-.121.071-.079.107-.223.107-.435v-3.032c0-.21-.036-.354-.109-.432-.073-.077-.212-.118-.417-.124v-.167h2.471c.15 0 .227-.058.232-.175h.163v1.339h-.254c-.009-.664-.403-.995-1.184-.995H241a6.284 6.284 0 00-.041.684c0 .778.325 1.166.972 1.166h.339v.245l-1.27.034v2.015h.367c.776 0 1.22-.375 1.333-1.122h.246v-.002zM250.199 183.538l-.284 1.444h-.173c-.002-.103-.046-.155-.132-.155h-2.523v-.167c.207-.002.347-.043.419-.121.071-.079.107-.223.107-.435v-3.032c0-.21-.036-.354-.109-.432-.073-.077-.212-.118-.417-.124v-.167h2.471c.15 0 .227-.058.232-.175h.163v1.339h-.254c-.009-.664-.403-.995-1.184-.995h-.262a6.042 6.042 0 00-.041.684c0 .778.325 1.166.972 1.166h.339v.245l-1.27.034v2.015h.367c.776 0 1.22-.375 1.333-1.122h.246v-.002zM254.664 183.546l-.298 1.447h-.162c-.002-.11-.052-.164-.148-.164h-2.506v-.167c.205-.002.344-.044.418-.123.073-.079.108-.225.108-.434v-3.031c0-.21-.035-.354-.108-.432-.074-.077-.213-.119-.418-.124v-.167h1.7v.167c-.211.005-.351.045-.423.121-.071.075-.107.221-.107.435v3.588h.45c.699 0 1.114-.371 1.246-1.115h.248v-.001zM257.657 184.827h-1.693v-.167c.205-.002.344-.043.417-.123.074-.079.109-.225.109-.433v-3.035c0-.211-.035-.355-.109-.43-.073-.078-.212-.117-.417-.123v-.167h1.693v.167c-.209.006-.348.045-.42.123-.071.077-.107.221-.107.43v3.035c0 .36.175.545.527.558v.165zM262.589 181.521h-.246c-.007-.67-.358-1.007-1.052-1.007h-.187v3.588c0 .362.174.547.526.558v.167h-1.693v-.167c.205-.002.345-.043.418-.123.073-.079.108-.225.108-.433v-3.588h-.239c-.638 0-.968.335-.989 1.006h-.243v-1.332h.152c.03.105.075.159.135.159h3.025c.078 0 .118-.052.118-.159h.165v1.331h.002zM267.008 183.538l-.283 1.444h-.173c-.002-.103-.046-.155-.132-.155h-2.525v-.167c.207-.002.348-.043.419-.121.071-.079.107-.223.107-.435v-3.032c0-.21-.036-.354-.109-.432-.073-.077-.212-.118-.417-.124v-.167h2.471c.15 0 .227-.058.232-.175h.163v1.339h-.254c-.009-.664-.403-.995-1.184-.995h-.264a6.284 6.284 0 00-.041.684c0 .778.325 1.166.972 1.166h.339v.245l-1.27.034v2.015h.368c.775 0 1.219-.375 1.332-1.122h.249v-.002zM209.462 183.24H199.12a.152.152 0 01-.151-.153v-.04c0-.085.067-.153.151-.153h10.342c.007 0 .012.005.012.013v.322c0 .005-.005.011-.012.011zM278.85 183.24h-10.342c-.007 0-.012-.006-.012-.013v-.322c0-.007.005-.013.012-.013h10.342c.084 0 .152.069.152.153v.04a.153.153 0 01-.152.155z" />
        </g>
        <g filter="url(#filter1_d_118_171)">
          <path
            transform="rotate(80 248.737 264.753) skewX(-.032)"
            fill="#AA2556"
            d="M0 0H13.823V504.474H0z"
          />
        </g>
        <g filter="url(#filter2_d_118_171)">
          <path
            transform="rotate(80 453.668 -142.527)"
            fill="#FAFAFA"
            d="M453.668 -142.527H571.328V364.689H453.668z"
          />
        </g>
        <g filter="url(#filter3_d_118_171)">
          <rect
            x={266.812}
            y={214.89}
            width={43.671}
            height={314.231}
            rx={21.835}
            transform="rotate(80 266.812 214.89)"
            fill="#FAFAFA"
          />
        </g>
        <g filter="url(#filter4_d_118_171)">
          <rect
            x={169.467}
            y={245.14}
            width={22.741}
            height={206.853}
            rx={11.37}
            transform="rotate(80 169.467 245.14)"
            fill="url(#paint0_linear_118_171)"
          />
        </g>
        <g filter="url(#filter5_d_118_171)">
          <path
            transform="rotate(80 48.837 330.329) skewX(-.032)"
            fill="#AA2556"
            d="M0 0H10.829V395.229H0z"
          />
        </g>
        <g filter="url(#filter6_d_118_171)">
          <path
            transform="rotate(80 365.615 235.2)"
            fill="#FAFAFA"
            d="M365.615 235.2H625.351V653.488H365.615z"
          />
        </g>
        <mask
          id="b"
          style={{
            maskType: "alpha",
          }}
          maskUnits="userSpaceOnUse"
          x={131}
          y={55}
          width={32}
          height={185}
        >
          <path transform="matrix(-1 0 0 1 163 55)" fill="#D9D9D9" d="M0 0H32V185H0z" />
        </mask>
        <g opacity={0.25} filter="url(#filter7_f_118_171)" mask="url(#b)">
          <ellipse cx={184.48} cy={147.5} rx={23.671} ry={72.489} fill="#1E1E1E" />
        </g>
        <mask
          id="c"
          style={{
            maskType: "alpha",
          }}
          maskUnits="userSpaceOnUse"
          x={163}
          y={55}
          width={32}
          height={185}
        >
          <path opacity={0.03} fill="#D9D9D9" d="M163 55H195V240H163z" />
        </mask>
        <g opacity={0.5} filter="url(#filter8_f_118_171)" mask="url(#c)">
          <ellipse
            cx={23.671}
            cy={72.489}
            rx={23.671}
            ry={72.489}
            transform="matrix(-1 0 0 1 173.192 75.011)"
            fill="#fff"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_118_171"
          x={194}
          y={95}
          width={90}
          height={98}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter1_d_118_171"
          x={-30.583}
          y={-26.179}
          width={499.26}
          height={106.933}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology radius={6} in="SourceAlpha" result="effect1_dropShadow_118_171" />
          <feOffset dy={8} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter2_d_118_171"
          x={-49.843}
          y={-142.527}
          width={527.942}
          height={211.95}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter3_d_118_171"
          x={-47.189}
          y={206.346}
          width={326.128}
          height={106.661}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={-4} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter4_d_118_171"
          x={-40.443}
          y={235.94}
          width={220.059}
          height={70.716}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={-3} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter5_d_118_171"
          x={-23.596}
          y={216.873}
          width={391.144}
          height={87.076}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology radius={4} in="SourceAlpha" result="effect1_dropShadow_118_171" />
          <feOffset dy={-8} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter6_d_118_171"
          x={-50.319}
          y={227.2}
          width={465.036}
          height={336.425}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={-4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_118_171" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_118_171" result="shape" />
        </filter>
        <filter
          id="filter7_f_118_171"
          x={132.908}
          y={47.111}
          width={103.143}
          height={200.778}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_118_171" />
        </filter>
        <filter
          id="filter8_f_118_171"
          x={97.95}
          y={47.111}
          width={103.143}
          height={200.778}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_118_171" />
        </filter>
        <linearGradient
          id="paint0_linear_118_171"
          x1={180.838}
          y1={245.14}
          x2={180.838}
          y2={451.992}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={1} stopColor="#4E0971" />
        </linearGradient>
        <clipPath id="clip0_118_171">
          <path fill="#fff" transform="translate(198 95)" d="M0 0H82V90H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function HeroImageFrame({ className }: { className?: string }) {
  return (
    <svg
      width={514}
      height={750}
      viewBox="0 0 514 750"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask
        id="a"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        width={514}
        height={750}
      >
        <path fill="#3E384D" d="M0 0H514V750H0z" />
      </mask>
      <g mask="url(#a)">
        <path
          d="M94.756 810.198c175.238-62.345 254.994-143.785 326.886-288.694h69.085v288.694H94.756z"
          fill="url(#paint0_linear_48_52)"
        />
        <g filter="url(#filter0_d_48_52)">
          <rect
            x={388.505}
            y={198.565}
            width={90.602}
            height={651.916}
            rx={45.301}
            transform="rotate(-5 388.505 198.565)"
            fill="#FAFAFA"
          />
        </g>
        <g filter="url(#filter1_d_48_52)">
          <rect
            x={434.407}
            y={405.079}
            width={47.179}
            height={429.144}
            rx={21}
            transform="rotate(-5 434.407 405.079)"
            fill="url(#paint1_linear_48_52)"
          />
        </g>
        <g filter="url(#filter2_d_48_52)">
          <path
            transform="rotate(-5.016 166.801 -4861.645) skewX(-.032)"
            fill="#AA2556"
            d="M0 0H22.467V819.957H0z"
          />
        </g>
        <g filter="url(#filter3_d_48_52)">
          <path
            transform="rotate(-5 447.075 -3.824)"
            fill="#FAFAFA"
            d="M447.075 -3.824H534.035V820.59H447.075z"
          />
        </g>
        <g filter="url(#filter4_d_48_52)">
          <path
            transform="matrix(.99617 .08744 .08688 -.99622 -6.26 810.857)"
            fill="#AA2556"
            d="M0 0H22.467V819.957H0z"
          />
        </g>
        <g filter="url(#filter5_d_48_52)">
          <path
            transform="scale(1 -1) rotate(-5 -9320.823 654.06)"
            fill="#FAFAFA"
            d="M0 0H86.96V824.414H0z"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_48_52"
          x={380.278}
          y={186.441}
          width={155.529}
          height={665.786}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-4} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <filter
          id="filter1_d_48_52"
          x={425.156}
          y={394.716}
          width={96.904}
          height={444.125}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-3} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <filter
          id="filter2_d_48_52"
          x={417.728}
          y={-6}
          width={101.615}
          height={818.821}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology radius={4} in="SourceAlpha" result="effect1_dropShadow_48_52" />
          <feOffset dx={-8} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <filter
          id="filter3_d_48_52"
          x={439.075}
          y={-15.403}
          width={166.481}
          height={836.856}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <filter
          id="filter4_d_48_52"
          x={-6.261}
          y={-6}
          width={99.615}
          height={818.821}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology radius={6} in="SourceAlpha" result="effect1_dropShadow_48_52" />
          <feOffset dx={8} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <filter
          id="filter5_d_48_52"
          x={-92.474}
          y={-15.403}
          width={166.481}
          height={836.856}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_52" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_52" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_48_52"
          x1={292.741}
          y1={521.504}
          x2={292.741}
          y2={810.198}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_48_52"
          x1={457.997}
          y1={405.079}
          x2={457.997}
          y2={834.223}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={1} stopColor="#4E0971" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export function DividerShadow_Y({ className }: { className?: string }) {
  return (
    <svg
      width={136}
      height={592}
      viewBox="0 0 136 592"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask
        id="mask0_48_115"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={67}
        y={0}
        width={69}
        height={592}
      >
        <rect x={67.9999} y={-0.0000534058} width={68} height={592} fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_48_115)">
        <g
          style={{
            mixBlendMode: "multiply",
          }}
          opacity={0.25}
          filter="url(#filter0_f_48_115)"
        >
          <ellipse
            cx={50.3014}
            cy={231.965}
            rx={50.3014}
            ry={231.965}
            transform="matrix(-1 0 0 1 72.6575 64.0353)"
            fill="#1E1E1E"
          />
        </g>
      </g>
      <mask
        id="mask1_48_115"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={-1}
        y={0}
        width={69}
        height={592}
      >
        <rect
          width={68}
          height={592}
          transform="matrix(-1 0 0 1 67.9999 -5.34058e-05)"
          fill="#D9D9D9"
        />
      </mask>
      <g mask="url(#mask1_48_115)">
        <g opacity={0.5} filter="url(#filter1_f_48_115)">
          <ellipse cx={96.6436} cy={296} rx={50.3014} ry={231.965} fill="white" />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_f_48_115"
          x={-55.8452}
          y={36.1353}
          width={156.403}
          height={519.729}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_48_115" />
        </filter>
        <filter
          id="filter1_f_48_115"
          x={18.4423}
          y={36.1353}
          width={156.403}
          height={519.729}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_48_115" />
        </filter>
      </defs>
    </svg>
  );
}

export function DividerShadow_X({ className }: { className?: string }) {
  return (
    <svg
      fill="none"
      width={632}
      height={136}
      viewBox="0 0 632 136"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <mask
        id="mask0_140_83"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={0}
        width={632}
        height={68}
      >
        <rect
          y={68}
          width={68}
          height={632}
          transform="rotate(-90 0 68)"
          fill="#D9D9D9"
        />
      </mask>
      <g mask="url(#mask0_140_83)">
        <g
          style={{
            mixBlendMode: "multiply",
          }}
          opacity={0.2}
          filter="url(#filter0_f_140_83)"
        >
          <ellipse
            cx={50.3014}
            cy={311.898}
            rx={50.3014}
            ry={311.898}
            transform="matrix(-4.37114e-08 1 1 4.37114e-08 4.10327 63.3419)"
            fill="#1E1E1E"
          />
        </g>
      </g>
      <mask
        id="mask1_140_83"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x={0}
        y={68}
        width={632}
        height={68}
      >
        <rect
          opacity={0.18}
          width={68}
          height={632}
          transform="matrix(-4.37114e-08 1 1 4.37114e-08 0 68)"
          fill="#D9D9D9"
        />
      </mask>
      <g mask="url(#mask1_140_83)">
        <g opacity={0.25} filter="url(#filter1_f_140_83)">
          <ellipse
            cx={316.002}
            cy={39.3558}
            rx={50.3014}
            ry={311.898}
            transform="rotate(-90 316.002 39.3558)"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_f_140_83"
          x={-23.7967}
          y={35.4419}
          width={679.597}
          height={156.403}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_140_83" />
        </filter>
        <filter
          id="filter1_f_140_83"
          x={-23.7967}
          y={-38.8456}
          width={679.597}
          height={156.403}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur stdDeviation={13.95} result="effect1_foregroundBlur_140_83" />
        </filter>
      </defs>
    </svg>
  );
}

// ===========================================================================
// Services Icons
// ===========================================================================
export function ServicesConsultingIcon({ className }: { className?: string }) {
  return (
    <svg
      width={101}
      height={100}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect
        x={0.456}
        width={100}
        height={100}
        rx={50}
        fill="url(#paint0_radial_48_307)"
      />
      <path
        d="M28.93 35.412c-4.447-3.887-3.767-9.896-.505-12.992a8.705 8.705 0 0111.934-.07c3.215 2.971 4.166 9.016-.451 13.082 1.582.195 3.044.645 4.361 1.49 2.69 1.728 4.141 4.144 4.119 7.394-.018 2.822-.002 5.644-.004 8.464 0 1.14-.286 1.422-1.438 1.422H21.914c-1.153 0-1.43-.276-1.43-1.418 0-2.741.089-5.486-.02-8.223-.187-4.694 3.184-7.858 6.881-8.825.503-.131 1.02-.21 1.585-.324zm5.475 16.857c3.827 0 7.654-.008 11.481.01.454.002.577-.12.573-.573-.022-2.559-.008-5.115-.012-7.674-.002-1.24-.32-2.388-1.035-3.41-1.526-2.177-3.7-3.208-6.282-3.281-3.14-.087-6.285-.035-9.427-.012a7.37 7.37 0 00-3.206.755c-2.596 1.274-4.137 3.24-4.09 6.268.038 2.437.023 4.875-.005 7.312-.006.516.15.62.64.615 3.789-.02 7.577-.01 11.363-.01zm.01-16.904c3.726.003 6.74-2.998 6.74-6.705 0-3.7-3.038-6.726-6.74-6.72-3.684.006-6.679 2.998-6.695 6.69a6.696 6.696 0 006.696 6.735zM61.094 61.224c-4.242-3.671-4.064-9.453-.723-12.834a8.64 8.64 0 0112.27-.055c3.37 3.34 3.6 9.2-.628 12.852.661.15 1.305.243 1.917.442 3.22 1.043 5.498 3.037 6.361 6.418.14.547.189 1.11.189 1.677-.005 3.002 0 6.007-.002 9.01 0 .941-.325 1.26-1.277 1.26H53.864c-.857 0-1.27-.294-1.272-1.024-.008-3.284-.124-6.578.064-9.852.201-3.514 2.36-5.735 5.458-7.13.922-.412 1.906-.62 2.98-.764zm5.413 16.839c3.827 0 7.656-.008 11.483.01.444.002.57-.095.565-.555-.024-2.6-.014-5.199-.012-7.798 0-.755-.122-1.487-.43-2.176-1.227-2.73-3.458-4.128-6.335-4.341-3.312-.243-6.642-.073-9.965-.063a7.35 7.35 0 00-1.908.276c-3.497.952-5.399 3.43-5.399 7.038 0 2.317.016 4.633-.012 6.95-.006.51.091.677.648.673 3.79-.029 7.577-.014 11.365-.014zm.006-16.92c3.76.004 6.726-2.944 6.726-6.682 0-3.711-3.03-6.746-6.722-6.73-3.697.019-6.725 3.067-6.711 6.757.014 3.689 3.002 6.653 6.707 6.655z"
        fill="#3E384D"
      />
      <path
        d="M66.082 20.003c2.46 0 4.919-.004 7.378 0 2.37.005 3.952 1.566 3.963 3.93.012 2.863.012 5.726 0 8.588-.008 2.32-1.522 3.872-3.835 3.924-.4.01-.509.124-.503.513.023 1.29.002 2.58.012 3.87.004.473-.087.895-.555 1.111-.486.225-.879.05-1.254-.301-1.74-1.621-3.498-3.224-5.235-4.848-.273-.258-.549-.345-.913-.343-2.178.014-4.354.012-6.532.006-2.33-.008-3.887-1.578-3.891-3.92-.004-2.882-.006-5.765 0-8.648.004-2.301 1.574-3.878 3.87-3.884 2.497-.004 4.997.002 7.495.002zm5.075 18.883c0-1.156-.004-2.163.002-3.168.004-.865.334-1.187 1.215-1.193.363-.004.725 0 1.088 0 1.303-.006 2.018-.705 2.022-2.018.01-2.86.01-5.723 0-8.583-.006-1.273-.731-1.98-2-1.982-4.937-.004-9.872-.002-14.809 0-1.33 0-2.024.683-2.026 1.998-.004 2.86-.004 5.723 0 8.583.002 1.32.691 2 2.02 2.002 2.277.002 4.554.022 6.83-.012.68-.01 1.183.202 1.665.666 1.276 1.22 2.587 2.405 3.993 3.707zM34.902 79.997c-2.46 0-4.919.004-7.376 0-2.407-.004-3.977-1.564-3.983-3.97-.008-2.822-.006-5.644 0-8.466.004-2.411 1.473-3.939 3.88-4.002.392-.01.463-.143.457-.492-.016-1.31 0-2.62-.01-3.93-.004-.482.115-.891.59-1.092.463-.197.842-.03 1.2.304 1.741 1.62 3.501 3.219 5.238 4.844.29.271.581.364.966.362 2.176-.014 4.354-.01 6.531-.006 2.26.006 3.85 1.583 3.856 3.835.008 2.924.008 5.845 0 8.768-.006 2.255-1.59 3.837-3.847 3.845-2.504.006-5.004 0-7.502 0zm-5.097-18.88v2.992c-.002 1.102-.278 1.371-1.402 1.373-.302 0-.604-.002-.908 0-1.282.006-2.01.687-2.018 1.961-.016 2.88-.016 5.762 0 8.644.006 1.277.738 1.974 2.008 1.974 4.937.002 9.872.002 14.81 0 1.322 0 2.017-.69 2.019-2.004.002-2.84.002-5.683 0-8.523 0-1.37-.675-2.05-2.03-2.052-2.196-.002-4.394-.038-6.588.018-.861.022-1.483-.249-2.085-.843-1.197-1.18-2.463-2.299-3.806-3.54z"
        fill="#AA2556"
      />
      <path
        d="M57.95 28.204c.01-1.44 1.139-2.54 2.587-2.52 1.38.018 2.524 1.193 2.506 2.569a2.569 2.569 0 01-2.615 2.52c-1.404-.02-2.488-1.145-2.478-2.569zm1.935.016c.022.375.204.61.591.612.38.002.61-.237.618-.596.006-.36-.223-.6-.6-.61-.386-.007-.581.217-.61.594zM74.187 28.247a2.548 2.548 0 01-2.595 2.528 2.531 2.531 0 01-2.494-2.56 2.531 2.531 0 012.59-2.529 2.542 2.542 0 012.5 2.56zm-1.93-.017c-.047-.364-.235-.605-.616-.601-.371.004-.604.21-.602.6.002.38.215.601.596.601.383.002.571-.233.622-.6zM63.525 28.25a2.529 2.529 0 012.5-2.566c1.428-.014 2.595 1.136 2.591 2.552a2.561 2.561 0 01-2.548 2.537 2.52 2.52 0 01-2.543-2.522zm1.933-.054c.012.387.192.624.577.636.379.012.616-.21.632-.577.016-.367-.208-.608-.583-.626-.381-.02-.586.198-.626.567zM34.71 68.596h5.743c.2 0 .405-.006.603.022.49.067.754.391.8.85.047.47-.196.807-.642.981-.25.098-.514.084-.776.084H28.713c-.748-.023-1.208-.4-1.204-.99.002-.59.446-.941 1.22-.943 1.991-.008 3.985-.004 5.98-.004zM31.685 74.943c-1.027 0-2.054.006-3.08-.002-.668-.006-1.096-.383-1.096-.942-.002-.547.432-.973 1.084-.977 2.075-.016 4.147-.016 6.222 0 .67.006 1.06.407 1.049.993-.01.581-.401.922-1.098.926-1.03.008-2.056.002-3.081.002zM39.51 74.939c-.443 0-.887.014-1.328-.004-.558-.023-.96-.38-.965-.884-.004-.52.252-.93.786-.986a14.41 14.41 0 013.072.002c.526.057.8.454.788.984-.013.51-.406.863-.963.886-.464.018-.928.002-1.39.002z"
        fill="#AA2556"
      />
      <defs>
        <radialGradient
          id="paint0_radial_48_307"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="matrix(0 50 -50 0 50.456 50)"
        >
          <stop stopColor="#fff" />
          <stop offset={1} stopColor="#FAFAFA" />
        </radialGradient>
      </defs>
    </svg>
  );
}

export function ServicesShortProgramsIcon() {
  return (
    <svg
      width={101}
      height={100}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x={0.456}
        width={100}
        height={100}
        rx={50}
        fill="url(#paint0_radial_48_289)"
      />
      <path
        d="M37.517 34.785c2.177-1.601 4.504-2.914 7.08-3.77a27.07 27.07 0 014.619-1.079c.174.032.349.052.5-.072.508-.032 1.025.01 1.518-.157.728.063 1.456.063 2.183.005.518.156 1.053.134 1.584.158.114.1.247.098.385.066 4.338.571 8.219 2.24 11.709 4.857l1.47 1.231c.523.525 1.048 1.048 1.57 1.574.412.49.82.982 1.232 1.473 1.906 2.556 3.337 5.344 4.2 8.424 3.127 11.175-2.474 23.361-13.049 28.176-7.62 3.47-15.112 3-22.43-.994-.811-.443-1.651-.832-2.465-1.269-.245-.13-.431-.062-.672-.008-1.222.275-1.812 1.069-2.034 2.255-.102.548-.347 1.069-.53 1.6-.164.475-.505.71-1.006.71-1.028 0-2.055-.004-3.083.002-.557.002-.904-.257-1.076-.786-.293-.898-.618-1.786-.89-2.688-.11-.367-.353-.49-.654-.68-1.006-.635-1.892-.429-2.813.167-.487.314-1.036.53-1.557.793-.475.241-.89.153-1.261-.218-.71-.714-1.42-1.43-2.139-2.135-.413-.405-.493-.84-.222-1.363.445-.856.852-1.732 1.3-2.586.147-.279.09-.487.023-.778-.285-1.207-1.073-1.764-2.23-1.986-.527-.103-1.028-.341-1.543-.51-.53-.17-.782-.525-.778-1.082.008-1.008.01-2.015-.002-3.023-.006-.593.293-.934.83-1.11.898-.293 1.79-.598 2.692-.879.315-.098.443-.304.61-.575.631-1.03.499-1.965-.159-2.923-.314-.46-.51-1-.767-1.501-.243-.473-.173-.89.202-1.263.728-.726 1.457-1.45 2.179-2.181.393-.397.82-.455 1.315-.2.86.438 1.73.855 2.588 1.3.269.14.497.21.776.022.18-.122.393-.232.603-.266.798-.128 1.035-.668 1.149-1.381.653-4.092 2.277-7.772 4.765-11.078l1.233-1.47 1.571-1.57c.493-.41.985-.82 1.474-1.232zm5.749 31.467c.09.096.126.146.172.18.13.096.261.19.395.277 3.38 2.157 7.04 2.963 11.012 2.325 8.154-1.307 13.942-9.181 12.717-17.32-1.34-8.91-9.664-14.728-18.479-12.91-6.074 1.251-11.17 6.62-11.965 12.61-.02.146-.15.304.014.449.19.168.34.004.489-.069.902-.443 1.8-.892 2.696-1.347.477-.242.89-.168 1.263.205.726.727 1.451 1.453 2.183 2.175.395.39.463.82.205 1.315-.271.517-.474 1.076-.798 1.555-.616.908-.74 1.774-.149 2.759.199.333.357.561.738.673.828.247 1.642.538 2.46.818.808.279.954.483.956 1.311.002.906.002 1.812 0 2.72-.002.782-.174 1.025-.924 1.277-.97.33-1.943.65-2.985.997zm-.812 7.397c.082.08.106.116.14.134.125.066.249.13.377.189 4.771 2.16 9.707 2.66 14.786 1.365 14.69-3.745 21.462-20.776 13.16-33.453-4.923-7.52-12.117-10.893-21.072-10.036-6.136.588-11.115 3.553-14.978 8.356-1.564 1.944-2.717 4.121-3.55 6.475-.179.5-.073.623.432.585.482-.036.967-.01 1.45-.008.79.004 1.018.17 1.273.924l.794 2.37c1.996-9.727 11.496-15.865 21.162-13.532 9.033 2.18 14.724 11.166 12.838 20.365-.926 4.517-3.314 8.175-7.155 10.721-6.348 4.208-12.857 4.033-19.529.195l1.261 2.521c.435.872.407 1.077-.268 1.748-.367.367-.746.72-1.12 1.08zm2.786-10.927c0-1.059 0-1.059-.904-1.361-.665-.225-1.329-.464-2-.668-.467-.142-.75-.417-.882-.892a8.391 8.391 0 00-.83-1.999c-.263-.447-.24-.856 0-1.309.417-.78.798-1.577 1.193-2.37.086-.174.18-.31-.002-.53-.985-1.2-1.02-1.217-2.38-.538-.198.099-.41.175-.593.297-.958.654-1.814.57-2.953.174-1.578-.547-2.313-1.455-2.692-2.98-.357-1.438-.447-1.404-1.943-1.368-.294.008-.429.103-.509.371-.154.52-.385 1.02-.491 1.548-.24 1.2-.978 1.726-2.085 2.257-1.475.71-2.6.527-3.899-.245-1.309-.775-1.345-.695-2.381.43-.189.204-.187.348-.072.573.415.804.801 1.621 1.222 2.421.227.433.237.83-.012 1.255a8.312 8.312 0 00-.832 1.999c-.136.501-.443.772-.932.92-.846.257-1.68.553-2.518.834-.19.064-.338.116-.362.39-.137 1.558-.149 1.558 1.319 2.05.19.064.374.156.569.194 1.183.228 1.742.936 2.283 2.05.73 1.502.531 2.643-.248 3.958-.734 1.237-.652 1.269.38 2.291.253.25.431.247.718.094.782-.415 1.582-.791 2.37-1.196.386-.199.76-.233 1.146-.012.684.39 1.413.68 2.167.9.43.126.67.4.802.817.269.843.565 1.676.832 2.518.094.299.206.447.573.45 1.456 0 1.456.017 1.915-1.35.058-.172.134-.34.166-.517.213-1.223.956-1.776 2.093-2.317 1.477-.706 2.602-.523 3.899.246 1.307.774 1.341.696 2.382-.43.186-.203.198-.344.078-.572-.427-.82-.826-1.654-1.251-2.476-.203-.39-.215-.758.006-1.144.379-.666.66-1.377.876-2.11.134-.45.405-.71.85-.847.691-.215 1.359-.513 2.06-.676.714-.164 1.071-.479.872-1.13z"
        fill="#3E384D"
      />
      <path
        d="M55.003 29.868c-.531-.024-1.066-.002-1.584-.159-.002-.54-.008-1.082-.008-1.623-.002-1.662 0-1.65-1.686-1.62-.364.006-.487.104-.479.475.018.92-.004 1.842-.01 2.765-.493.166-1.01.124-1.517.156-.319.006-.387-.172-.381-.46.014-.733.016-1.469-.002-2.202-.006-.243.118-.521-.128-.726-.866-.002-1.732.002-2.596-.008-.742-.008-1.115-.365-1.119-1.114-.012-2.073-.01-4.146 0-6.22.004-.749.377-1.126 1.115-1.128 3.805-.006 7.61-.006 11.414 0 .706.002 1.096.375 1.1 1.075a475.22 475.22 0 01-.002 6.34c-.003.682-.382 1.035-1.06 1.045-.886.014-1.772.004-2.656.006-.21.142-.132.36-.132.547-.008.778-.002 1.556-.004 2.332-.004.216.052.463-.265.519zm-2.65-9.929c-1.407 0-2.817.018-4.224-.01-.499-.01-.75.05-.731.662.017.545.144.723.703.693.902-.048 1.81-.016 2.714-.012.756.004 1.193.373 1.183.982-.01.59-.433.942-1.154.948-.987.006-1.973.03-2.957-.01-.522-.02-.5.277-.46.606.037.3-.224.75.44.744 2.956-.02 5.913-.016 8.87 0 .387.002.47-.13.463-.488-.018-1.206-.022-2.413.002-3.62.008-.395-.106-.513-.505-.507-1.447.026-2.897.012-4.344.012zM68.566 36.022l-1.47-1.23c1.005-1.013 2.004-2.034 3.016-3.036.507-.5 1.024-.5 1.53-.004.932.916 1.856 1.838 2.772 2.773.49.5.49 1.026-.008 1.53-1.005 1.011-2.023 2.01-3.037 3.014l-1.231-1.473c.373-.367.758-.724 1.114-1.105.381-.407 1.111-.758 1.065-1.219-.042-.439-.724-.8-1.084-1.228-.275-.327-.446-.25-.702.018-.64.667-1.31 1.309-1.965 1.96zM34.476 37.59l-1.233 1.47c-1.015-1.007-2.039-2.006-3.043-3.022-.5-.507-.485-1.012.026-1.531.904-.918 1.816-1.83 2.734-2.735.52-.51 1.027-.527 1.534-.028 1.018 1.005 2.016 2.027 3.023 3.043l-1.47 1.235c-.352-.359-.693-.73-1.062-1.07-.425-.391-.78-1.151-1.261-1.1-.457.047-.834.743-1.269 1.128-.263.232-.242.385.004.62.686.649 1.345 1.324 2.017 1.99z"
        fill="#AA2556"
      />
      <path
        d="M49.208 26.476c.246.204.122.483.128.726.018.733.016 1.47.002 2.203-.006.287.062.463.38.459-.152.124-.326.104-.5.072l-.01-3.46zM55.003 29.868c.315-.054.26-.3.26-.513.003-.778-.003-1.556.005-2.332.002-.188-.077-.407.132-.547l-.014 3.46c-.136.03-.269.032-.383-.068zM51.36 49.182v-4.284c.003-.79.323-1.222.913-1.246.6-.024 1.01.447 1.014 1.234.012 2.675.012 5.35-.004 8.025-.002.395.11.676.395.948.798.766 1.574 1.554 2.351 2.344.536.543.572 1.108.12 1.531-.434.41-.962.359-1.487-.16-.958-.948-1.9-1.91-2.862-2.855a1.382 1.382 0 01-.441-1.072c.01-1.488.002-2.977.002-4.465z"
        fill="#AA2556"
      />
      <path
        d="M38.39 62.613a6.555 6.555 0 01-6.56 6.558 6.547 6.547 0 01-6.574-6.602 6.55 6.55 0 016.588-6.529 6.557 6.557 0 016.547 6.573zm-11.201.004a4.613 4.613 0 004.643 4.623 4.617 4.617 0 004.626-4.643 4.614 4.614 0 00-4.646-4.62 4.606 4.606 0 00-4.623 4.64z"
        fill="#3E384D"
      />
      <defs>
        <radialGradient
          id="paint0_radial_48_289"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="matrix(0 50 -50 0 50.456 50)"
        >
          <stop stopColor="#fff" />
          <stop offset={1} stopColor="#FAFAFA" />
        </radialGradient>
      </defs>
    </svg>
  );
}

export function ServicesTherapySessionsIcon({ className }: { className?: string }) {
  return (
    <svg
      width={101}
      height={100}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect
        x={0.456}
        width={100}
        height={100}
        rx={50}
        fill="url(#paint0_radial_48_301)"
      />
      <path
        d="M43.43 62.035c.55 1.003.602 1.971.61 2.956.032 3.679.104 7.358.108 11.034.002 2.852-2.78 4.752-5.35 3.665-1.599-.677-2.404-1.942-2.413-3.683-.01-2.093-.023-4.187.01-6.28.008-.547-.1-.739-.697-.717-1.875.07-3.755.088-5.634.088-.452 0-.552.144-.549.57.017 3.013.01 6.027.008 9.041 0 .196.009.394-.02.587-.056.4-.304.63-.7.672-.42.042-.735-.138-.853-.552-.062-.22-.066-.463-.066-.695-.004-3.014-.01-6.026.006-9.04.002-.405-.064-.565-.528-.587-2.85-.146-4.874-2.261-4.888-5.124-.02-4.207-.03-8.414.004-12.62.018-2.316 1.195-3.9 3.369-4.848-2.39-1.888-3.569-4.321-3.229-7.366.237-2.117 1.247-3.85 2.915-5.18 3.244-2.585 7.923-2.149 10.7.95 2.72 3.035 2.88 8.367-1.262 11.564.013.03.017.08.039.088 2.225.956 3.288 2.662 3.278 5.065-.004.725.018 1.45-.008 2.172-.014.368.118.464.47.458 1.115-.018 2.23-.014 3.347-.004 3.14.026 4.955 2.902 3.61 5.728-.164.347-.12.43.258.429 3.11-.008 6.223-.008 9.333 0 .378 0 .42-.084.256-.43-1.345-2.829.47-5.703 3.611-5.729 1.095-.01 2.192-.018 3.286.006.405.01.55-.098.53-.518-.033-.682-.01-1.369-.007-2.053.002-2.526 1.019-4.102 3.388-5.182-2.355-1.84-3.522-4.225-3.256-7.203.184-2.054 1.113-3.77 2.672-5.124 3.146-2.736 7.863-2.482 10.774.522a7.917 7.917 0 01-1.08 11.793c.035.146.181.126.273.168 1.937.913 3.002 2.45 3.028 4.573.052 4.285.046 8.57.006 12.856-.026 2.768-2.065 4.855-4.823 5-.513.025-.593.193-.589.65.018 2.994.012 5.986.008 8.98 0 .232-.004.474-.066.695-.118.414-.432.594-.852.552-.397-.04-.645-.272-.699-.672-.026-.193-.02-.39-.02-.587-.002-3.014-.008-6.026.008-9.04.002-.427-.098-.57-.55-.569-1.762.002-3.52-.04-5.282-.068-1.033-.016-1.033-.016-1.033.99-.004 2.094.046 4.19-.026 6.281-.094 2.682-2.836 4.422-5.304 3.397-1.559-.649-2.425-1.878-2.428-3.557-.003-3.99.07-7.982.125-11.972.01-.709.174-1.392.528-2.014-.19-.184-.394-.114-.574-.116-1.704-.006-3.405.014-5.106-.016-.502-.008-.655.108-.653.638.022 5.342.014 10.684.014 16.026 0 .196.018.396-.016.586-.078.44-.35.699-.806.697-.456 0-.727-.26-.805-.7-.034-.19-.016-.39-.016-.585 0-5.342-.01-10.684.014-16.026.002-.536-.16-.644-.656-.636-1.882.032-3.757.016-5.742.016zm33.718-4.351V51.46v-.118c-.042-2-1.475-3.438-3.465-3.452-1.859-.014-3.716-.012-5.576 0-2.027.012-3.472 1.455-3.492 3.48-.012 1.135 0 2.27-.004 3.405-.002.87-.24 1.112-1.113 1.116a785.94 785.94 0 01-4.343 0c-.629-.002-1.199.153-1.677.579a2.247 2.247 0 00-.615 2.464c.349.928 1.163 1.463 2.316 1.467 2.954.01 5.908.006 8.864.002.723 0 .811-.092.811-.815.002-2.053 0-4.109.002-6.162 0-.843.268-1.225.84-1.22.559.007.801.36.801 1.184.002 2.053.002 4.109 0 6.162-.002 1.701-.772 2.48-2.464 2.482-2.425.002-4.853.028-7.279-.016-.716-.014-1.133.302-1.483.835a2.45 2.45 0 00-.386 1.335c-.052 3.97-.1 7.943-.136 11.914a2.113 2.113 0 00.704 1.621c.7.645 1.52.8 2.394.445.9-.369 1.377-1.083 1.385-2.058.016-2.151.008-4.305.008-6.457 0-1.765.548-2.291 2.334-2.27 2.621.035 5.241.073 7.863.085 2.296.012 3.707-1.4 3.713-3.68.002-2.036-.002-4.072-.002-6.105zM24.11 57.63c0 2.055.002 4.109-.002 6.164 0 .453.04.899.18 1.331.472 1.445 1.735 2.35 3.362 2.344 2.738-.01 5.478-.052 8.216-.084 1.54-.018 2.15.57 2.152 2.1.002 2.133.004 4.264-.002 6.398-.002.583.098 1.127.47 1.597a2.265 2.265 0 002.476.759 2.215 2.215 0 001.543-2.138c-.028-3.97-.08-7.944-.13-11.914a2.908 2.908 0 00-.094-.639c-.236-.997-.887-1.511-1.902-1.511h-7.103c-1.751 0-2.516-.757-2.518-2.488-.002-1.467 0-2.934 0-4.403 0-.705-.006-1.409.006-2.114.008-.478.25-.774.74-.82.461-.044.811.256.882.748.024.172.014.353.014.529 0 2.035-.002 4.069.002 6.104 0 .713.094.809.814.809 2.934.004 5.87.002 8.805 0 .352 0 .702-.03 1.038-.154a2.246 2.246 0 001.447-2.476c-.174-1.067-1.084-1.855-2.235-1.873-1.545-.025-3.093 0-4.638-.01-.718-.005-.984-.283-.988-1.003-.006-1.155.004-2.31-.004-3.463-.016-2.1-1.451-3.526-3.559-3.534-1.781-.006-3.56-.004-5.342 0-2.211.004-3.628 1.419-3.634 3.636 0 2.036.004 4.07.004 6.105zm46.772-11.387c3.416 0 6.166-2.73 6.172-6.126.006-3.426-2.76-6.206-6.175-6.204-3.416 0-6.176 2.778-6.17 6.208.008 3.4 2.752 6.122 6.172 6.122zm-40.514 0c3.425 0 6.169-2.713 6.18-6.116.013-3.428-2.747-6.212-6.162-6.216-3.414-.004-6.182 2.77-6.182 6.196a6.138 6.138 0 006.164 6.136z"
        fill="#3E384D"
      />
      <path
        d="M50.629 20h11.504c1 0 1.197.2 1.197 1.207 0 4.756.002 9.51 0 14.265 0 .964-.21 1.183-1.167 1.185-2.073.004-4.149.012-6.222-.008-.447-.004-.609.102-.6.582.027 2.015.007 4.03.013 6.046.002.435-.044.837-.506 1.023-.456.186-.777-.056-1.085-.364-2.292-2.302-4.593-4.593-6.88-6.9-.27-.27-.537-.39-.924-.385-2.27.016-4.539.01-6.809.008-1.034-.002-1.224-.197-1.224-1.245V21.209c0-1.008.194-1.206 1.194-1.206C42.96 19.998 46.794 20 50.63 20zm3.082 21.524c0-1.881-.002-3.6 0-5.318.002-.94.246-1.189 1.179-1.19 2.073-.005 4.147-.015 6.218.007.459.004.593-.12.59-.586-.017-4.067-.017-8.136 0-12.205.003-.494-.137-.616-.622-.614-6.963.016-13.926.014-20.887 0-.47 0-.636.098-.632.606.022 4.067.018 8.136.002 12.205-.002.45.106.602.58.596 2.112-.026 4.225-.002 6.337-.018a1.54 1.54 0 011.195.497c1.813 1.837 3.644 3.656 5.47 5.482.146.148.302.286.57.538z"
        fill="#AA2556"
      />
      <path
        d="M50.605 31.093c-2.094 0-4.187-.005-6.279.002-.37.002-.704-.048-.922-.389-.359-.564.036-1.233.766-1.25.86-.023 1.721-.007 2.582-.007h9.917c.156 0 .314-.006.47.004.503.032.803.3.845.8.038.461-.336.825-.864.834-1.155.016-2.308.006-3.463.006h-3.052zM50.627 26.143c2.093 0 4.185-.002 6.278 0 .69 0 1.075.286 1.087.79.012.529-.392.843-1.109.845-4.165.002-8.332.002-12.497 0-.7 0-1.077-.27-1.11-.778-.035-.5.346-.853.958-.855 2.13-.006 4.261-.002 6.393-.002z"
        fill="#AA2556"
      />
      <defs>
        <radialGradient
          id="paint0_radial_48_301"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="matrix(0 50 -50 0 50.456 50)"
        >
          <stop stopColor="#fff" />
          <stop offset={1} stopColor="#FAFAFA" />
        </radialGradient>
      </defs>
    </svg>
  );
}

export function ServicesTreatmentProgramsIcon({ className }: { className?: string }) {
  return (
    <svg
      width={101}
      height={100}
      viewBox="0 0 101 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect
        x={0.456}
        width={100}
        height={100}
        rx={50}
        fill="url(#paint0_radial_48_295)"
      />
      <path
        d="M73.399 53.72c.008.228.027.453.027.68.002 7.132-.058 14.262.029 21.393.035 2.895-2.098 4.993-5.031 4.976-12.294-.062-24.587-.025-36.879-.027-3.056 0-4.935-1.87-4.935-4.906V28.403c0-3.016 1.867-4.88 4.885-4.883 1.934-.003 3.87-.007 5.804.004.343.002.562-.013.614-.457.073-.61.539-.838 1.126-.838.874 0 1.748-.017 2.62.008.36.01.47-.107.45-.455a15.5 15.5 0 01-.002-1.425c.025-.738.393-1.119 1.122-1.12 4.534-.007 9.07-.009 13.603 0 .728.001 1.082.388 1.095 1.133.008.475.025.951-.007 1.425-.023.366.12.45.462.442.831-.02 1.664-.01 2.496-.006.847.004 1.126.199 1.278 1.016.071.38.31.266.506.268 2.059.009 4.12-.012 6.178.01 2.273.026 4.003 1.418 4.494 3.558.098.428.088.863.088 1.295.006 2.583.008 5.166.012 7.75-.597 1.187-1.28 2.329-1.996 3.45-.004-.27-.013-.538-.013-.805 0-3.553.007-7.109-.004-10.662-.004-1.501-.984-2.567-2.435-2.594-2.142-.037-4.284-.004-6.426-.018-.36-.002-.402.153-.397.447.014.765.029 1.53-.005 2.293-.016.408.107.53.519.522 1.33-.025 2.662-.01 3.992-.009 1.251 0 1.493.242 1.495 1.48l.014 14.938c-.595 1.19-1.282 2.327-1.994 3.45-.006-.97-.016-1.942-.016-2.912-.003-4.794-.009-9.587.012-14.38.002-.501-.146-.62-.622-.603-.997.036-1.997.019-2.994.007-.279-.005-.391.058-.406.37-.045.966-.353 1.251-1.292 1.253-7.238.002-14.473.002-21.71 0-.908 0-1.243-.304-1.287-1.21-.017-.324-.108-.422-.43-.415-.956.018-1.916.037-2.87-.007-.555-.024-.726.116-.724.701.019 13.802.019 27.606-.002 41.407 0 .631.196.722.766.722 7.05-.019 14.099-.012 21.149-.012h9.856c.277 0 .57.095.566-.395-.027-2.726-.023-5.454-.03-8.18.734-.445 1.398-.954 1.722-1.79.059-.148.177-.273.269-.407.006 3.905.02 7.812.016 11.717 0 .703-.43 1.04-1.257 1.04-11.253.002-22.505.002-33.758 0-.922 0-1.278-.36-1.278-1.288-.002-14.737-.002-29.471 0-44.207 0-.94.335-1.27 1.276-1.275 1.371-.006 2.745-.026 4.117.01.512.015.635-.154.61-.628a20.323 20.323 0 010-2.108c.02-.438-.123-.558-.558-.552-1.934.023-3.87.004-5.803.01-1.926.007-2.906.995-2.906 2.917v47.431c0 1.949.984 2.925 2.953 2.925 12.315 0 24.627-.017 36.942.02 1.582.005 2.978-.961 2.953-2.953-.064-5.456-.02-10.912-.022-16.368 0-.763-.017-1.529-.028-2.292a45.363 45.363 0 012-3.448zm-23.38-23.348h9.606c.281 0 .569.085.562-.401-.027-1.777-.02-3.554-.004-5.33.004-.368-.129-.447-.466-.439a72.39 72.39 0 01-2.681.004c-.708-.01-1.07-.366-1.093-1.069a22.077 22.077 0 010-1.487c.01-.292-.033-.44-.393-.44-3.68.012-7.36.012-11.04.001-.34 0-.43.116-.413.43.023.474.012.95.004 1.426-.014.792-.352 1.133-1.136 1.14-.791.008-1.587.055-2.37-.015-.642-.058-.75.207-.74.748.03 1.612.062 3.223.019 4.832-.015.525.126.612.603.608 3.18-.019 6.361-.008 9.542-.008z"
        fill="#3E384D"
      />
      <path
        d="M68.147 62.765c-.092.134-.21.26-.269.407-.326.836-.988 1.345-1.72 1.79-1.691 1.05-3.386 2.095-5.07 3.153-.378.238-.76.348-1.171.137-.448-.23-.54-.635-.52-1.102.1-2.333.189-4.664.274-6.997.008-.218.077-.404.181-.584.824-1.41 1.657-2.817 2.475-4.23 1.293-2.237 2.577-4.479 3.865-6.719.712-1.123 1.397-2.26 1.995-3.45a104.15 104.15 0 003.257-5.59 39.521 39.521 0 001.996-3.45c.764-1.276 1.49-2.577 2.305-3.82 1.022-1.562 2.552-2.174 4.365-1.889 1.75.276 2.903 1.347 3.443 3.035.395 1.236.185 2.405-.458 3.513-2.608 4.489-5.219 8.977-7.823 13.465-.633 1.09-1.249 2.192-1.873 3.286a43.992 43.992 0 00-1.994 3.446 107.173 107.173 0 00-3.258 5.599zm10.866-22.76c.004-.208-.154-.248-.275-.318-1.132-.653-2.269-1.294-3.395-1.958-.283-.168-.42-.124-.577.159-.478.869-.976 1.727-1.475 2.583-3.67 6.306-7.34 12.614-11.02 18.914-.175.3-.169.437.154.613.945.515 1.871 1.067 2.806 1.605.932.537.93.537 1.461-.379 1.634-2.827 3.266-5.657 4.908-8.48 2.4-4.128 4.809-8.252 7.213-12.376.07-.124.14-.253.2-.362zm.379-7.66c-.804-.002-1.483.291-1.924.955-.48.72-.884 1.494-1.334 2.236-.127.21-.092.323.119.44 1.174.663 2.343 1.33 3.51 2.007.216.126.363.145.5-.103.423-.76.919-1.483 1.275-2.27.707-1.559-.438-3.259-2.146-3.265zM61.574 65.46c.97-.614 1.954-1.239 2.99-1.897-.93-.538-1.791-1.032-2.651-1.532-.215-.124-.344-.124-.344.173.007 1.072.005 2.141.005 3.256z"
        fill="url(#paint1_linear_48_295)"
      />
      <path
        d="M54.299 44.36c1.848.492 3.299 1.447 4.2 3.116.348.644.568 1.33.57 2.069.007 2.004.02 4.008.009 6.01-.002.67-.294.962-.972 1.053a4.786 4.786 0 01-.623.03c-4.99.003-9.979.003-14.968 0-.167 0-.333.003-.498-.016-.745-.083-1.06-.424-1.063-1.18-.007-1.736-.004-3.471-.002-5.207.002-2.65 1.407-4.657 3.917-5.59.27-.102.55-.184.895-.298-1.317-1.572-1.825-3.34-1.326-5.338.333-1.336 1.078-2.404 2.196-3.208 2.298-1.65 5.431-1.43 7.43.523 1.84 1.8 2.655 5.068.235 8.035zm-4.305 10.3c2.16 0 4.323-.015 6.484.01.502.006.633-.151.622-.631-.03-1.239-.01-2.478-.01-3.717-.002-2.258-1.334-3.816-3.595-4.056-2.171-.23-4.354-.057-6.534-.074a3.15 3.15 0 00-.678.093c-1.786.379-3.477 1.777-3.348 4 .073 1.255.04 2.52 0 3.779-.016.515.167.61.637.606 2.14-.023 4.28-.01 6.422-.01zm.052-10.495c2.094 0 3.757-1.644 3.761-3.715.005-2.095-1.686-3.764-3.8-3.756-2.1.009-3.801 1.717-3.779 3.796.025 2.056 1.705 3.675 3.818 3.675z"
        fill="url(#paint2_linear_48_295)"
      />
      <path
        d="M47.817 68.372h-8.674c-.23 0-.462.012-.685-.025-.495-.083-.768-.407-.803-.891-.034-.455.178-.797.616-.977.258-.105.528-.09.799-.09h17.597c.271 0 .544-.017.802.086.464.19.668.546.616 1.032-.05.464-.325.755-.785.838-.223.04-.456.027-.685.027h-8.798zM47.87 62.502h8.862c.187 0 .379-.018.56.019.454.09.739.37.793.835.054.457-.13.805-.55 1.016-.252.128-.524.108-.795.108-5.907 0-11.815 0-17.722-.003-.187 0-.381.015-.558-.026-.535-.127-.837-.478-.803-1.024.033-.54.358-.867.916-.919.185-.016.374-.006.562-.006h8.736z"
        fill="#3E384D"
      />
      <defs>
        <radialGradient
          id="paint0_radial_48_295"
          cx={0}
          cy={0}
          r={1}
          gradientUnits="userSpaceOnUse"
          gradientTransform="matrix(0 50 -50 0 50.456 50)"
        >
          <stop stopColor="#fff" />
          <stop offset={1} stopColor="#FAFAFA" />
        </radialGradient>
        <linearGradient
          id="paint1_linear_48_295"
          x1={55.181}
          y1={19.231}
          x2={55.181}
          y2={80.769}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={1} stopColor="#4E0971" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_48_295"
          x1={55.181}
          y1={19.231}
          x2={55.181}
          y2={80.769}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={1} stopColor="#4E0971" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export function QuoteMyStory({ className }: { className?: string }) {
  return (
    <svg
      width={449}
      height={398}
      viewBox="0 0 449 398"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g filter="url(#Quote_a)">
        <path
          d="m337.118 24.388 13.644 17.783 13.809 17.589 13.988 17.424c4.632 5.827 9.398 11.543 14.11 17.283 4.739 5.728 9.426 11.531 14.208 17.205 9.178 10.827 18.233 21.795 27.498 32.515.14 13.119.122 26.24.441 39.367l.402 20.737.56 20.736c.199 6.901.389 13.823.701 20.726l.86 20.725c.357 6.9.641 13.815 1.084 20.729.378 6.909.867 13.82 1.376 20.74.466-6.918.904-13.823 1.258-20.743.391-6.902.632-13.823.957-20.731l.733-20.735c.26-6.913.407-13.829.574-20.735l.46-20.737.275-20.723c.247-13.828.149-27.64.218-41.475l.019-2.032-1.313-1.583c-9.541-11.411-19.346-22.55-28.982-33.832-4.846-5.63-9.797-11.16-14.686-16.735-4.917-5.563-9.778-11.15-14.766-16.648l-14.907-16.516-15.066-16.343-15.334-16.12c-5.118-5.336-10.255-10.68-15.508-15.906 4.376 6.08 8.878 12.068 13.387 18.035z"
          fill="url(#Quote_b)"
        />
        <path
          d="M433.326 303.762c1.184-28.667 2.349-57.343 3.534-86.011.776-18.921 1.544-37.821 2.32-56.742.156-3.789 2.775-16.361.333-19.591L336.101 5.41c-1.049-1.388-4.011 1.095-2.973 2.454 34.307 45.114 68.614 90.228 102.913 135.363 1.71 2.255.048 14.06-.161 18.857-.776 18.921-1.544 37.821-2.32 56.742-1.184 28.668-2.349 57.344-3.534 86.011-.114 2.161 3.219.535 3.3-1.075z"
          fill="url(#Quote_c)"
        />
        <path
          d="M447.027 310.483c-.735-27.699-1.478-55.378-2.213-83.077-.5-18.791-1.008-37.561-1.497-56.322-.103-3.759 1.893-21.558-.743-24.408-36.546-39.473-73.092-78.946-109.657-118.428-.66-.695-2.113.535-1.492 1.212 36.496 39.426 72.992 78.851 109.508 118.286 2.044 2.211.571 19.831.679 23.814.5 18.791.989 37.551 1.489 56.342.735 27.7 1.478 55.378 2.213 83.078.039 1.124 1.74.425 1.713-.497z"
          fill="url(#Quote_d)"
        />
      </g>
      <g filter="url(#Quote_e)">
        <path
          d="m11.88 99.55.742 23.112.497 23.086.264 23.091c.123 7.69.077 15.393.087 23.075-.019 7.693.048 15.404-.036 23.088-.198 14.7-.218 29.416-.548 44.098 7.954 9.893 16.027 19.671 23.852 29.698l12.448 15.746 12.319 15.879c4.09 5.294 8.2 10.597 12.217 15.953l12.09 16.087c3.97 5.408 8.007 10.774 11.923 16.253 3.963 5.428 7.842 10.939 11.722 16.45-4.597-4.826-9.165-9.664-13.68-14.573-4.55-4.878-8.963-9.861-13.449-14.781l-13.284-14.947c-4.44-4.972-8.798-10.027-13.165-15.061L42.797 310.66l-12.934-15.268c-8.679-10.141-17.09-20.52-25.64-30.795l-1.263-1.503.008-2.129c.111-15.397.587-30.782.857-46.172.158-7.696.456-15.394.68-23.081.252-7.698.43-15.384.794-23.074l.97-23.057 1.206-23.062 1.514-23.028c.551-7.694 1.093-15.368 1.804-23.055.459 7.713.787 15.408 1.087 23.114z"
          fill="url(#Quote_f)"
        />
        <path
          d="M111.71 377.445c-18.493-20.54-36.978-41.101-55.471-61.642-12.202-13.561-24.385-27.113-36.587-40.674-2.444-2.714-12.122-10.208-12.273-14.401C5.256 202.033 3.133 143.338 1 84.663c-.07-1.793 3.666-2.076 3.726-.313 2.109 58.411 4.217 116.823 6.344 175.245.11 2.916 8.601 10.527 11.704 13.957 12.202 13.561 24.385 27.113 36.587 40.674 18.493 20.541 36.978 41.102 55.471 61.642 1.385 1.541-2.082 2.741-3.123 1.577z"
          fill="url(#Quote_g)"
        />
        <path
          d="M105.586 392.354c-16.467-21.203-32.915-42.395-49.381-63.598-11.17-14.384-22.32-28.758-33.498-43.122-2.233-2.881-14.655-14.725-14.44-18.753 3.028-55.816 6.055-111.632 9.073-167.479.066-.995 1.906-1.125 1.858-.172-3.02 55.745-6.039 111.491-9.067 167.256-.167 3.125 11.755 15.213 14.121 18.263 11.17 14.385 22.33 28.739 33.48 43.113 16.466 21.202 32.914 42.395 49.38 63.597.718.897-.961 1.612-1.526.895z"
          fill="url(#Quote_h)"
        />
      </g>
      <g filter="url(#Quote_i)">
        <path
          d="M72 34.3c4.1-9.13 10.1-14.206 18.046-15.18h.126c.167 0 .25.113.25.34v.136a.438.438 0 0 1-.25.407c-1.778.499-3.074.907-3.91 1.27C77.98 24.851 72.69 32.011 70.39 42.773c-.628 3.014-.941 5.914-.941 8.746 0 1.971.146 3.874.439 5.71 1.045 6.457 2.927 10.626 5.645 12.507 1.778 1.291 3.66 1.948 5.646 1.948 1.903 0 3.931-.612 6.085-1.813 4.308-2.379 6.587-5.823 6.88-10.354.041-.453.062-.884.062-1.269 0-5.053-2.132-8.587-6.377-10.558-1.861-.861-3.618-1.269-5.27-1.269-2.613 0-4.976 1.065-7.13 3.217-.167.136-.335.204-.502.204-.167 0-.334-.068-.502-.204a.95.95 0 0 1-.188-.544c0-.226.063-.43.188-.611.168-.181.314-.272.44-.272.167 0 .334.09.501.272.167.181.251.362.251.544a.95.95 0 0 1-.188.543v.068c-.251-.272-.586-.657-1.004-1.133 2.321-2.333 4.956-3.489 7.946-3.489 1.443 0 2.948.272 4.538.793 4.955 1.654 7.8 5.008 8.49 10.083.083.77.125 1.495.125 2.22 0 3.127-.92 6.05-2.781 8.746-2.321 3.217-5.332 5.189-9.055 5.914-.92.18-1.798.272-2.676.272-2.614 0-4.81-.794-6.629-2.425-2.488-2.152-4.244-5.778-5.269-10.875a40.031 40.031 0 0 1-.878-8.27C68.216 45.652 69.49 39.942 72 34.3zm5.333 27.076c-5.332-5.189-8.176-8.043-8.49-8.542-.042-.045-.063-.09-.063-.136 0-.09.042-.159.126-.204a.454.454 0 0 1 .188-.068c.042 0 .084.023.126.068 1.024 1.246 2.509 2.764 4.412 4.509 2.362 2.153 3.889 3.557 4.579 4.237.92.997 2.154 2.583 3.722 4.78 1.526 2.199 2.76 3.762 3.722 4.69.084.091.126.182.126.273 0 .045-.042.113-.126.204-.042.045-.105.068-.188.068a.522.522 0 0 1-.251-.068c-1.045-.952-2.342-2.56-3.91-4.826-1.652-2.38-2.97-4.034-3.973-4.985zm-5.207-9.947c-1.735-1.835-2.635-3.308-2.676-4.44v-.069c0-.045.062-.09.188-.136.125 0 .188.068.188.204.335 1.337 1.59 3.06 3.785 5.098.522.499 1.4 1.247 2.593 2.289 1.15.997 2.028 1.744 2.613 2.288 1.401 1.292 3.053 3.24 4.956 5.846 1.192 1.744 3.074 4.237 5.646 7.454a.219.219 0 0 1 .125.204c0 .045-.02.09-.062.136-.042.09-.105.136-.189.136-.041 0-.083-.023-.125-.068-.878-.634-2.74-2.968-5.646-7.046-2.154-3.037-4.015-5.257-5.583-6.662-3.408-2.99-5.353-4.735-5.813-5.234zm-.376 10.559c-.983-1.292-1.506-1.949-1.548-1.949-.083 0-.125-.045-.125-.136v-.068c0-.136.063-.204.188-.204.21 0 .69.453 1.485 1.337l3.408 4.101c1.945 2.424 3.158 3.92 3.66 4.509 1.15 1.473 1.777 2.311 1.86 2.492v.068c0 .09-.041.181-.125.272-.042.045-.105.068-.188.068-.042 0-.084-.023-.126-.068-.041-.045-.669-.838-1.923-2.356a226.222 226.222 0 0 0-3.158-3.965c-1.652-2.017-2.78-3.399-3.408-4.101zm1.129-15.996c-1.234-1.745-2.05-2.787-2.426-3.082a.219.219 0 0 1-.125-.204c0-.045.02-.09.063-.136.041-.045.104-.068.188-.068h.125c.544.408 1.318 1.292 2.363 2.628.67.861 1.631 1.858 2.928 3.014.041.045.062.113.062.204v.204c-.042.09-.104.136-.188.136-.042 0-.084-.023-.125-.068-.92-.363-1.882-1.246-2.865-2.628zm4.893-1.201c-2.572-2.107-4.621-4.35-6.21-6.73-.042-.045-.063-.113-.063-.204 0-.045.042-.113.125-.203.084-.091.168-.136.251-.136.042 0 .084.045.126.136.585.86 1.589 1.97 3.032 3.353 1.442 1.382 2.488 2.492 3.094 3.285.042.045.063.113.063.204 0 .045-.02.113-.063.204-.041.09-.104.136-.188.136-.02.023-.083 0-.167-.045zm-1.234-6.118c-1.233-1.382-2.384-2.515-3.408-3.353a.554.554 0 0 1-.063-.204c0-.09.021-.181.063-.272.084-.09.167-.136.25-.136.043 0 .084.023.126.068a41.837 41.837 0 0 1 3.471 3.421c1.82 2.107 2.865 3.399 3.158 3.897.042.046.063.091.063.136 0 .046-.042.09-.126.136-.042.09-.084.136-.125.136-.042 0-.084-.023-.126-.068 0 .046-1.087-1.223-3.283-3.76zm6.629 18.602c-3.973-3.444-6.65-6.548-8.009-9.267-.042-.045-.063-.09-.063-.136 0-.09.042-.159.126-.204h.125c.042 0 .084.023.126.068 1.819 2.787 4.81 5.982 8.928 9.607 3.973 3.534 6.608 6.367 7.925 8.474.042.045.063.113.063.204 0 .09-.063.181-.188.272-.042.045-.105.067-.188.067-.084 0-.168-.067-.251-.203-1.338-2.062-4.203-5.03-8.594-8.882zM78.21 34.64a59.564 59.564 0 0 1-2.656-3.761c-.041-.045-.062-.09-.062-.136a.22.22 0 0 1 .125-.204c.084-.09.167-.136.251-.136.042 0 .084.045.126.136 1.275 1.745 2.3 3.059 3.031 3.965 1.736 2.198 2.614 3.285 2.677 3.285.042.046.063.091.063.136 0 .046-.042.091-.126.136-.041.091-.083.136-.125.136-.042 0-.084-.022-.126-.068-.69-.498-1.735-1.654-3.178-3.489zm-.376-7.409c.209.181.439.43.69.748.041.045.062.09.062.136l-.062.068c-.042.045-.084.068-.126.068-.042 0-.083-.023-.125-.068a33.1 33.1 0 0 1-.69-.476l.25-.408c.293.363.712.929 1.234 1.745.502.793.9 1.404 1.192 1.812.293.363.711.861 1.297 1.473.627.68 1.045 1.178 1.317 1.54.042.046.063.114.063.205 0 .045-.021.09-.063.136-.042.045-.105.068-.188.068h-.126c-.71-.363-1.63-1.36-2.802-3.037a89.225 89.225 0 0 1-2.362-3.693c-.042-.045-.063-.09-.063-.136 0-.09.042-.158.125-.204a.456.456 0 0 1 .188-.068c.084.023.147.068.189.091zm16.31-3.557c.041.045.062.09.062.136 0 .045-.041.09-.125.136-.21.136-.565.249-1.046.34-.543.09-.92.203-1.108.34a9.96 9.96 0 0 0-2.488 1.88c-1.778 1.835-3.43 4.124-4.956 6.865-1.652 2.832-2.844 5.37-3.534 7.658-.418 1.315-.669 2.289-.732 2.969-.041.951-.125 1.472-.25 1.608a.41.41 0 0 1-.314.136.456.456 0 0 1-.188-.068c-.335-.226-.502-.657-.502-1.337 0-.974.376-2.47 1.108-4.44 1.13-3.036 2.51-5.891 4.161-8.542 2.07-3.218 3.973-5.506 5.709-6.866 1.317-.974 2.363-1.472 3.094-1.472.502-.023.858.226 1.109.657zM86.22 54.919c-3.095-3.127-5.353-5.891-6.754-8.27-.042-.046-.063-.091-.063-.136 0-.091.042-.159.125-.204a.454.454 0 0 1 .189-.068c.041 0 .083.022.125.068 1.36 1.926 3.743 4.6 7.13 7.998 3.304 3.353 5.667 6.027 7.068 7.998.042.045.063.09.063.136 0 .09-.042.181-.126.272a.523.523 0 0 1-.25.068.456.456 0 0 1-.189-.068c-1.003-1.201-3.45-3.784-7.318-7.794zm-6.148-30.294c-.084-.09-.125-.158-.125-.203 0-.091.041-.16.125-.204.084-.091.146-.136.188-.136.084 0 .147.045.188.136.837.77 1.59 1.835 2.3 3.217v.136c0 .09-.02.159-.062.204-.042.045-.084.068-.126.068-.041 0-.083-.023-.125-.068-1.61-2.062-2.426-3.127-2.426-3.172l.063.022zm1.924 3.354c-.377-.453-.69-.839-.92-1.133a.554.554 0 0 1-.063-.204c0-.136.042-.25.125-.34.084-.09.147-.136.189-.136.083 0 .167.046.25.136 2.322 2.878 3.534 4.418 3.66 4.645v.136c0 .09-.021.158-.063.204-.042.045-.104.068-.188.068-.042 0-.084-.023-.126-.068-.041-.046-.669-.77-1.86-2.153-.23-.249-.544-.611-1.004-1.155zm.732-5.914c.209.272.502.612.878.997.042.045.063.113.063.204 0 .045-.021.09-.063.136-.042.045-.084.068-.126.068-.041 0-.083-.023-.125-.068-.376-.363-.711-.68-.983-.952l.356-.385c.041 0 .815.93 2.3 2.764.25.318 1.066 1.156 2.488 2.56a.219.219 0 0 1 .126.205c0 .045-.042.113-.126.203-.042.091-.105.136-.188.136a.455.455 0 0 1-.188-.067c.041 0-1.548-1.79-4.789-5.37a.552.552 0 0 1-.063-.204c0-.09.021-.159.063-.204.084-.09.147-.136.188-.136.084-.023.147.022.189.113zm6.398 30.769c-2.32-2.65-3.826-4.486-4.537-5.506-.042-.045-.063-.09-.063-.136 0-.09.042-.158.125-.204.084-.09.168-.136.251-.136.042 0 .084.046.126.136 5.792 7.523 8.74 11.284 8.866 11.284v.068c0 .136-.063.226-.189.272-.041.045-.104.068-.188.068-.083 0-.146-.068-.188-.204-.899-1.541-2.3-3.444-4.203-5.642zm-.314-28.956c-.878-.884-1.589-1.541-2.174-1.949-.084-.09-.126-.181-.126-.272 0-.09.021-.181.063-.272.084-.136.167-.204.251-.204.084 0 .146.023.188.069.711.543 1.464 1.223 2.3 2.084 1.109 1.178 1.757 1.858 1.924 2.085.042.045.063.09.063.135 0 .091-.042.159-.126.204-.041.09-.104.136-.188.136-.042 0-.083-.022-.125-.068-.293-.136-.962-.77-2.05-1.948zm1.862-4.645c.041.045.083.068.125.068a.219.219 0 0 1 .125.204c0 .045-.02.09-.062.136-.042.09-.126.136-.251.136h-.063c0-.046-.042-.068-.125-.068.125-.181.23-.317.313-.408v.068c-.041-.045-.167-.113-.376-.204.125 0 .376.25.753.748.125.09.502.52 1.108 1.268a153.69 153.69 0 0 1 1.798 2.289c.084.136.126.25.126.34 0 .181-.105.317-.314.408a.64.64 0 0 1-.376.136c-.168 0-.293-.091-.377-.272-.25-.363-.794-1.02-1.61-2.017-.71-.86-1.17-1.563-1.422-2.152a1.202 1.202 0 0 1-.063-.34c0-.09.042-.181.126-.272.084-.136.188-.204.314-.204.041 0 .104.023.188.068 0 .045.02.068.063.068zm18.171 8.814c3.596-4.622 7.674-7.659 12.211-9.131a.46.46 0 0 1 .188-.068c.168 0 .272.113.314.34v.136c0 .18-.105.34-.314.475-9.347 4.124-15.369 11.578-18.045 22.386-.816 3.24-1.234 6.321-1.234 9.267 0 1.518.126 3.013.376 4.509.9 5.279 3.283 8.859 7.131 10.762 1.359.68 2.697.997 4.036.997 2.613 0 5.185-1.291 7.757-3.897 3.011-2.99 4.517-6.254 4.517-9.743 0-.951-.126-1.926-.377-2.945-.543-2.243-1.672-4.079-3.408-5.529-1.652-1.337-3.471-2.016-5.457-2.016h-.377c-5.541.362-8.343 1.495-8.427 3.421 0 .544-.23.793-.69.793-.502 0-.752-.272-.752-.793.041-1.971 1.861-3.376 5.457-4.237 1.61-.408 3.095-.612 4.475-.612 2.446 0 4.537.657 6.252 1.949 2.969 2.334 4.475 5.28 4.475 8.859 0 .77-.063 1.563-.188 2.424-.753 4.532-2.907 8.225-6.462 11.08-2.279 1.835-4.537 2.764-6.816 2.764-1.569 0-3.137-.453-4.726-1.337-3.973-2.243-6.482-5.959-7.57-11.17a27.046 27.046 0 0 1-.564-5.574c0-2.786.397-5.687 1.171-8.745a38.976 38.976 0 0 1 7.047-14.365zm-4.893 29.364c-1.276-1.428-1.987-2.425-2.112-2.968v-.25c0-.136.021-.203.062-.203.084 0 .189.067.314.203.335.363.795.93 1.359 1.677.669.861 1.129 1.428 1.422 1.745a63.181 63.181 0 0 0 3.471 3.489c.439.453 1.088 1.02 1.924 1.745.962.86 1.589 1.427 1.924 1.744 1.401 1.246 2.258 2.22 2.53 2.878.042.045.063.09.063.136 0 .09-.042.181-.126.272a.456.456 0 0 1-.188.068c-.084 0-.146-.023-.188-.068-.711-.952-3.011-3.195-6.943-6.798-1.254-1.178-2.446-2.401-3.512-3.67zm-1.673-13.572a.96.96 0 0 1 .313.068c.042 0 .063.023.063.068 0 .045-.021.09-.063.136 0 .045-.021.068-.062.068h-.063c.502.181 1.568 1.133 3.22 2.832 2.572 2.56 4.517 4.6 5.897 6.118.983 1.133 2.676 3.194 5.081 6.253 2.154 2.651 3.889 4.69 5.207 6.118a.22.22 0 0 1 .125.204c0 .045-.042.113-.125.203-.042.046-.105.068-.189.068a.461.461 0 0 1-.188-.068c-1.484-1.518-3.492-3.874-6.022-7.046-2.614-3.263-4.579-5.62-5.959-7.047-2.844-3.058-4.831-5.052-5.96-6.05-.983-.928-1.484-1.517-1.484-1.744v-.068c.062-.09.125-.113.209-.113zm3.032 9.335c-1.694-1.971-2.635-3.218-2.844-3.761v-.068c0-.045.021-.09.063-.136a.451.451 0 0 1 .188-.068c.042 0 .063.023.063.068.543.544 1.191 1.269 1.986 2.22 1.025 1.246 1.673 2.017 1.924 2.289 2.697 3.013 4.035 4.531 4.035 4.576 5.04 5.053 7.675 7.93 7.884 8.61.041.046.062.091.062.136 0 .136-.083.227-.251.272h-.125c-.125 0-.23-.068-.314-.204-.585-1.563-1.84-3.195-3.784-4.894-2.74-2.334-4.287-3.738-4.642-4.237l.062.136c-.062-.022-1.484-1.654-4.307-4.94zm-.983-13.572c.042 0 .063.023.063.068 0 .045-.021.09-.063.136-.042.045-.105.068-.188.068h-.063a1.87 1.87 0 0 0-.251-.204l.126-.136.125-.136v-.068c.042 0 .063.023.063.068.125.136.292.295.502.476.251.226.585.566 1.003 1.02.418.453 1.004 1.02 1.799 1.744.899.816 1.526 1.36 1.861 1.677a.22.22 0 0 1 .125.204c0 .045-.042.113-.125.204-.042.09-.105.136-.189.136-.041 0-.083-.023-.125-.068-1.15-.635-2.488-1.858-3.973-3.694-.836-1.087-1.276-1.608-1.359-1.608.042-.136.104-.204.188-.204h.063c.062-.023.209.09.418.317zm1.045-4.101v.136c0 .544.732 1.654 2.175 3.353.816.997 1.631 1.88 2.426 2.697.815.86 1.422 1.336 1.798 1.472a.41.41 0 0 0 .314-.136.205.205 0 0 0 .062-.136.218.218 0 0 0-.125-.203c-.335-.454-.899-1.043-1.673-1.745a48.107 48.107 0 0 1-1.735-1.745 77.584 77.584 0 0 1-1.861-2.22c-.586-.906-.9-1.337-.983-1.337v-.136c0-.09-.042-.136-.126-.136h-.062c-.105 0-.168.045-.21.136zm3.848.476c-.669-.816-1.401-1.835-2.237-3.082-.042-.045-.063-.09-.063-.136 0-.09.042-.18.125-.271.084-.091.168-.136.251-.136.084 0 .147.045.188.136a47.19 47.19 0 0 0 2.426 3.285c1.401 1.79 2.133 2.673 2.175 2.696v-.068c.041.045.062.09.062.136 0 .09-.041.159-.125.204-.042.09-.105.136-.188.136-.042 0-.084-.023-.126-.068-.502-.408-1.338-1.36-2.488-2.832zm3.597 13.572c-1.778-1.654-3.095-3.014-3.973-4.101a.552.552 0 0 1-.063-.204c0-.09.042-.181.125-.272.084-.09.147-.136.188-.136.084 0 .147.045.189.136.794.816 2.216 2.243 4.286 4.305 1.819 1.79 3.22 3.24 4.224 4.373 3.429 3.76 5.353 6.457 5.771 8.066v.136c0 .136-.083.249-.251.34h-.125c-.125 0-.209-.091-.251-.272-.418-1.382-2.467-4.147-6.148-8.27-.752-.839-2.07-2.22-3.972-4.101zm-.753-17.197a39.57 39.57 0 0 1-2.739-2.878.551.551 0 0 1-.063-.204c0-.09.021-.158.063-.204.083-.09.167-.136.25-.136.084 0 .147.023.189.068.627.77 1.526 1.722 2.739 2.9 1.401 1.292 2.237 2.153 2.488 2.56v.069c0 .09-.021.181-.063.272-.041.045-.083.068-.125.068-.042 0-.084-.023-.125-.068-.126-.068-1.004-.884-2.614-2.447zm12.65-7.25c.084 0 .126.067.126.203s-.042.227-.126.272c-3.471 1.2-6.252 3.557-8.301 7.07a39.423 39.423 0 0 0-4.287 10.15c-.083.317-.271.476-.564.476h-.188c-.293-.091-.439-.295-.439-.612v-.204c1.191-4.622 2.969-8.61 5.332-11.963 2.613-3.67 5.039-5.506 7.318-5.506.398-.045.774.023 1.129.113zm-9.723 4.69c-1.108-1.02-2.112-1.813-2.969-2.357a.405.405 0 0 1-.188-.34c0-.045.021-.113.062-.204.084-.136.168-.204.251-.204.084 0 .147.023.189.068 2.446 1.564 4.244 3.172 5.394 4.849.042.045.063.09.063.136 0 .045-.021.09-.063.136a.518.518 0 0 1-.25.068h-.063c-.105-.046-.899-.77-2.426-2.153zm3.91 20.64c-2.948-2.56-4.893-4.645-5.896-6.253-.042-.046-.063-.09-.063-.136a.22.22 0 0 1 .125-.204.46.46 0 0 1 .189-.068c.083 0 .167.045.251.136.752 1.178 3.011 3.444 6.816 6.865 3.095 2.787 4.81 5.075 5.144 6.865v.068c0 .091-.063.182-.188.272h-.063c-.083 0-.167-.045-.251-.136-1.359-2.288-2.258-3.648-2.676-4.1-.774-.907-1.903-2.017-3.388-3.309zm-1.986-23.654c-1.046-.816-1.903-1.427-2.614-1.88-.125-.046-.188-.137-.188-.273 0-.045.021-.113.063-.203.083-.136.167-.204.251-.204.083 0 .146.022.188.068.711.407 1.568 1.042 2.614 1.88 1.484 1.178 2.279 1.79 2.362 1.88.042.046.063.091.063.137a.551.551 0 0 1-.063.204c-.041.09-.104.136-.188.136h-.125c-.377-.136-1.171-.726-2.363-1.745zm1.422-5.053c.042.046.063.09.063.136a.551.551 0 0 1-.063.204c-.042.09-.105.136-.188.136-.042 0-.084-.023-.126-.068l-.251-.136c.126-.181.23-.317.314-.408l.188.136.251.272c.125.09.23.159.314.204.439.363.732.612.794.748 2.154 2.198 3.262 3.353 3.346 3.489.042.045.063.09.063.136 0 .09-.042.159-.126.204-.042.09-.104.136-.188.136-.042 0-.084-.023-.125-.068-.544-.363-1.192-.952-1.924-1.813-.878-.997-1.485-1.631-1.861-1.948-.878-.725-1.318-1.156-1.318-1.337 0-.227.063-.34.189-.34.083 0 .167.023.251.068l-.063-.068.46.317zm-.251 21.842c.251.408.418.703.502.861a.2.2 0 0 1 .062.136c0 .045-.041.09-.125.136-.042.045-.084.068-.125.068-.042 0-.084-.045-.126-.136a2.118 2.118 0 0 0-.627-.748l.376-.407c-.042-.046 1.297 1.45 4.036 4.486 1.694 1.97 2.697 3.353 3.032 4.169.042.09.063.158.063.204 0 .09-.042.158-.126.203-.042.046-.104.068-.188.068-.042 0-.084-.022-.126-.068-.376-.453-.941-1.2-1.735-2.288a37.284 37.284 0 0 0-1.736-2.22c-2.404-2.742-3.596-4.124-3.596-4.17a.551.551 0 0 1-.063-.203c0-.09.021-.159.063-.204a.452.452 0 0 1 .188-.068c.084.045.167.09.251.181zm2.927-24.266c.042-.09.084-.136.126-.136h.063c.083 0 .125.045.125.136v.068c0-.09.021-.136.063-.136.083 0 .209.317.376.952.126.317.376.793.753 1.404.334.59.857 1.428 1.547 2.56.084.091.126.205.126.34 0 .136-.084.25-.251.34-.126.091-.23.136-.314.136a.411.411 0 0 1-.314-.136c-.669-.77-1.275-1.812-1.861-3.149-.292-.725-.48-1.269-.564-1.677v-.068c0-.158.021-.362.125-.634z"
          fill="#fff"
        />
      </g>
      <g filter="url(#Quote_j)">
        <path
          d="M383.651 336.064c-4.098 9.131-10.1 14.207-18.045 15.181h-.126c-.167 0-.251-.113-.251-.34v-.136a.44.44 0 0 1 .251-.408c1.778-.498 3.074-.906 3.91-1.269 8.26-3.579 13.571-10.739 15.871-21.502.628-3.013.941-5.913.941-8.745 0-1.972-.146-3.875-.439-5.71-1.045-6.457-2.927-10.626-5.646-12.507-1.777-1.291-3.659-1.949-5.645-1.949-1.903 0-3.932.612-6.085 1.813-4.308 2.379-6.587 5.823-6.88 10.355-.042.453-.063.883-.063 1.268 0 5.053 2.133 8.588 6.378 10.559 1.861.838 3.618 1.269 5.269 1.269 2.614 0 4.977-1.065 7.131-3.218.167-.136.334-.204.502-.204.167 0 .334.068.502.204a.957.957 0 0 1 .188.544c0 .227-.063.431-.188.612-.168.181-.314.272-.44.272-.167 0-.334-.091-.501-.272-.168-.181-.251-.363-.251-.544 0-.181.062-.363.188-.544v-.068c.251.272.585.657.983 1.133-2.321 2.334-4.956 3.489-7.946 3.489-1.443 0-2.949-.272-4.538-.815-4.956-1.654-7.799-5.008-8.489-10.083a20.278 20.278 0 0 1-.126-2.22c0-3.127.941-6.05 2.781-8.746 2.321-3.217 5.332-5.189 9.054-5.914.92-.181 1.799-.272 2.677-.272 2.614 0 4.809.793 6.628 2.425 2.489 2.152 4.245 5.777 5.27 10.875.585 2.787.857 5.529.878 8.27.042 5.846-1.234 11.556-3.743 17.197zm-5.332-27.098c5.332 5.189 8.176 8.043 8.49 8.542a.205.205 0 0 1 .062.136.22.22 0 0 1-.125.204.46.46 0 0 1-.188.068c-.042 0-.084-.023-.126-.068-1.024-1.246-2.509-2.764-4.412-4.509-2.363-2.153-3.889-3.557-4.579-4.237-.92-.997-2.154-2.583-3.722-4.781-1.527-2.198-2.76-3.761-3.722-4.69-.084-.091-.126-.181-.126-.272 0-.045.042-.113.126-.204.042-.045.104-.068.188-.068s.167.023.251.068c1.045.952 2.342 2.561 3.91 4.849 1.652 2.379 2.969 4.033 3.973 4.962zm5.207 9.969c1.735 1.836 2.613 3.308 2.655 4.441v.068c0 .045-.062.091-.188.136-.125 0-.188-.068-.188-.204-.335-1.337-1.589-3.059-3.785-5.12-.544-.499-1.401-1.247-2.593-2.289-1.15-.997-2.028-1.744-2.614-2.288-1.401-1.292-3.052-3.24-4.955-5.846-1.213-1.745-3.095-4.237-5.646-7.454a.218.218 0 0 1-.125-.204c0-.045.02-.091.041-.136.042-.091.105-.136.189-.136.041 0 .083.023.125.068.878.634 2.76 2.991 5.646 7.046 2.154 3.037 4.015 5.257 5.583 6.662 3.45 3.013 5.395 4.758 5.855 5.256zm.376-10.558c1.004 1.291 1.506 1.948 1.547 1.948.084 0 .126.046.126.136v.068c0 .136-.063.204-.188.204-.209 0-.69-.453-1.485-1.337-.753-.883-1.882-2.265-3.408-4.101-1.945-2.424-3.158-3.919-3.66-4.508-1.15-1.473-1.777-2.311-1.861-2.493v-.068c0-.09.042-.181.126-.272.042-.045.104-.068.188-.068.042 0 .084.023.126.068.041.046.669.839 1.923 2.357a224.638 224.638 0 0 0 3.158 3.965c1.652 2.016 2.781 3.376 3.408 4.101zm-1.129 15.996c1.234 1.745 2.049 2.787 2.425 3.081a.22.22 0 0 1 .126.204c0 .046-.021.091-.063.136-.042.046-.104.068-.188.068h-.125c-.523-.407-1.318-1.291-2.363-2.628-.649-.861-1.631-1.858-2.907-3.036-.042-.045-.063-.113-.063-.204v-.204c.042-.091.105-.136.189-.136.041 0 .083.023.125.068.899.385 1.861 1.269 2.844 2.651zm-4.893 1.201c2.551 2.107 4.621 4.35 6.21 6.729.042.046.063.114.063.204 0 .045-.042.113-.125.204-.084.091-.168.136-.251.136-.042 0-.084-.045-.126-.136-.585-.861-1.589-1.971-3.032-3.353-1.443-1.382-2.488-2.493-3.095-3.286-.041-.045-.062-.113-.062-.204a.56.56 0 0 1 .062-.203c.042-.091.105-.136.189-.136.021-.023.083 0 .167.045zm1.255 6.117c1.233 1.383 2.362 2.515 3.408 3.354.042.09.063.158.063.204 0 .09-.021.181-.063.272-.084.09-.167.136-.251.136-.042 0-.084-.023-.125-.068a36.444 36.444 0 0 1-3.472-3.422c-1.819-2.107-2.885-3.398-3.157-3.897-.042-.045-.063-.09-.063-.136 0-.045.042-.09.126-.136.042-.09.083-.136.125-.136.042 0 .084.023.126.068-.021-.045 1.087 1.201 3.283 3.761zm-6.65-18.624c3.973 3.444 6.65 6.548 8.009 9.29.042.045.062.09.062.136 0 .09-.041.158-.125.203h-.125c-.042 0-.084-.022-.126-.068-1.819-2.786-4.788-5.981-8.929-9.606-3.973-3.535-6.607-6.367-7.925-8.474-.041-.046-.062-.114-.062-.204 0-.091.062-.181.188-.272.042-.045.104-.068.188-.068s.167.068.251.204c1.338 2.062 4.203 5.007 8.594 8.859zm4.956 22.657a59.3 59.3 0 0 1 2.655 3.762c.042.045.063.09.063.136 0 .09-.042.158-.125.204-.084.09-.168.135-.251.135-.042 0-.084-.045-.126-.135a104.706 104.706 0 0 0-3.032-3.966c-1.735-2.197-2.634-3.285-2.676-3.285a.202.202 0 0 1-.063-.136c0-.045.042-.09.126-.136.041-.09.083-.136.125-.136.042 0 .084.023.125.068.69.499 1.736 1.654 3.179 3.489zm.376 7.387a6.993 6.993 0 0 1-.69-.725c-.042-.046-.063-.091-.063-.136l.063-.068c.042-.045.084-.068.126-.068.041 0 .083.023.125.068.335.226.565.385.69.476l-.251.408a20.014 20.014 0 0 1-1.234-1.745c-.501-.793-.878-1.405-1.17-1.813a22.752 22.752 0 0 0-1.297-1.472c-.627-.68-1.045-1.179-1.296-1.541-.042-.045-.063-.113-.063-.204 0-.045.021-.091.063-.136.041-.045.104-.068.188-.068h.125c.711.363 1.631 1.359 2.802 3.036a89.505 89.505 0 0 1 2.363 3.693c.042.046.063.091.063.136a.218.218 0 0 1-.126.204.456.456 0 0 1-.188.068c-.125-.045-.188-.068-.23-.113zm-16.31 3.58c-.042-.046-.063-.091-.063-.136 0-.046.042-.091.126-.136.209-.136.564-.249 1.045-.34.544-.091.9-.204 1.109-.34a9.982 9.982 0 0 0 2.488-1.88c1.777-1.836 3.429-4.124 4.956-6.866 1.652-2.832 2.844-5.37 3.534-7.658.418-1.291.648-2.288.731-2.945.042-.952.126-1.473.251-1.609a.41.41 0 0 1 .314-.136c.042 0 .105.023.188.068.335.227.502.657.502 1.337 0 .997-.376 2.469-1.108 4.441a45.838 45.838 0 0 1-4.161 8.541c-2.07 3.218-3.973 5.506-5.709 6.866-1.317.974-2.363 1.472-3.095 1.472-.481-.022-.857-.249-1.108-.679zm7.946-31.268c3.095 3.127 5.353 5.891 6.754 8.27.042.046.063.091.063.136a.218.218 0 0 1-.126.204.442.442 0 0 1-.188.068c-.042 0-.083-.022-.125-.068-1.359-1.926-3.743-4.599-7.131-7.998-3.304-3.353-5.666-6.027-7.067-7.998a.202.202 0 0 1-.063-.136c0-.091.042-.181.125-.272a.53.53 0 0 1 .251-.068c.042 0 .105.023.188.068.983 1.201 3.43 3.807 7.319 7.794zm6.127 30.316c.083.091.125.159.125.204a.219.219 0 0 1-.125.204c-.084.091-.147.136-.188.136-.084 0-.147-.045-.189-.136-.836-.77-1.589-1.835-2.3-3.217v-.136c0-.091.021-.159.063-.204.042-.046.084-.068.125-.068.042 0 .084.022.126.068 1.61 2.062 2.426 3.126 2.426 3.172l-.063-.023zm-1.924-3.353c.376.453.69.838.92 1.133.042.09.063.158.063.204a.477.477 0 0 1-.126.339c-.083.091-.146.136-.188.136-.083 0-.167-.045-.251-.136-2.321-2.877-3.534-4.418-3.659-4.644v-.136c0-.091.021-.159.063-.204.042-.046.104-.068.188-.068.042 0 .084.022.125.068.042.045.669.77 1.861 2.152.23.227.544.612 1.004 1.156zm-.732 5.913c-.209-.271-.502-.589-.878-.997-.042-.045-.063-.113-.063-.203 0-.046.021-.091.063-.136.042-.046.084-.068.125-.068.042 0 .084.022.126.068.376.362.711.679.983.951l-.356.385c-.042 0-.794-.929-2.3-2.764-.251-.317-1.066-1.155-2.488-2.56a.218.218 0 0 1-.126-.204c0-.045.042-.113.126-.204.042-.091.104-.136.188-.136a.46.46 0 0 1 .188.068c-.042 0 1.547 1.79 4.789 5.37a.569.569 0 0 1 .062.204c0 .09-.021.158-.062.204-.084.09-.147.136-.189.136-.083.022-.146-.023-.188-.114zm-6.398-30.791c2.321 2.651 3.826 4.486 4.516 5.506.042.045.063.09.063.136 0 .09-.042.158-.125.203-.084.091-.168.136-.251.136-.042 0-.084-.045-.126-.136-5.792-7.522-8.74-11.283-8.866-11.283v-.068c0-.136.063-.226.188-.272.042-.045.105-.068.189-.068.083 0 .146.068.188.204.92 1.564 2.321 3.444 4.224 5.642zm.313 28.979c.879.883 1.589 1.541 2.175 1.948a.405.405 0 0 1 .125.272.64.64 0 0 1-.062.272c-.084.136-.168.204-.251.204-.084 0-.147-.023-.188-.068-.711-.544-1.464-1.223-2.301-2.084-1.108-1.179-1.756-1.858-1.923-2.085a.202.202 0 0 1-.063-.136c0-.09.042-.158.125-.204.042-.09.105-.136.189-.136.041 0 .083.023.125.068.293.136.962.771 2.049 1.949zm-1.861 4.622c-.042-.045-.083-.068-.125-.068a.218.218 0 0 1-.126-.204c0-.045.021-.091.063-.136.042-.091.126-.136.251-.136h.063c0 .045.042.068.125.068a4.03 4.03 0 0 1-.313.408v-.068c.041.045.167.113.376.204-.126 0-.376-.249-.753-.748-.125-.09-.502-.521-1.129-1.269a143.8 143.8 0 0 1-1.798-2.288c-.084-.136-.126-.249-.126-.34 0-.181.105-.317.314-.408a.644.644 0 0 1 .376-.136c.168 0 .293.091.377.272.251.363.794 1.02 1.61 2.017.711.861 1.171 1.563 1.422 2.152.042.136.062.249.062.34 0 .091-.041.181-.125.272-.084.136-.188.204-.314.204a.46.46 0 0 1-.188-.068c.021-.045 0-.068-.042-.068zm-18.171-8.791c-3.596 4.622-7.674 7.658-12.211 9.131a.464.464 0 0 1-.189.068c-.167 0-.271-.114-.313-.34v-.136c0-.181.104-.34.313-.476 9.347-4.123 15.369-11.578 18.046-22.385.816-3.24 1.234-6.322 1.234-9.267 0-1.518-.126-3.014-.377-4.509-.92-5.279-3.283-8.882-7.13-10.763-1.359-.679-2.718-.996-4.036-1.019-2.593 0-5.185 1.291-7.757 3.897-3.012 2.991-4.517 6.253-4.517 9.743 0 .951.125 1.926.376 2.945.544 2.243 1.673 4.079 3.409 5.529 1.652 1.336 3.471 2.016 5.457 2.016h.377c5.541-.362 8.343-1.495 8.426-3.421 0-.544.231-.793.691-.793.501 0 .752.272.752.793-.042 1.971-1.861 3.376-5.457 4.237-1.61.408-3.095.612-4.475.612-2.447 0-4.538-.658-6.273-1.949-2.969-2.334-4.454-5.279-4.454-8.882 0-.77.063-1.563.188-2.424.753-4.532 2.907-8.225 6.461-11.08 2.28-1.835 4.538-2.764 6.817-2.764 1.568 0 3.137.453 4.705 1.337 3.973 2.243 6.482 5.959 7.57 11.147.376 1.79.564 3.648.564 5.574 0 2.787-.397 5.687-1.171 8.746a38.572 38.572 0 0 1-7.026 14.433zm4.893-29.387c1.276 1.427 1.987 2.424 2.112 2.968v.272c0 .136-.021.204-.063.204-.083 0-.188-.068-.313-.204-.335-.362-.795-.929-1.359-1.677-.669-.861-1.129-1.427-1.422-1.744a62.898 62.898 0 0 0-3.471-3.489c-.439-.454-1.088-1.043-1.924-1.745-.941-.861-1.589-1.428-1.924-1.745-1.401-1.246-2.258-2.22-2.53-2.877-.042-.046-.063-.091-.063-.136 0-.091.042-.181.126-.272a.456.456 0 0 1 .188-.068c.084 0 .146.023.188.068.711.952 3.011 3.195 6.942 6.797a46.669 46.669 0 0 1 3.513 3.648zm1.673 13.595a.974.974 0 0 1-.314-.068c-.041 0-.062-.023-.062-.068 0-.046.021-.091.062-.136 0-.046.021-.068.063-.068h.063c-.502-.182-1.568-1.133-3.22-2.833-2.551-2.56-4.517-4.599-5.897-6.117-1.004-1.11-2.697-3.195-5.081-6.253-2.154-2.651-3.889-4.691-5.207-6.118a.219.219 0 0 1-.125-.204c0-.045.042-.113.125-.204.042-.045.105-.068.188-.068.042 0 .105.023.189.068 1.484 1.518 3.492 3.875 6.022 7.047 2.613 3.262 4.579 5.619 5.959 7.046 2.865 3.059 4.83 5.053 5.96 6.05.982.929 1.484 1.518 1.484 1.744v.068c-.063.091-.125.114-.209.114zm-3.032-9.358c1.694 1.971 2.656 3.217 2.865 3.761v.068a.202.202 0 0 1-.063.136.46.46 0 0 1-.188.068c-.042 0-.063-.023-.063-.068a29.005 29.005 0 0 1-1.986-2.22c-1.025-1.247-1.673-2.017-1.924-2.289-2.697-3.013-4.036-4.531-4.036-4.577-5.039-5.052-7.674-7.93-7.883-8.609-.042-.046-.063-.091-.063-.136 0-.136.084-.227.251-.272h.126c.125 0 .23.068.313.204.586 1.563 1.84 3.194 3.785 4.894 2.718 2.333 4.287 3.738 4.642 4.237l-.063-.136c.042.045 1.464 1.676 4.287 4.939zm.983 13.572c-.042 0-.063-.023-.063-.068 0-.045.021-.091.063-.136.042-.045.104-.068.188-.068h.063c.042.045.125.113.251.204l-.126.136-.125.136v.068c-.042 0-.063-.023-.063-.068a6.287 6.287 0 0 0-.502-.476 24.264 24.264 0 0 1-1.003-.997 24.996 24.996 0 0 0-1.799-1.745 92.387 92.387 0 0 1-1.861-1.676.221.221 0 0 1-.125-.204c0-.045.042-.113.125-.204.042-.091.105-.136.188-.136.042 0 .084.023.126.068 1.15.634 2.467 1.858 3.973 3.693.815 1.088 1.296 1.609 1.359 1.609-.042.136-.105.204-.188.204h-.063c-.063 0-.209-.114-.418-.34zm-1.046 4.124v-.136c0-.544-.711-1.654-2.174-3.354a48.76 48.76 0 0 0-2.426-2.696c-.815-.861-1.422-1.337-1.798-1.473a.412.412 0 0 0-.314.136c-.042.046-.063.091-.063.136 0 .091.042.159.126.204.334.453.899 1.02 1.673 1.745.794.77 1.359 1.337 1.735 1.744.669.771 1.297 1.496 1.861 2.221.586.906.899 1.337.983 1.337v.136c0 .09.042.135.126.135h.062c.105-.022.168-.068.209-.135zm-3.847-.476c.669.815 1.401 1.835 2.237 3.081a.202.202 0 0 1 .063.136c0 .091-.042.181-.125.272-.084.091-.168.136-.251.136-.084 0-.147-.045-.189-.136a47.088 47.088 0 0 0-2.425-3.285c-1.401-1.79-2.133-2.674-2.175-2.674v.068a.202.202 0 0 1-.063-.136c0-.091.042-.159.126-.204.042-.09.104-.136.188-.136.042 0 .084.023.126.068.501.385 1.338 1.337 2.488 2.81zm-3.597-13.572c1.778 1.654 3.095 3.013 3.973 4.101.042.09.063.158.063.204 0 .09-.042.181-.125.272-.084.09-.147.136-.189.136-.083 0-.146-.046-.188-.136a465.267 465.267 0 0 0-4.286-4.305c-1.82-1.79-3.221-3.24-4.224-4.373-3.43-3.761-5.353-6.458-5.772-8.066v-.136c0-.136.084-.249.251-.34h.126c.125 0 .209.091.251.272.418 1.382 2.467 4.146 6.147 8.27.753.838 2.07 2.22 3.973 4.101zm.753 17.197a39.898 39.898 0 0 1 2.739 2.877.555.555 0 0 1 .063.204c0 .091-.021.159-.063.204-.083.091-.167.136-.251.136-.083 0-.146-.022-.188-.068-.627-.77-1.526-1.722-2.718-2.9-1.401-1.291-2.238-2.152-2.488-2.56v-.068a.64.64 0 0 1 .062-.272c.042-.045.084-.068.126-.068.042 0 .083.023.125.068.105.068.962.884 2.593 2.447zm-12.651 7.25c-.083 0-.125-.068-.125-.204 0-.136.042-.226.125-.272 3.471-1.2 6.253-3.557 8.302-7.046a39.416 39.416 0 0 0 4.286-10.151c.084-.317.272-.475.565-.475h.188c.293.09.439.294.439.611v.204c-1.192 4.622-2.969 8.61-5.332 11.963-2.614 3.671-5.039 5.506-7.319 5.506-.397 0-.773-.045-1.129-.136zm9.724-4.69c1.108 1.02 2.112 1.813 2.969 2.357.125.09.188.204.188.339a.545.545 0 0 1-.063.204c-.083.136-.167.204-.251.204-.083 0-.146-.022-.188-.068-2.446-1.563-4.245-3.172-5.395-4.848-.041-.046-.062-.091-.062-.136 0-.046.021-.091.062-.136a.518.518 0 0 1 .251-.068h.063c.105.045.899.747 2.426 2.152zm-3.89-20.641c2.928 2.561 4.893 4.645 5.897 6.254.042.045.063.09.063.136a.22.22 0 0 1-.126.204.54.54 0 0 1-.167.068c-.084 0-.167-.046-.251-.136-.753-1.179-3.011-3.444-6.817-6.866-3.094-2.787-4.809-5.075-5.144-6.865v-.068c0-.09.063-.181.189-.272h.062c.084 0 .168.046.251.136 1.359 2.289 2.259 3.648 2.677 4.101.732.907 1.861 2.017 3.366 3.308zm1.966 23.655c1.045.815 1.903 1.427 2.614 1.88.125.046.188.136.188.272a.555.555 0 0 1-.063.204c-.084.136-.167.204-.251.204-.083 0-.146-.023-.188-.068-.711-.408-1.568-1.042-2.614-1.881-1.484-1.178-2.279-1.79-2.363-1.88a.205.205 0 0 1-.062-.136c0-.045.021-.113.062-.204.042-.091.105-.136.189-.136h.125c.397.136 1.171.725 2.363 1.745zm-1.422 5.03c-.042-.046-.063-.091-.063-.136 0-.046.021-.114.063-.204.042-.091.105-.136.188-.136.042 0 .084.022.126.068l.251.136c-.126.181-.23.317-.314.408l-.188-.136-.251-.272a2.938 2.938 0 0 0-.314-.204c-.439-.363-.732-.612-.794-.748-2.154-2.198-3.262-3.353-3.346-3.489a.202.202 0 0 1-.063-.136c0-.091.042-.159.126-.204.041-.091.104-.136.188-.136.042 0 .083.023.125.068.544.363 1.171.952 1.924 1.813.878.997 1.485 1.631 1.861 1.948.878.725 1.296 1.178 1.296 1.337 0 .227-.062.34-.188.34a.53.53 0 0 1-.251-.068l.063.068-.439-.317zm.251-21.842a17.13 17.13 0 0 1-.502-.861c-.042-.046-.063-.091-.063-.136 0-.045.042-.091.126-.136.042-.045.083-.068.125-.068.042 0 .084.045.126.136.167.317.376.566.627.748l-.376.407c.041.046-1.297-1.45-4.036-4.486-1.694-1.971-2.697-3.353-3.032-4.169a.545.545 0 0 1-.063-.204.22.22 0 0 1 .126-.204c.042-.045.104-.068.188-.068.042 0 .084.023.125.068.377.454.941 1.224 1.736 2.289.69.951 1.296 1.676 1.735 2.22 2.405 2.719 3.597 4.124 3.597 4.169a.555.555 0 0 1 .063.204c0 .091-.021.159-.063.204a.46.46 0 0 1-.188.068.878.878 0 0 1-.251-.181zm-2.907 24.266c-.041.091-.083.136-.125.136h-.063c-.083 0-.125-.045-.125-.136v-.068c0 .091-.021.136-.063.136-.084 0-.209-.317-.376-.952-.126-.317-.377-.793-.732-1.404a83.608 83.608 0 0 0-1.548-2.561.484.484 0 0 1-.125-.34c0-.136.084-.249.251-.339.125-.091.23-.136.313-.136.126 0 .231.045.314.136.669.747 1.276 1.812 1.861 3.149.293.725.481 1.269.565 1.677v.068a3.05 3.05 0 0 1-.147.634z"
          fill="#fff"
        />
      </g>
      <defs>
        <linearGradient
          id="Quote_b"
          x1={293.492}
          y1={126.136}
          x2={489.184}
          y2={202.217}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="Quote_c"
          x1={293.492}
          y1={126.136}
          x2={489.184}
          y2={202.217}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="Quote_d"
          x1={293.492}
          y1={126.136}
          x2={489.184}
          y2={202.217}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="Quote_f"
          x1={-127.834}
          y1={193.388}
          x2={155.811}
          y2={276.394}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={0.81} stopColor="#4E0971" />
        </linearGradient>
        <linearGradient
          id="Quote_g"
          x1={-127.834}
          y1={193.388}
          x2={155.811}
          y2={276.394}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={0.81} stopColor="#4E0971" />
        </linearGradient>
        <linearGradient
          id="Quote_h"
          x1={-127.834}
          y1={193.388}
          x2={155.811}
          y2={276.394}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={0.81} stopColor="#4E0971" />
        </linearGradient>
        <filter
          id="Quote_a"
          x={308.731}
          y={0.002}
          width={139.296}
          height={322.558}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={-7} dy={3} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_268" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_268" result="shape" />
        </filter>
        <filter
          id="Quote_e"
          x={0}
          y={65.436}
          width={130.155}
          height={332.211}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx={7} dy={-3} />
          <feGaussianBlur stdDeviation={4} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_268" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_268" result="shape" />
        </filter>
        <filter
          id="Quote_i"
          x={64.237}
          y={18.848}
          width={66.716}
          height={62.197}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_268" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_268" result="shape" />
        </filter>
        <filter
          id="Quote_j"
          x={324.699}
          y={297.297}
          width={66.696}
          height={62.219}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_268" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_268" result="shape" />
        </filter>
      </defs>
    </svg>
  );
}
