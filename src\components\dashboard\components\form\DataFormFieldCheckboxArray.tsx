import React from "react";
import { DataFormFieldProps } from "./DataFormField";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/Popover";
import { Button } from "../ui/Button";
import { Checkbox } from "../ui/Checkbox";
import FieldAttachments from "../ui/FieldAttachments";
import { Badge } from "../ui/Badge";
import { ChevronsUpDown } from "lucide-react";
import { DataFormCheckboxArrayConfig } from "./types";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "../ui/Command";

export default function DataFormFieldCheckboxArray<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>,
) {
  const [arrayValues, setArrayValues] = React.useState<string[]>(
    Array.isArray(props.defaultValue) ? props.defaultValue : [],
  );
  React.useEffect(
    () => setArrayValues(Array.isArray(props.defaultValue) ? props.defaultValue : []),
    [props.defaultValue],
  );

  return (
    <div>
      <FieldAttachments
        label={props.label}
        required={props.required}
        htmlFor={props.accessorKey}
        description={props.description}
        errorMessage={props.errorMessage}
      >
        <div className="relative">
          <input
            onChange={() => {}}
            type="text"
            className="sr-only absolute right-1/2 bottom-0"
            required={props.required ?? true}
            name={`@parse${props.accessorKey}`}
            value={arrayValues.length ? JSON.stringify(arrayValues) : ""}
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="secondary" className="w-full justify-start pl-2" disabled={props.isPending}>
                <SelectedValues arrayValues={arrayValues} checkboxArrayConfig={props.checkboxArrayConfig} />
                <ChevronsUpDown
                  className="mr-auto size-4 shrink-0 stroke-2 text-gray-500/80 sm:size-4"
                  aria-hidden="true"
                />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="start" className="p-0">
              <Command>
                {props.checkboxArrayConfig?.enableSearchInput && (
                  <CommandInput
                    autoFocus={false}
                    id={"searchInArray"}
                    placeholder={props.checkboxArrayConfig?.searchInputPlaceholder ?? "بحث... "}
                  />
                )}
                <CommandList>
                  <CommandEmpty>لا توجد نتيجة</CommandEmpty>
                  <CommandGroup>
                    {props.checkboxArrayConfig?.options.map((option) => {
                      const isSelected = arrayValues.includes(option.value);
                      return (
                        <CommandItem
                          key={option.value}
                          className="pl-2"
                          onSelect={() => {
                            if (isSelected) {
                              setArrayValues((prev) => prev.filter((i) => i !== option.value));
                            } else {
                              setArrayValues((prev) => [...prev, option.value]);
                            }
                          }}
                        >
                          <Checkbox checked={isSelected} className="!cursor-default" />
                          <span>{option.label}</span>
                          {option?.icon && (
                            <div className="mr-auto text-gray-600 dark:text-gray-400 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
                              {option.icon}
                            </div>
                          )}
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
      </FieldAttachments>
    </div>
  );
}

function SelectedValues(props: {
  arrayValues: (string | number)[];
  checkboxArrayConfig?: DataFormCheckboxArrayConfig<any>;
}) {
  const [isMore, setIsMore] = React.useState(false);
  const [hiddenCount, setHiddenCount] = React.useState(0);

  return (
    <>
      <div
        className="relative flex w-full items-center gap-2 overflow-hidden"
        ref={(e) => {
          if (e) setIsMore(e?.scrollWidth > e?.clientWidth);
        }}
      >
        {props.arrayValues.length > 0 ? (
          <>
            <div
              className="flex gap-1"
              ref={(e) => {
                if (!e) return;

                const children = Array.from(e.children);
                let count = 0;

                for (const child of children) {
                  if ((child as HTMLElement).offsetLeft < e.scrollLeft) count++;
                }

                setHiddenCount(count);
              }}
            >
              {props.checkboxArrayConfig?.options
                ?.filter((e) => props.arrayValues.includes(e.value))
                ?.map((e) => (
                  <Badge
                    variant="default"
                    key={e.value}
                    className="rounded-xs px-1.5 py-1 font-normal ring-0 sm:py-0.5"
                  >
                    {e.label}
                  </Badge>
                ))}
            </div>
          </>
        ) : (
          <span className="text-base font-normal text-gray-400 sm:text-sm dark:text-gray-500">
            لم يتم تحديد قيمة حتى الآن
          </span>
        )}
      </div>
      {isMore && (
        <>
          <Badge variant="default" className="relative rounded-xs px-2 py-1 font-normal ring-0 sm:py-0.5">
            +{hiddenCount}
            <div className="absolute inset-y-0 left-full w-10 bg-gradient-to-r from-white from-30% transition-colors duration-100 ease-in-out group-hover:from-gray-50 dark:from-gray-950 dark:group-hover:from-[#0C121F]"></div>
          </Badge>
        </>
      )}
    </>
  );
}

/*  <Checkbox
                id={option.value}
                value={option.value}
                defaultChecked={arrayValues.includes(option.value)}
                onCheckedChange={() => {
                  setArrayValues((prev) =>
                    prev.includes(option.value)
                      ? prev.filter((i) => i !== option.value)
                      : [...prev, option.value],
                  );
                }}
              />
              <Label htmlFor={option.value} className="text-sm text-gray-600">
                {option.label}
              </Label>*/
