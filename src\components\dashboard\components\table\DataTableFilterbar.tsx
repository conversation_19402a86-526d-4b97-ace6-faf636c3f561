import { Column, Table } from "@tanstack/react-table";
import React from "react";
import { ColumnSearchDef, DataTableFiltersDef } from "./types";
import DataTableFilterOptions from "./DataTableFilterOptions";
import { useDebouncedCallback } from "use-debounce";
import { Input } from "../ui/Input";
import { Button } from "../ui/Button";
import { Plus } from "lucide-react";
import { ViewOptions } from "./DataTableViewOptions";
import { cx } from "../../lib/utils-tremor";
import Link from "next/link";

// ================================================================================
// ادوات الجدول
// ================================================================================
export default function DataTableFilterbar<TData extends { [K: string]: any }>(props: {
  table: Table<TData>;
  filters: DataTableFiltersDef<TData>;
  columnSearch?: ColumnSearchDef<TData>;
  createDataButton?: { label?: string; href: string };
}) {
  const { filters, table, columnSearch, createDataButton } = props;
  const { getColumn } = table;

  return (
    <div className="flex flex-wrap items-center justify-between gap-2 sm:gap-x-6">
      <div className="flex w-full grow flex-col gap-2 sm:w-fit sm:flex-row sm:items-center">
        {filters?.options && (
          <DataTableFilterOptions optionFilters={filters.options} getColumn={getColumn} />
        )}
        <SearchInput getColumn={getColumn} columnSearch={columnSearch} />
      </div>
      <div className="flex items-center gap-2 sm:mr-auto">
        <CreateDataButton {...createDataButton} />
        {/* <Button variant="secondary" className="gap-x-1.5 py-1.5 pr-2 pl-2.5 text-sm sm:text-xs">
          <ArrowDownToLine className="!size-3.5" />
          تصدير
        </Button> */}
        <ViewOptions table={table} />
      </div>
    </div>
  );
}

// ================================================================================
// زر انشاء بيانات جديدة
// ================================================================================
function CreateDataButton({ href, label }: { label?: string; href?: string }) {
  if (!href) return null;

  return (
    <Button
      variant="light"
      asChild
      className={cx(
        "gap-x-1.5 text-sm text-blue-600 sm:text-xs dark:text-blue-500",
        !!label ? "py-1.5 pr-2 pl-2.5" : "h-[34px] sm:h-[30px]",
      )}
    >
      <Link href={href}>
        <Plus />
        {label}
      </Link>
    </Button>
  );
}

// ================================================================================
// حقل البحث في الجدول
// ================================================================================
function SearchInput<TData extends { [K: string]: any }>(props: {
  getColumn: (columnId: string) => Column<TData, unknown> | undefined;
  columnSearch?: ColumnSearchDef<TData>;
}) {
  const { getColumn, columnSearch } = props;
  const { columnKey, placeholder } = columnSearch ?? {};
  const [searchTerm, setSearchTerm] = React.useState<string>("");

  const debouncedSetFilterValue = useDebouncedCallback((value) => {
    if (!columnKey) return;
    getColumn(columnKey)?.setFilterValue(value);
  }, 250);

  const handleSearchChange = (event: any) => {
    const value = event.target.value;
    setSearchTerm(value);
    debouncedSetFilterValue(value);
  };

  if (columnKey && getColumn(columnKey)?.getIsVisible()) {
    return (
      <Input
        type="search"
        required={false}
        placeholder={placeholder ?? "بحث في الجدول..."}
        value={searchTerm}
        onChange={handleSearchChange}
        className="w-full lg:max-w-64 [&>input]:h-[38px] sm:[&>input]:h-[30px]"
      />
    );
  }
}
