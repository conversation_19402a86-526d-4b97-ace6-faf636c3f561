import { ColumnDef } from "@tanstack/react-table";
import { DataTableColumnHeader } from "./DataTableColumnHeader";
import { DataTableCreateColumnsDef, DataTableRowActionsDef } from "./types";
import { Checkbox } from "../ui/Checkbox";
import { DataTableRowActions } from "./DataTableRowActions";

type Props<TData extends { [K: string]: any }> = {
  createColumns: DataTableCreateColumnsDef<TData>[];
  actions?: DataTableRowActionsDef<TData>;
};

export function generateColumns<TData extends { [K: string]: any }>(props: Props<TData>) {
  // الأعمدة
  const columns: ColumnDef<TData>[] = props.createColumns.map((col) => ({
    accessorKey: col.accessorKey,
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={col.columnLabel} />
    ),
    enableSorting: (col.enableSorting ?? col.accessorKey === "id") ? false : true,
    meta: { displayName: col.columnLabel },
    filterFn: "fuzzySearch",
    enableColumnFilter: col.valueOptions?.enableFilter,

    cell: ({ getValue }) => {
      if (col.cell) return col.cell({ value: getValue() });

      const optionValue = col.valueOptions?.options.find((v) => v.value === getValue());
      if (!!optionValue && col.valueOptions?.enableDisplayOptionsInBody !== false) {
        return (
          <div className="flex items-center gap-2 [&_svg]:size-3.5">
            {optionValue?.icon}
            {optionValue?.label}
          </div>
        );
      }

      return getValue();
    },
  }));

  return [
    ...selectionColumn<TData>({ isDisable: false }),
    ...columns,
    ...actionColumn<TData>({ actions: props.actions }),
  ];
}

function selectionColumn<TData>({
  isDisable,
}: {
  isDisable?: boolean;
}): ColumnDef<TData>[] {
  if ((isDisable ?? false) === true) return [];

  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
              ? true
              : table.getIsSomeRowsSelected()
                ? "indeterminate"
                : false
          }
          onCheckedChange={() => table.toggleAllPageRowsSelected()}
          className="translate-y-0.5"
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={() => row.toggleSelected()}
          className="translate-y-0.5"
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      meta: {
        displayName: "تحديد الأعمدة",
      },
    },
  ];
}

function actionColumn<TData extends { [K: string]: any }>({
  actions,
}: {
  actions?: DataTableRowActionsDef<TData>;
}): ColumnDef<TData>[] {
  if (!actions) return [];

  return [
    {
      id: "edit",
      enableSorting: false,
      enableHiding: false,
      meta: {
        displayName: "الإجراءات",
      },
      cell: ({ row }) => <DataTableRowActions row={row} actions={actions} />,
    },
  ];
}
