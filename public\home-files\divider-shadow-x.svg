<svg width="632" height="136" viewBox="0 0 632 136" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_140_83" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="632" height="68">
<rect y="68" width="68" height="632" transform="rotate(-90 0 68)" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_140_83)">
<g style="mix-blend-mode:multiply" opacity="0.2" filter="url(#filter0_f_140_83)">
<ellipse cx="50.3014" cy="311.898" rx="50.3014" ry="311.898" transform="matrix(-4.37114e-08 1 1 4.37114e-08 4.10327 63.3419)" fill="#1E1E1E"/>
</g>
</g>
<mask id="mask1_140_83" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="68" width="632" height="68">
<rect opacity="0.18" width="68" height="632" transform="matrix(-4.37114e-08 1 1 4.37114e-08 0 68)" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask1_140_83)">
<g opacity="0.25" filter="url(#filter1_f_140_83)">
<ellipse cx="316.002" cy="39.3558" rx="50.3014" ry="311.898" transform="rotate(-90 316.002 39.3558)" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_f_140_83" x="-23.7967" y="35.4419" width="679.597" height="156.403" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="13.95" result="effect1_foregroundBlur_140_83"/>
</filter>
<filter id="filter1_f_140_83" x="-23.7967" y="-38.8456" width="679.597" height="156.403" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="13.95" result="effect1_foregroundBlur_140_83"/>
</filter>
</defs>
</svg>
