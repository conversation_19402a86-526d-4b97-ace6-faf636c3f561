import * as React from "react";
import { ConsultationCard } from "./ConsultationCard";
import { Consultation } from "@prisma/client";
import { SetConsultationStateType } from "./Consultations";
import ConsultationCardSkeleton from "./ConsultationCardSkeleton";
import { useOnView } from "@/hooks/useOnScreen";
import { getConsultations } from "./db-queries";
import { ConsultationsListFilterBar } from "./ConsultationsListFilterBar";

export default React.memo(function ConsultationsList({
  data,
  mode,
  setData,
  currentCursor,
  setCurrentCursor,
  setActivatedConsultation,
}: {
  currentCursor: string | null;
  mode?: "unRead" | "all";
  data: Consultation[];
  setData: React.Dispatch<React.SetStateAction<Consultation[]>>;
  setCurrentCursor: React.Dispatch<React.SetStateAction<string | null>>;
  setActivatedConsultation: SetConsultationStateType;
}) {
  const { ref, isOnView } = useOnView("0px 0px 10px 0px");
  const [isLoading, startTransition] = React.useTransition();

  React.useEffect(() => {
    const timeLimit = setTimeout(() => {
      if (isOnView && !!currentCursor) {
        startTransition(async () => {
          const { data: newData, nextCursor } = await getConsultations({
            cursorId: currentCursor,
            ...(mode === "unRead" && { where: { read: false } }),
          });

          startTransition(() => {
            setCurrentCursor(nextCursor);
            setData((prev) => [...prev, ...newData]);
          });
        });
      }
    }, 300);

    return () => {
      clearTimeout(timeLimit);
    };
  }, [isOnView, currentCursor, setCurrentCursor, setData, mode]);

  return (
    <div className="col-span-2 flex flex-col @min-3xl:h-svh @min-3xl:max-h-[calc(100vh-170px)]">
      <div className="group flex h-full flex-col @min-3xl:overflow-y-auto">
        <ConsultationsListFilterBar mode={mode} />

        {React.useMemo(
          () =>
            data.length ? (
              data.map((consultation) => (
                <ConsultationCard
                  key={consultation.id}
                  consultation={consultation}
                  onClick={() => setActivatedConsultation(consultation)}
                />
              ))
            ) : (
              <div className="flex w-full items-center justify-center rounded-lg border bg-white p-5 text-gray-600 dark:bg-gray-950 dark:text-gray-400">
                <p className="text-center text-sm">لا توجد استشارات</p>
              </div>
            ),
          [data, setActivatedConsultation],
        )}

        <ConsultationCardSkeleton hasMore={!!currentCursor} isLoading={isLoading} isOnView={isOnView} ref={ref} />
      </div>
    </div>
  );
});
