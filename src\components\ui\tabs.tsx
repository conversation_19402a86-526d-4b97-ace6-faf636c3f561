"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

const items = [
  {
    label: "مقالات",
    path: "/media-experience/articles/1",
  },
  {
    label: "كتب",
    path: "/media-experience/books/1",
  },
  {
    label: "مقابلات",
    path: "/media-experience/interviews/1",
  },
];

export default function Tabs() {
  const currentPath = usePathname();

  return (
    <nav id="tabs" className="mx-auto max-w-72 scroll-mt-20 sm:max-w-96">
      <ul className="grid h-10 grid-cols-3 place-items-center items-center justify-center gap-1 rounded-md bg-secondary p-1 text-secondary-foreground">
        {items.map((item, index) => {
          const isEqualPath = currentPath.startsWith(item.path.slice(0, -2));
          return (
            <li
              data-state={isEqualPath}
              className="inline-flex w-full items-center justify-center whitespace-nowrap rounded-sm text-center text-sm font-medium ring-offset-white transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-zinc-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=true]:bg-white data-[state=true]:text-zinc-950 data-[state=true]:shadow-xs data-[state=false]:hover:bg-muted/50"
              key={index}
            >
              <Link className="w-full px-3 py-1.5" href={item.path + "#tabs"}>
                {item.label}
              </Link>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
