"use client";
import PageLayout from "../PageLayout";
import DataTable from "@/components/dashboard/components/table/DataTable";
import { Course } from "@prisma/client";
import PriceBadge from "../PriceBadge";
import { DataTableOnDeleteFnDef, DataTableOnOptionChangeFnDef } from "@/components/dashboard/components/table/types";
import TableImage from "../TableImage";
import TableArray from "../TableArray";
import { Edit, FolderTree } from "lucide-react";
import FormatTime from "@/components/ui/format-time";

export default function CoursesTable({
  data,
  onDelete,
  onOptionChange,
}: {
  data: Course[];
  onDelete: DataTableOnDeleteFnDef<Course>;
  onOptionChange?: DataTableOnOptionChangeFnDef<Course>;
}) {
  return (
    <PageLayout title="الكورسات" description="">
      <DataTable
        data={data}
        columnSearch={{ columnKey: "title", placeholder: "بحث بالاسم ..." }}
        createDataButton={{ href: "/admin/courses/create", label: "كورس جديد" }}
        defaultPageSize={5}
        rowActions={{
          links: {
            items: [
              {
                label: "تعديل",
                basePath: "/admin/courses/update",
                icon: Edit,
              },
              {
                label: "المحاور",
                basePath: "/admin/courses/lectures",
                icon: FolderTree,
              },
            ],
          },
          onOptionChange,
          onDelete,
        }}
        createColumns={[
          { accessorKey: "title", columnLabel: "اسم الكورس" },
          {
            accessorKey: "price",
            columnLabel: "السعر",
            cell: ({ value }) => <PriceBadge price={value} />,
          },
          { accessorKey: "modulesCount", columnLabel: "عدد المحاور" },
          {
            accessorKey: "posterUrl",
            columnLabel: "صورة الكورس",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "previewInHomePage",
            columnLabel: "عرض في الرئيسية",
            valueOptions: {
              enableActionChange: true,
              options: [
                { label: "نعم", value: true },
                { label: "لا", value: false },
              ],
            },
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => <FormatTime dateInput={value} />,
          },
          { accessorKey: "description", columnLabel: "الوصف" },
          {
            accessorKey: "features",
            columnLabel: "مميزات الكورس",
            cell: ({ value }) => <TableArray arr={value} />,
          },

          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },

          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  );
}
