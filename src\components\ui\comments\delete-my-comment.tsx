"use client";

import useSession from "@/hooks/useSession";
import dynamic from "next/dynamic";

import { Comment } from "@prisma/client";
import { Skeleton } from "../skeleton";

const DeleteMyCommentDropdownMenu = dynamic(
  () => import("./delete-my-comment-dropdown-menu"),
  { loading: () => <Skeleton className="size-5 rounded-md" />, ssr: false },
);

// مكون يمكن المستخدم من حذف التعليق الخاص به
export default function DeleteMyComment({
  comment,
  pathRevalidate,
}: {
  comment: Comment;
  pathRevalidate: string;
}) {
  const session = useSession();
  if (!session) return null;

  if (comment.userId === session.id) {
    return (
      <DeleteMyCommentDropdownMenu comment={comment} pathRevalidate={pathRevalidate} />
    );
  }

  return null;
}
