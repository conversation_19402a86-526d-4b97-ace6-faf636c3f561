"use server";

import prisma from "@/lib/prisma";
import { verifySession } from "./dal";
import { createSession, deleteSession } from "./session";
import { redirect } from "next/navigation";

export async function getSession() {
  const session = await verifySession();

  if (!session) {
    return undefined;
  }

  const user = await prisma.user.findUnique({
    where: { id: session.id, emailVerified: true },
    include: {
      bookOrders: { where: { status: "PAID" } },
      courseOrders: { where: { status: "PAID" } },
    },
  });

  // اذا كان المستخدم غير موجود في قاعدة البيانات يتم حذف الجلسة التي يستخدمها وإعادة توجيهه الى صفحة تسجيل الدخول
  if (!user) {
    await deleteSession();
    return redirect("/");
  }

  // انشاء مصفوفة لمعرفات كل الكورسات والكتب التي اشترك فيها المستخدم
  const userBooksIds = user.bookOrders.map((book) => book.bookId);
  const userCoursesIds = user.courseOrders.map((course) => course.courseId);
  const purchasesIds = [...userBooksIds, ...userCoursesIds];

  // ================================================================================== //
  // انشاء توكن جديد للمستخدم
  // ================================================================================== //
  await createSession({
    id: user.id,
    purchasesIds,
    name: user.name,
    email: user.email,
  });

  return session;
}
