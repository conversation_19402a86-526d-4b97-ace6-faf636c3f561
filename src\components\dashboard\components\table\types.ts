/**
 * Defines the properties for a data table component.
 *
 * @template TData - The type of the data objects in the table. Each object must have string keys and any values.
 *
 * @property data - An array of data objects to be displayed in the table.
 * @property defaultPageSize - (Optional) The default number of rows to display per page.
 * @property enableRowSelection - (Optional) A flag to enable or disable row selection functionality.
 * @property primaryKey - (Optional) The key in the data objects that uniquely identifies each row. Must be a string key of `TData`.
 * @property columnSearch - (Optional) Configuration for enabling column-specific search functionality.
 * @property createDataButton - (Optional) Configuration for a button to create new data entries.
 * @property rowActions - (Optional) Configuration for actions that can be performed on individual rows.
 * @property createColumns - An array of column definitions used to create the table's columns.
 */
export type DataTablePropsDef<TData extends { [K: string]: any }> = {
  data: TData[];
  defaultPageSize?: number;
  enableRowSelection?: boolean;
  primaryKey?: keyof TData & string;
  columnSearch?: ColumnSearchDef<TData>;
  createDataButton?: CreateDataButtonType;
  rowActions?: DataTableRowActionsDef<TData>;
  createColumns: DataTableCreateColumnsDef<TData>[];
};

/** زر انشاء بيانات جديده في الجدول */
export type CreateDataButtonType = { label?: string; href: string };

/** تحديد العمود الذي تريد استخدامه للبحث */
export type ColumnSearchDef<TData extends { [K: string]: any }> = {
  columnKey: keyof TData & string;
  placeholder?: string;
};

/** نوع أعمدة الجدول */
export type DataTableCreateColumnsDef<TData extends { [K: string]: any }> = {
  [K in keyof TData]: {
    accessorKey: K;
    columnLabel: string;
    enableSorting?: boolean;
    cell?: (props: { value: TData[K]; optionValue?: React.ReactNode }) => React.ReactNode;

    valueOptions?: {
      enableFilter?: boolean;
      enableActionChange?: boolean;
      enableDisplayOptionsInBody?: boolean;
      options: { label: string; value: TData[K]; icon?: React.JSX.Element }[];
    };
  };
}[keyof TData];

/** نوع إجراءات الجدول */
export type DataTableRowActionsDef<TData extends { [K: string]: any }> = {
  links?: DataTableRowActionLinksDef<TData>;
  options?: DataTableOptionsDef<TData>[];
  onDelete?: DataTableOnDeleteFnDef<TData>;

  onOptionChange?: DataTableOnOptionChangeFnDef<TData>;
};

export type DataTableOnOptionChangeFnDef<TData extends { [K: string]: any }> = (props: {
  where: TData;
  data: TData;
}) => Promise<{ success: boolean; message?: string }>;

export type DataTableOnDeleteFnDef<TData extends { [K: string]: any }> = (props: {
  data: TData[];
}) => Promise<{ success: boolean; message?: string }>;

/** نوع إجراءات الروابط */
export type DataTableRowActionLinksDef<TData extends { [K: string]: any }> = {
  /**
   * مفتاح العمود الاساسي الذي تريد استخدامه لتحديد الصف الذي تريد تعديله
   * سيتم الحاق قيمة هذا المفتاح بعد قيمة basePath
   * @example
   * id = 1
   * basePath = /users
   * سيتم الانتقال الى الصفحة /users/1
   * @default id
   */
  primaryKey?: keyof TData & string;
  items: {
    label: string;
    basePath: string;
    icon?: React.ComponentType<{ className?: string; [K: string]: any }>;
  }[];
};

/** قيم الخيارات */
export type DataTableOptionsDef<TData extends { [K: string]: any }> = {
  [K in keyof TData]: {
    columnKey: K & string;
    label: string;
    options: {
      value: TData[K];
      label: string;
      icon?: React.JSX.Element;
    }[];
  };
}[keyof TData];

export type DataTableFiltersDef<TData extends { [K: string]: any }> = {
  options?: DataTableOptionsDef<TData>[];
};
