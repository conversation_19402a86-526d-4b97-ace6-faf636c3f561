import DeleteMyComment from "./delete-my-comment";
import FormatTime from "../format-time";

import { Comment } from "@prisma/client";
import { User } from "lucide-react";
import { cn } from "@/lib/utils";
// ==================================================================================
// مكون التعليق يستخدم وكأنه بطاقة
// ==================================================================================
export function CommentCard({
  comment,
  usingAnimate,
  pathRevalidate,
}: {
  comment: Comment;
  usingAnimate?: boolean;
  pathRevalidate: string;
}) {
  return (
    <div
      className={cn(
        "my-4 flex flex-nowrap items-start gap-5 py-4 first:transition-all",
        usingAnimate &&
          "first:delay-75 first:duration-500 first:animate-in first:fade-in-0 first:slide-in-from-top",
      )}
    >
      <div className="flex size-12 shrink-0 items-center justify-center overflow-clip rounded-full bg-secondary">
        <User className="size-7 text-background" />
      </div>
      <div className="relative flex-1 space-y-2">
        <div className="flex flex-nowrap items-center gap-2">
          <p className="text-sm font-semibold">{comment.userName}</p>
          <p className="text-xs">
            <FormatTime dateInput={comment.createdAt} />
          </p>
          <div className="absolute top-0 left-0">
            <DeleteMyComment comment={comment} pathRevalidate={pathRevalidate} />
          </div>
        </div>
        <p className="text-sm leading-relaxed text-wrap">{comment.comment}</p>
      </div>
    </div>
  );
}
