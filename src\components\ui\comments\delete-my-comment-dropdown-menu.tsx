"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { deleteCommentAction } from "./comment-actions";
import { Delete, Ellipsis } from "lucide-react";
import { toast } from "sonner";
import { Comment } from "@prisma/client";

export default function DeleteMyCommentDropdownMenu({
  comment,
  pathRevalidate,
}: {
  comment: Comment;
  pathRevalidate: string;
}) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="appearance-none outline-hidden focus:outline-hidden">
        <Ellipsis className="size-5 rotate-90" />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem
          className="text-destructive focus:bg-destructive/10 focus:text-destructive"
          onClick={async () => {
            const deleteComment = await deleteCommentAction({
              pathRevalidate,
              commentId: comment.id,
              entity: comment.entity,
              entityId: comment.entityId,
            });
            if (!deleteComment.sucess) {
              toast.error(deleteComment.message);
              return;
            }

            if (deleteComment.sucess) {
              toast.success(deleteComment.message);
            }
          }}
        >
          حذف التعليق
          <DropdownMenuShortcut>
            <Delete />
          </DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
