"use server";
import prisma from "@/lib/prisma";
import { Consultation, Prisma } from "@prisma/client";
import { unstable_cache } from "next/cache";
import { revalidateTag } from "next/cache";
import { revalidateConsultations } from "@/lib/cache-invalidation";

type GetConsultationsResponse = Promise<{ data: Consultation[]; nextCursor: string | null }>;
type GetConsultationsProps = {
  cursorId?: string | null;
  limit?: number;
  where?: Prisma.ConsultationWhereInput | undefined;
};

export const getConsultations = async ({
  cursorId,
  limit = 30,
  where,
}: GetConsultationsProps): GetConsultationsResponse => {
  // Fetch consultations from the database with optional cursor and limit
  const consultations = await prisma.consultation.findMany({
    where,
    take: limit + 1,
    orderBy: { createdAt: "desc" },
    ...(cursorId && { cursor: { id: cursorId }, skip: 1 }),
  });

  const hasMore = consultations.length > limit;
  const nextCursor = hasMore ? consultations[limit].id : null;

  return { data: consultations.slice(0, limit), nextCursor };
};

export const getConsultationCache = unstable_cache(async () => await getConsultations({ limit: 50 }), ["consultations"], {
  tags: ["consultations"],
});

export const getConsultationUnReadCache = unstable_cache(
  async () => await getConsultations({ limit: 100, where: { read: false } }),
  ["consultations"],
  {
    tags: ["consultations"],
  },
);

export const getConsultationUnReadCountCache = unstable_cache(
  async () => await prisma.consultation.count({ where: { read: false } }),
  ["consultations"],
  {
    tags: ["consultations"],
  },
);

export async function markConsultationAsRead(id: string) {
  await prisma.consultation.update({
    where: { id },
    data: { read: true },
  });

  revalidateTag("consultations");
  await revalidateConsultations();
  return { success: true };
}

export async function deleteConsultation(id: string) {
  await prisma.consultation.delete({
    where: { id },
  });
  revalidateTag("consultations");
  await revalidateConsultations();
  return { success: true, message: "تم حذف الاستشارة بنجاح" };
}
