import prisma from "@/lib/prisma";

import ConsultationForm, { ConsultationFormAction } from "@/components/ui/consultation-form";

import { DividerShadow_X, DividerShadow_Y } from "@/components/ui/svg/icons";
import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers";
import { fromEntries } from "@/functions/utils-fonctions";
import { siteName } from "@/utils/siteConfig";
import { Metadata } from "next";
import { z } from "zod";
import { revalidateTag } from "next/cache";
import { sendConsultationConfirmationEmail } from "@/lib/email";
import { sendNotificationToAdmins } from '@/app/actions';

export const dynamic = "force-static";

export const metadata: Metadata = {
  title: `حجز استشارة | ${siteName}`,
  description:
    "نحن هنا لمساعدتك على تخطي التحديات التي تأتي مع القيادة، الشهرة، والمسؤوليات الكبيرة. لا تدع الضغوط تؤثر على صفائك الذهني وسلامك الداخلي.",
};

// ==================================================================================
// # صفحة حجز استشارة
// ==================================================================================
export default function ConsultationPage() {
  return (
    <div className="pt-16">
      <HeaderConsultationPage />
      <div className="mx-auto mt-16 mb-24 w-full max-w-(--breakpoint-xl) space-y-4 overflow-x-clip px-3 md:mt-24 md:mb-32 md:flex md:items-center md:gap-4 md:space-y-0 md:px-6">
        <Texts />
        <DividerShadow_X className="pointer-events-none max-w-full scale-x-125 scale-y-150 select-none md:hidden" />
        <DividerShadow_Y className="pointer-events-none hidden h-auto w-20 scale-y-2 select-none md:block" />
        <ConsultationForm formAction={formAction} />
      </div>
    </div>
  );
}

// ==================================================================================
// دالة معالجة طلب حجز الاستشارة
// ==================================================================================
const formAction: ConsultationFormAction = async (state, formData) => {
  "use server";

  const request = fromEntries(formData) as {
    name: string;
    email: string;
    message: string;
    phone: string;
  };

  const schema = z.object({
    email: z.string().trim().email(),
    name: z
      .string()
      .trim()
      .max(40, "لا يمكن ان يكون الاسم اكبر من 40 حرف")
      .min(2, "لا يمكن ان يكون الاسم اقل من 2 احرف"),
    message: z
      .string()
      .trim()
      .min(5, { message: "يجب أن تحتوي الرسالة على 5 أحرف على الأقل" })
      .max(500, { message: "لا يمكن أن تتجاوز الرسالة 500 حرف" }),
    phone: z
      .string()
      .trim()
      .regex(/^(?:\+?[0-9]{10,15}|[0-9]{9})$/, {
        message: "رقم الهاتف غير صالح، تأكد من التنسيق الصحيح",
      }),
  });

  const { success, data, error } = schema.safeParse(request);

  if (!success) {
    return {
      state: request,
      success: false,
      message: "حدث خطأ. لم يتم إرسال الحجز",
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    // حفظ البيانات في قاعدة البيانات
    const consultation = await prisma.consultation.create({
      data: {
        ...data,
        createdAt: new Date(),
      }
    });

    // الحصول على إعدادات الموقع للحصول على البريد الإلكتروني للإدارة
    // const siteSettings = await getSiteSettings();

    // إعداد بيانات الاستشارة للبريد الإلكتروني
    const consultationData = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      message: data.message,
      createdAt: consultation.createdAt,
    };

    // إرسال البريد الإلكتروني للمستخدم (تأكيد الحجز)
    const userEmailResult = await sendConsultationConfirmationEmail(consultationData);

    // إرسال إشعار للمشرفين
    await sendNotificationToAdmins(
      "استشارة جديدة",
      `تم استلام استشارة جديدة من ${consultationData.name}`
    );

    revalidateTag("consultations");

    // التحقق من نجاح إرسال البريد الإلكتروني
    if (!userEmailResult.isSended) {
      console.error("فشل في إرسال بريد التأكيد للمستخدم:", userEmailResult.error);
    }

    return {
      message: "تم إرسال الحجز بنجاح وسيتم التواصل معك خلال 24 ساعة",
      success: true
    };

  } catch (error) {
    console.error("خطأ في معالجة طلب الاستشارة:", error);
    return {
      state: request,
      success: false,
      message: "حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى",
    };
  }
};

// ==================================================================================
// مكون يعرض رأس الصفحة
// ==================================================================================
function HeaderConsultationPage() {
  return (
    <div className="relative bg-secondary py-24 sm:py-32">
      <DividerRedTop />
      <div className="flex flex-col items-center space-y-5 px-3 text-center md:space-y-6 md:leading-normal lg:space-y-8">
        <h1 className="text-4xl leading-normal font-semibold text-balance text-background drop-shadow-[0_4px_2px_#00000050] md:text-5xl lg:font-bold">
          تحرّر من الضغوط، واستعد توازنك
        </h1>
        <div className="h-px w-full bg-linear-to-l from-background/0 via-background/60" />
        <p className="w-full text-sm leading-loose text-balance text-background/90 sm:max-w-(--breakpoint-sm) sm:leading-loose md:text-base md:leading-loose md:font-medium">
          نحن هنا لمساعدتك على تخطي التحديات التي تأتي مع القيادة، الشهرة، والمسؤوليات الكبيرة. لا تدع الضغوط تؤثر على
          صفائك الذهني وسلامك الداخلي.
        </p>
      </div>
      <DividerRedBottom />
    </div>
  );
}

// ==================================================================================
// مكون يعرض النصوص والعناوين
// ==================================================================================
function Texts() {
  return (
    <div className="md:max-w-[42%] lg:max-w-[47%]">
      <h2 className="text-3xl leading-normal font-bold text-nowrap drop-shadow-[0_2px_1px_#00000050] lg:text-5xl lg:leading-normal lg:font-bold">
        احجز استشارتك الآن
      </h2>
      <p className="mt-2 text-sm font-medium text-muted lg:text-base">
        املأ النموذج التالي وسنكون على تواصل معك في اسرع وقت ممكن.
      </p>
      <h2 className="mt-6 text-2xl font-bold lg:text-3xl">كيف تتم معالجة طلبك؟</h2>
      <ul className="mt-2 list-item list-outside space-y-1.5 px-4 text-sm lg:mt-4 lg:space-y-2 lg:text-base">
        <li className="list-decimal">قم بملء النموذج بمعلوماتك الأساسية وإرساله إلينا.</li>
        <li className="list-decimal">سنقوم بمراجعة تفاصيلك والتأكد منها.</li>
        <li className="list-decimal">سنتواصل بك لتحديد موعد يناسبك وتوضيح أي استفسارات.</li>
        <li className="list-decimal">استمتع بجلسة خاصة وسرية مصممة لمساعدتك.</li>
        <li className="list-decimal">سنتابعك باستمرار ونقدم لك توجيهات إضافية وخطط دعم بناءً على احتياجاتك الشخصية.</li>
      </ul>
    </div>
  );
}
