import React from "react";
import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Book } from "@prisma/client";

export default function BooksForm({
  mode,
  defaultValues,
  onAction,
}: {
  onAction?: DataFormOnActionDef<Book>;
  mode: "create" | "update";
  defaultValues?: Partial<Book>;
}) {
  return (
    <DataForm<Book>
      fields={[
        {
          accessorKey: "title",
          label: "عنوان الكتاب",
          placeholder: "ادخل عنوان هذا الكتاب",
        },
        {
          accessorKey: "summary",
          label: "ملخص الكتاب",
          fieldType: "textarea",
          placeholder: "اكتب نبذه ملخصة عن الكتاب ...",
        },
        {
          accessorKey: "pagesCount",
          label: "عدد الصفحات",
          placeholder: "ادخل عدد صفحات الكتاب",
        },
        {
          accessorKey: "coverImage",
          label: "صورة الغلاف",
          fieldType: "fileUploader",
        },
        {
          accessorKey: "bookUrl",
          label: "الكتاب",
          fieldType: "fileUploader",
          fileUploaderConfig: { acceptedFileTypes: ["application/pdf"] },
        },
        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف الكتاب لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description: "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },
        {
          label: "السعر",
          accessorKey: "price",
          placeholder: "$0.00",
          required: false,
          description: `سعر الكتاب بالدولار. عندما يترك فارغ او ادخال قيمة تساوي "0" سيكون الكتاب مجاني`,
        },
        { accessorKey: "id", label: "", fieldType: "hidden" },
      ]}
      mode={mode}
      onAction={onAction}
      defaultValues={defaultValues}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/books"
      cancelButtonLabel="إلغاء"
    />
  );
}