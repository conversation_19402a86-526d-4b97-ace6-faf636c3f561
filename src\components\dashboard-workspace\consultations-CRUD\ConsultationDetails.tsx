import {
  Dialog,
  DialogTitle,
  <PERSON><PERSON>Header,
  DialogContent,
  DialogDescription,
} from "@/components/dashboard/components/ui/Dialog";

import React from "react";
import ConsultationDetailsButtonsBar from "./ConsultationDetailsButtonsBar";

import { Inbox, User2 } from "lucide-react";
import { Divider } from "@/components/dashboard/components/ui/Divider";
import { ConsultationStateType, SetConsultationStateType } from "./Consultations";
import { useOnView } from "@/hooks/useOnScreen";
import { Consultation } from "@prisma/client";
import FormatTime from "@/components/ui/format-time";

export default function ConsultationDetails({
  setData,
  activatedConsultation,
  setActivatedConsultation,
}: {
  activatedConsultation: ConsultationStateType;
  setActivatedConsultation: SetConsultationStateType;
  setData: React.Dispatch<React.SetStateAction<Consultation[]>>;
}) {
  return (
    <DetailsContainer isActivated={!!activatedConsultation}>
      <ConsultationDetailsButtonsBar
        activatedConsultation={activatedConsultation}
        setActivatedConsultation={setActivatedConsultation}
        setData={setData}
      />
      {!!activatedConsultation ? (
        <div className="p-3">
          <div className="flex gap-x-3">
            <div className="flex size-11 shrink-0 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-900">
              <User2 className="size-6" />
            </div>
            <div className="w-full">
              <div className="flex w-full items-center gap-x-3 gap-y-1">
                <p className="w-full max-w-max overflow-hidden text-base font-medium text-nowrap text-ellipsis">
                  {activatedConsultation.name}
                </p>
                <p className="min-w-fit text-xs text-nowrap text-gray-600 dark:text-gray-400">
                  <FormatTime dateInput={activatedConsultation.createdAt} />
                </p>
              </div>
              <p dir="ltr" className="mt-0.5 text-right text-sm">
                {activatedConsultation.phone}
              </p>
            </div>
          </div>
          <Divider>الرسالة</Divider>
          <div>
            <p className="text-sm leading-relaxed whitespace-pre-line text-gray-600 dark:text-gray-400">
              {activatedConsultation.message}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex h-full w-full items-center justify-center">
          <Inbox className="size-48 text-gray-300 dark:text-gray-900" />
        </div>
      )}
    </DetailsContainer>
  );
}

// ================================================================================================
// غلاف تفاصيل الاستشارة وهو المسؤل عن تحديد طريقة عرض التفاصيل في الشاشات الكبيرة والشاشات الصغيرة
// ================================================================================================
function DetailsContainer({ children, isActivated }: { children: React.ReactNode; isActivated: boolean }) {
  const { ref, isOnView: isLargeScreen } = useOnView();
  return (
    <>
      <div className="fixed top-16 right-1/2 hidden h-px w-px @min-3xl:block" ref={ref}></div>
      {isLargeScreen ? (
        <div className="col-span-3 hidden h-full @min-3xl:block">
          <div className="sticky top-20 h-full w-full rounded-lg border">{children}</div>
        </div>
      ) : (
        <Dialog open={isActivated}>
          <DialogContent className="h-svh max-h-[calc(100vh-100px)] p-0">
            <DialogHeader className="sr-only">
              <DialogTitle></DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            {children}
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
