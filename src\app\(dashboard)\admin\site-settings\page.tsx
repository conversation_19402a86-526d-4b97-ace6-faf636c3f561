import PageLayout from "@/components/dashboard-workspace/PageLayout";
import SiteSettingsForm from "@/components/dashboard-workspace/site-settings-CRUD/SiteSettingsForm";
import { getSiteSettings } from "@/utils/get-data-from-db";
import React from "react";
import { updateSiteSettings } from "./actions";

export default async function SiteSettingsFormPage() {
  const siteSettings = await getSiteSettings();

  return (
    <PageLayout title="إعدادات الموقع">
      <SiteSettingsForm siteSettings={siteSettings} onSubmit={updateSiteSettings} />
    </PageLayout>
  );
}
