"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import useSession from "@/hooks/useSession";
import { SubscribeCourseButton } from "./subscribe-course-button";

type Props = { courseId: string; coursePrice: number; subscriptionInstructions: string };

export default function SubscribeCourseDialog({ courseId, coursePrice, subscriptionInstructions }: Props) {
  const session = useSession();
  const isSubscribe = session?.purchasesIds.includes(courseId);

  if (isSubscribe) return null;

  return (
    <Dialog>
      <DialogTrigger className="absolute inset-0 z-20"></DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>الاشتراك مطلوب.</DialogTitle>
          <DialogDescription>
            عذرًا، هذه المادة متاحة فقط للمشتركين في الكورس. يرجى الاشتراك للوصول إلى جميع المحاور والمحتويات التعليمية.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-3">
          <SubscribeCourseButton
            courseId={courseId}
            coursePrice={coursePrice}
            subscriptionInstructions={subscriptionInstructions}
          />
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export function SubscriptionInstructionsPaidCourseDialog({
  children,
  subscriptionInstructions,
}: {
  children: React.JSX.Element;
  subscriptionInstructions: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-h-[calc(100vh-100px)] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>للاشتراك في الكورس، يُرجى اتباع الخطوات التالية:</DialogTitle>
          <DialogDescription className="my-6 whitespace-pre-line">{subscriptionInstructions}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="default" className="mr-auto">
              إغلاق
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
