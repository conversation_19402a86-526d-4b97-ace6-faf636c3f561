"use client";
import dividerShadowX from "@/../public/home-files/divider-shadow-x.svg";
import Image from "next/image";
import useSession from "@/hooks/useSession";
import { cn } from "@/lib/utils";
import { ChevronLeft, LucideProps, ShieldCheck, User, WalletMinimal } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ForwardRefExoticComponent, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";

type ItemsType = {
  label: string;
  path: string;
  Icon: ForwardRefExoticComponent<LucideProps>;
  childrens?: Omit<ItemsType, "Icon" | "childrens">[];
};

const items: ItemsType[] = [
  { label: "الملف الشخصي", path: "/profile/profile-data", Icon: User },
  {
    label: "الاشتراكات",
    path: "#",
    Icon: WalletMinimal,
    childrens: [
      { label: "الكــــتب", path: "/profile/book-subscriptions" },
      { label: "الكورسات", path: "/profile/cours-subscriptions" },
    ],
  },
  { label: "أمان الحساب", path: "/profile/account-security", Icon: ShieldCheck },
];

export default function Sidebar() {
  return (
    <>
      <SidebarSm />
      <SidebarMd />
    </>
  );
}

function SidebarMd() {
  return (
    <div className="sticky top-24 hidden h-[calc(100vh-123px)] w-72 rounded-l-xl border-l border-muted/25 py-3 shadow-[-3px_0_10px_-1px_#********] md:flex">
      <SidebarItems />
    </div>
  );
}

function SidebarSm() {
  const [isOpen, setIsOpen] = useState(false);

  function onClickItem() {
    setIsOpen(false);
    document.body.style.overflowY = "auto";
  }

  return (
    <>
      <div className="sticky top-0 z-30 flex h-28 flex-col justify-center rounded-b-md bg-background/70 px-3 pt-[52px] shadow-sm backdrop-blur-xs backdrop-brightness-150 md:hidden">
        <hr className="border-muted/20" />
        <button
          onClick={() => {
            if (!isOpen) {
              setIsOpen(true);
              document.body.style.overflowY = "hidden";
            } else {
              setIsOpen(false);
              document.body.style.overflowY = "auto";
            }
          }}
          className="mt-3 flex flex-nowrap items-center gap-1 py-0.5 text-sm"
        >
          <ChevronLeft
            className={cn("size-4 transition-all duration-300", isOpen && "-rotate-90")}
          />
          <span>القائمة</span>
        </button>
      </div>
      <div
        className={cn(
          "fixed top-28 z-20 w-full overflow-clip transition-all duration-300 md:hidden",
          isOpen ? "h-[calc(100vh-112px)] opacity-100" : "h-0 opacity-50",
        )}
      >
        <div className="h-[calc(100vh-112px)]">
          <SidebarItems onClickItem={onClickItem} />
        </div>
      </div>
    </>
  );
}

function SidebarItems({ onClickItem }: { onClickItem?: () => void }) {
  const session = useSession();
  const pathname = usePathname();

  return (
    <div className="h-full w-full overflow-x-clip overflow-y-auto bg-background p-4">
      <div>
        <div className="mx-auto size-32 rounded-2xl bg-secondary p-6">
          <User className="size-full text-background" />
        </div>
        {!session?.name ? (
          <Skeleton className="mx-auto mt-2 h-7 w-3/4" />
        ) : (
          <p className="mt-2 pr-0.5 text-center text-lg font-semibold">{session?.name}</p>
        )}
      </div>

      <Image
        className="w-full scale-x-125 rotate-180"
        loading="eager"
        src={dividerShadowX}
        alt=""
      />

      <div className="">
        <ul className="grid gap-2 text-sm font-medium">
          {items.map((item) => (
            <ItemGenerated
              key={item.path}
              item={item}
              pathname={pathname}
              onClickItem={onClickItem}
            />
          ))}
        </ul>
      </div>
      <Image className="w-full scale-x-125" loading="eager" src={dividerShadowX} alt="" />
    </div>
  );
}

function ItemGenerated({
  item,
  pathname,
  onClickItem,
}: {
  item: ItemsType;
  pathname: string;
  onClickItem?: () => void;
}) {
  const [isExpanded, setIsExpanded] = useState(false);
  const isChildren = item.childrens !== undefined;
  const isEqualPath = pathname.startsWith(item.path);

  return (
    <li>
      <Link
        className={cn(
          "flex flex-nowrap items-center gap-2 py-1 transition-all duration-300 hover:text-primary",
          isEqualPath && "text-primary",
        )}
        onClick={(e) => {
          if (isChildren) {
            e.preventDefault();
            setIsExpanded((prev) => !prev);
            return;
          }
          if (onClickItem) {
            onClickItem();
          }
        }}
        href={item.path}
      >
        <item.Icon className={cn("size-5", isExpanded && "text-primary")} /> {item.label}
        {isChildren && (
          <ChevronLeft
            className={cn(
              "mr-auto size-4 transition-all duration-300",
              isExpanded && "-rotate-90 text-primary",
            )}
          />
        )}
      </Link>
      <ChildrensItemsGenerated
        pathname={pathname}
        items={item.childrens}
        isExpanded={isExpanded}
        onClickItem={onClickItem}
      />
    </li>
  );
}

function ChildrensItemsGenerated({
  items,
  pathname,
  isExpanded,
  onClickItem,
}: {
  pathname: string;
  items: ItemsType["childrens"];
  onClickItem?: () => void;
  isExpanded: boolean;
}) {
  if (!items?.length) return null;

  return (
    <ul
      className={cn(
        "mr-2.5 flex max-h-min flex-col gap-1 overflow-y-clip border-r border-primary pr-[18px] transition-all duration-300",
        isExpanded ? "h-20 opacity-100" : "h-0 opacity-0",
      )}
    >
      {items?.map((child) => (
        <li key={child.path}>
          <Link
            href={child.path}
            onClick={onClickItem}
            className={cn(
              "flex flex-nowrap gap-2 py-1 hover:text-primary",
              pathname.startsWith(child.path) && "text-primary",
            )}
          >
            {child.label}
          </Link>
        </li>
      ))}
    </ul>
  );
}
