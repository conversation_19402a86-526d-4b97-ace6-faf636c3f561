import Link from "next/link";
import { Button } from "./button";
import { ChevronsLeft, ChevronsRight, MoreHorizontal } from "lucide-react";

export type PaginationProps = {
  currentPage: number;
  totalPages: number;
  // remainingPages: number;
};

export function Pagination({
  url,
  totalPages,
  currentPage,
}: {
  url: string;
} & PaginationProps) {
  if (totalPages <= 1) return null;

  return (
    <div className="flex select-none flex-nowrap items-center justify-center gap-2">
      {/* انتقال الى البداية */}
      <Button asChild variant="ghost">
        <Link
          href={`${url}/${1}#pagination`}
          className={`${currentPage < 2 && "pointer-events-none text-muted"}`}
        >
          <ChevronsRight /> البداية
        </Link>
      </Button>

      {/* اذا كانت الصفحة الحالية ليست الصفحة الأولى */}
      {currentPage > 1 && (
        <Button asChild variant="ghost">
          <Link href={`${url}/${currentPage - 1}#pagination`}>{currentPage - 1}</Link>
        </Button>
      )}

      {/* الصفحة الحالية */}
      <Button variant="outline">{currentPage}</Button>

      {/* الصفحة التالية اذا كانت موجودة */}
      {totalPages > currentPage && (
        <Button asChild variant="ghost">
          <Link href={`${url}/${currentPage + 1}#pagination`}>{currentPage + 1}</Link>
        </Button>
      )}

      {/* الثلاث النقاط */}
      {totalPages > currentPage + 1 && <MoreHorizontal className="h-4 w-4" />}

      {/* انتقال الى النهاية */}
      <Button asChild variant="ghost">
        <Link
          href={`${url}/${totalPages}#pagination`}
          className={`${currentPage >= totalPages && "pointer-events-none text-muted"}`}
        >
          النهاية <ChevronsLeft />
        </Link>
      </Button>
    </div>
  );
}
