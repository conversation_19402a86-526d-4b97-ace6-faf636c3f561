"use client";

import { EntityType } from "@prisma/client";
import dynamic from "next/dynamic";

type CommentsDynamicProps = { entity: EntityType; entityId: string } & {
  hasMore: boolean;
  pathRevalidate: string;
};

const ClientSideComments = dynamic(() => import("./client-side-comments"), {
  ssr: false,
});

export default function ClientSideDynamicComment(props: CommentsDynamicProps) {
  if (props.hasMore) {
    return (
      <ClientSideComments
        entity={props.entity}
        entityId={props.entityId}
        pathRevalidate={props.pathRevalidate}
      />
    );
  }
  return;
}
