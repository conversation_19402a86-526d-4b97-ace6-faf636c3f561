"use client";

import DataTable from "@/components/dashboard/components/table/DataTable";
import PageLayout from "../PageLayout";
import { AdminType } from "@/components/dashboard/components/auth/types";
import { CheckCircle, XCircle } from "lucide-react";
import TableImage from "../TableImage";
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types";

export default function TableAdmins({
  data,
  onDelete,
}: {
  data: AdminType[];
  onDelete: DataTableOnDeleteFnDef<AdminType>;
}) {
  return (
    <PageLayout title="المشرفين" description="يعرض الجدول بيانات كل المشرفين الذي لديهم صلاحية الوصول الى لوحة التحكم">
      <DataTable<AdminType>
        data={data}
        defaultPageSize={5}
        columnSearch={{ columnKey: "name" }}
        createDataButton={{
          label: "إضافة مشرف",
          href: "/admin/admins-management/create",
        }}
        rowActions={{
          // links: {
          //   items: [{ basePath: "/admin/admins-management/update", label: "تعديل" }],
          // },
          onDelete,
        }}
        createColumns={[
          {
            accessorKey: "name",
            columnLabel: "الاسم",
          },
          {
            accessorKey: "email",
            columnLabel: "البريد الإلكتروني",
          },
          {
            accessorKey: "password",
            columnLabel: "كلمة المرور",
            cell: () => "*********",
          },
          {
            accessorKey: "status",
            columnLabel: "الحالة",
            valueOptions: {
              enableActionChange: false,
              options: [
                {
                  label: "مفعل",
                  value: "activated",
                  icon: <CheckCircle />,
                },
                {
                  label: "معطل",
                  value: "disabled",
                  icon: <XCircle />,
                },
              ],
            },
          },
          {
            accessorKey: "avatarUrl",
            columnLabel: "الصورة الرمزية",
            cell: ({ value }) => <TableImage src={value} />,
          },
        ]}
      />
    </PageLayout>
  );
}
