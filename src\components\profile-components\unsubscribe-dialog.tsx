"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTrigger,
  DialogClose,
} from "@/components/ui/dialog";

import { Button } from "../ui/button";
import InputField from "@/components/ui/input-field";
import { useState, useTransition } from "react";
import { toast } from "sonner";

export type ResultUnsubscribeAction = {
  state: { success: boolean; message: string };
  errors?: { password?: string[] };
};

type UnsubscribeDialogProps = {
  children: React.ReactNode;
  unsubscribeAction: (formData: FormData) => Promise<ResultUnsubscribeAction>;
};

export default function UnsubscribeDialog({
  children,
  unsubscribeAction,
}: UnsubscribeDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [errors, setErrors] = useState<ResultUnsubscribeAction["errors"]>(undefined);
  const [isOpen, setOpen] = useState(false);

  function handleSubmitAction(e: React.FormEvent<HTMLFormElement>) {
    startTransition(async () => {
      e.preventDefault();
      const formData = new FormData(e.currentTarget);

      const { state, errors } = await unsubscribeAction(formData);

      if (state.success) {
        toast.success(state.message);
        setOpen(false);
        setTimeout(() => window.location.reload(), 500);
      }

      if (!state.success) {
        toast.error(state.message);
        setErrors(errors);
      }
    });
  }

  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-h-[calc(100vh-100px)] overflow-y-auto">
        <DialogHeader className="text-right">
          <DialogTitle className="">إلغاء الاشتراك</DialogTitle>
          <DialogDescription className="sr-only">إلغاء الاشتراك</DialogDescription>
          <ul className="mr-5 list-outside list-disc text-sm *:my-1.5 *:text-sm *:leading-normal *:text-balance">
            <li>إلغاء الاشتراك نهائي ولا يمكن التراجع عنه</li>
            <li>لن تتمكن من استعادة الوصول إلى المحتوى.</li>
            <li>
              إذا كنت قد دفعت رسوم الاشتراك، فلن يكون هناك أي استرداد للمبلغ المدفوع.
            </li>
          </ul>
          <p className="mt-4 text-sm">
            إذا كنت متأكدًا من قرارك، يرجى إدخال كلمة مرور حسابك أدناه ثم النقر على
            (متابعة) لإكمال عملية الإلغاء.
          </p>
        </DialogHeader>
        <form onSubmit={handleSubmitAction}>
          <InputField
            required
            dir="ltr"
            name="password"
            label="كلمة المرور"
            placeholder="ادخل كلمة المرور"
            autoComplete="current-password"
            errorMessage={errors?.password?.at(0)}
            disabled={isPending}
          />
          <DialogFooter className="mt-6 gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isPending}>
                إلغاء
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isPending}>
              متابعة
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
