import React from "react";
import FieldAttachments from "../ui/FieldAttachments";

import { Input } from "../ui/Input";
import { DataFormFieldProps } from "./DataFormField";

export default function DataFormFieldInput<TData extends { [K: string]: any }>(props: DataFormFieldProps<TData>) {
  const [warningMessage, setWarningMessage] = React.useState<string[] | undefined>(undefined);

  function handleOnChange(e: React.ChangeEvent<HTMLInputElement>) {
    const validate = props.inputConfig?.validateWarning?.safeParse(e.target.value);
    if (!validate?.success) {
      setWarningMessage(validate?.error.flatten().formErrors);
    } else if (validate?.success) {
      setWarningMessage((prev) => prev && undefined);
    }
  }

  return (
    <FieldAttachments
      warningMessage={warningMessage}
      description={props.description}
      label={props.label}
      htmlFor={props.accessorKey}
      required={props.required}
      errorMessage={props.errorMessage}
    >
      <Input
        defaultValue={props.defaultValue}
        onChange={handleOnChange}
        required={props.required}
        disabled={props.isPending}
        id={props.accessorKey}
        type={props.inputConfig?.type ?? "text"}
        name={props.accessorKey}
        placeholder={props.placeholder}
        autoComplete={props.inputConfig?.autoComplete}
      />
    </FieldAttachments>
  );
}
