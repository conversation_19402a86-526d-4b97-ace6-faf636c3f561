import { z } from "zod";

export const lectureSchema = z.object({
  title: z.string().trim(),
  posterUrl: z.string().url(),

  video: z.preprocess((val) => {
    if (typeof val !== "string" || val.trim() === "") return null;
    return z.string().url().safeParse(val).success ? val : null;
  }, z.string().url().nullable().default(null)),

  audio: z.preprocess((val) => {
    if (typeof val !== "string" || val.trim() === "") return null;
    return z.string().url().safeParse(val).success ? val : null;
  }, z.string().url().nullable().default(null)),

  pdf: z.preprocess((val) => {
    if (typeof val !== "string" || val.trim() === "") return null;
    return z.string().url().safeParse(val).success ? val : null;
  }, z.string().url().nullable().default(null)),

  seoDescription: z.string().trim(),
  seokeywords: z.array(z.string().trim()),
});
