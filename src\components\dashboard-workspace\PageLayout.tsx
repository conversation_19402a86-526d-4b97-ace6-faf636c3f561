import React from "react";

export default function PageLayout(props: {
  title: string;
  description?: string;
  children: React.ReactNode;
}) {
  return (
    <div className="mt-4 mb-5 flex flex-col gap-y-8 px-3 sm:px-4">
      <div className="flex flex-col gap-y-1.5">
        <h1 className="text-3xl font-medium">{props.title}</h1>
        {props.description && (
          <p className="text-[14px] text-gray-500">{props.description}</p>
        )}
      </div>
      <hr className="border-dashed mb-2" />
      {props.children}
    </div>
  );
}
