import { cn } from "@/lib/utils";

export function DividerRedBottom({ className }: { className?: string }) {
  return (
    <div className={cn("absolute bottom-0 left-0 right-0 overflow-clip pt-4", className)}>
      <div className="h-8 bg-primary shadow-[0_0_8px_8px_#00000025]"></div>
      <div className="-mb-8 h-8 bg-background shadow-[0_-2px_6px_#00000070]"></div>
    </div>
  );
}

export function DividerRedTop({ className }: { className?: string }) {
  return (
    <div className={cn("absolute top-0 left-0 right-0 overflow-clip pb-4", className)}>
      <div className="relative z-20 -mt-8 h-8 bg-background shadow-[0_2px_6px_#00000070]"></div>
      <div className="relative z-10 h-8 bg-primary shadow-[0_0_8px_8px_#00000025]"></div>
    </div>
  );
}
