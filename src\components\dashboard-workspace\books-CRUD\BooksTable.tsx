"use client";
import PageLayout from "../PageLayout";
import DataTable from "@/components/dashboard/components/table/DataTable";
import { Book } from "@prisma/client";
import TableFile from "../TableFile";
import TableImage from "../TableImage";
import PriceBadge from "../PriceBadge";
import TableArray from "../TableArray";
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types";
import FormatTime from "@/components/ui/format-time";

export default function BooksTable({ data, onDelete }: { data: Book[]; onDelete?: DataTableOnDeleteFnDef<Book> }) {
  return (
    <PageLayout title="الكتب">
      <DataTable<Book>
        data={data}
        defaultPageSize={5}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/books/update", label: "تعديل" }],
          },
        }}
        columnSearch={{ columnKey: "title" }}
        createDataButton={{ href: "/admin/books/create", label: "اضافة كتاب" }}
        createColumns={[
          { accessorKey: "title", columnLabel: "عنوان الكتاب" },
          {
            accessorKey: "price",
            columnLabel: "السعر",
            cell: ({ value }) => <PriceBadge price={value} />,
          },
          {
            accessorKey: "coverImage",
            columnLabel: "صورة الغلاف",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "bookUrl",
            columnLabel: "الكتاب",
            cell: ({ value }) => <TableFile fileType="pdf" href={value} />,
          },
          { accessorKey: "pagesCount", columnLabel: "عدد الصفحات" },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) =><FormatTime dateInput={value} />,
          },
          { accessorKey: "summary", columnLabel: "ملخص الكتاب" },
          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  );
}
