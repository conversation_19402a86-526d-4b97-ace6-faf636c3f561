import prisma from "@/lib/prisma";
import { ProfileForm, ProfileResultSubmitAction } from "@/components/profile-components/profile-form";

import { fromEntries } from "@/functions/utils-fonctions";
import { getUser } from "@/utils/get-data-from-db";
import { createSession } from "@/auth/session";
import { verifySession } from "@/auth/dal";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { z } from "zod";
import Loading from "@/components/ui/loading";

// ====================================================================================
// دالة خادم تعالج طلب تغيير بيانات المستدخدم
// ====================================================================================
async function submitAction(formData: FormData): Promise<ProfileResultSubmitAction> {
  "use server";

  // التأكد من بيانات الجلسة
  const session = await verifySession();

  if (!session) {
    const callbackUrl = encodeURIComponent("/profile/profile-data");
    redirect(`/auth/login?callback-url=${callbackUrl}`);
  }

  const request = fromEntries(formData) as { name: string };

  // فحص البيانات المدخلة
  const schema = z.object({
    name: z.string().trim().min(2, "يجب أن يتكون الاسم من حرفين على الأقل.").max(40, "يجب ألا يتجاوز الاسم 40 حرفًا."),
  });

  const { success, data, error } = schema.safeParse(request);

  if (!success) {
    return {
      state: { success: false, message: "خطأ اثناء معالجة البيانات" },
      errors: error.flatten().fieldErrors,
    };
  }

  // تحديث بيانات المستخدم
  const { name } = await prisma.user.update({
    where: { id: session.id },
    omit: { password: true },
    data: {
      name: data.name,
    },
  });

  // تحديث بيانات الجلسة بالبيانات الجديدة
  await createSession({ ...session, name });

  return { state: { success: true, message: "تم تعديل بياناتك بنجاح" } };
}

// ====================================================================================
// صفحة تعديل الاسم والبريد الألكتروني
// ====================================================================================
export default function ProfileDataPage() {
  const user = getUser();

  return (
    <div>
      <h1 className="text-2xl font-semibold">الملف الشخصي</h1>
      <Suspense fallback={<Loading className="h-60" />}>
        <ContentsPage user={user} />
      </Suspense>
    </div>
  );
}

async function ContentsPage(props: { user: ReturnType<typeof getUser> }) {
  const user = await props.user;
  if (!user) redirect("/auth/login");

  return (
    <ProfileForm
      submitAction={submitAction}
      items={[
        {
          name: "name",
          label: "الاسم",
          autoComplete: "name",
          defualtValue: user?.name,
        },
      ]}
    />
  );
}
