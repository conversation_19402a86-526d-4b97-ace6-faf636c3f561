import LecturesTable from "@/components/dashboard-workspace/lectures-CRUD/LecturesTable";
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";

type Props = {
  params: Promise<{ courseId: string }>;
};

export const dynamic = "force-static";

export default async function lecturesCoursePage(props: Props) {
  const { courseId } = await props.params;

  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: { lectures: { orderBy: { createdAt: "desc" } } },
  });
  
  if (!course) return notFound();

  return (
    <LecturesTable
      data={course.lectures}
      courseId={courseId}
      courseTitle={course.title}
      onDelete={async ({ data }) => {
        "use server";

        const [updatedCourse] = await prisma.$transaction([
          // تحديث عدد المحاضرات
          prisma.course.update({
            where: { id: data[0].courseId },
            data: {
              modulesCount: {
                decrement: data.length,
              },
            },
          }),

          // حذف المحاضرات من قاعدة البيانات
          prisma.lecture.deleteMany({
            where: {
              id: {
                in: data.map((lecture) => lecture.id),
              },
            },
          }),
        ]);

        // حذف الملفات من التخزين
        const deleteFile = async (source: string | null | undefined) =>
          source ? await deleteFileInR2({ fileUrl: source }) : Promise.resolve();

        await Promise.all(
          data.flatMap((lecture) => [
            deleteFile(lecture.posterUrl),
            deleteFile(lecture.video),
            deleteFile(lecture.audio),
            deleteFile(lecture.pdf),
          ]),
        );

        data.map((lecture) => {
          revalidatePath(`/courses/${updatedCourse.id}/${lecture.id}`);
        });

        revalidatePath(`/courses/${updatedCourse.id}`);
        revalidatePath(`/courses`);

        revalidatePath(`/admin/courses`);
        revalidatePath(`/admin/courses/lectures/${updatedCourse.id}`);

        if (updatedCourse.previewInHomePage) revalidatePath(`/`);

        return {
          success: true,
          message: `تم حذف ${data.length} محاضرة وجميع ملفاتها بنجاح.`,
        };
      }}
    />
  );
}
