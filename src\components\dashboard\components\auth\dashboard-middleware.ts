import "server-only";
import { NextRequest, NextResponse } from "next/server";
import { verifyAdminSession } from "./admin-session";

/**
 * دالة تقوم بحماية لوحة التحكم من الدخول غير المصرح به, قم بإستيرادها الى دالة البرامج الوسيطه الأساسية
 * 
 * @param request
 * 
 * @example // دالة البرامج الوسيطة الرئيسية
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // ... هنا بقية الأكواد الخاصة بك

  if (
    pathname.startsWith("/dashboard-login") ||
    pathname.startsWith("/admin")
  ) {
    // دالة البرامج الوسيطة الخاصة بلوحة التحكم
    return await dashboardMiddleware(request);
  }
}

// مطابقة المسارات
export const config = {
  matcher: [
    // ... هنا المسارات الاخرى الخاصة بك
    "/admin/:path*",
    "/dashboard-login",
  ],
};
 */

export async function dashboardMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const session = await verifyAdminSession();

  if (pathname.startsWith("/admin") && !session) {
    return NextResponse.redirect(new URL("/dashboard-login", request.url));
  }

  if (pathname.startsWith("/dashboard-login") && session) {
    return NextResponse.redirect(new URL("/admin", request.url));
  }
  return NextResponse.next();
}
