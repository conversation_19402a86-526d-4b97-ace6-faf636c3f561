import { Minus } from "lucide-react";
import Image from "next/image";
import React from "react";

export default function TableImage({ src }: { src?: string | null }) {
  if (!src)
    return (
      <div className="flex size-8 items-center justify-center">
        <Minus className="size-5 text-gray-500 opacity-90 hover:opacity-100 dark:text-gray-400" />
      </div>
    );

  return (
    <a
      href={src}
      target="_blank"
      rel="noopener noreferrer"
      className="relative flex size-7 shrink-0 items-center justify-center overflow-clip rounded-lg bg-gray-100 dark:bg-gray-900"
      aria-hidden="true"
    >
      <Image
        className="h-auto min-h-full w-auto min-w-full object-cover"
        src={src}
        width={32}
        height={32}
        alt=""
      />
    </a>
  );
}
