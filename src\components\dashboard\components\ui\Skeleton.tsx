import { cx } from "@/components/dashboard/lib/utils-tremor";

function Skeleton({
  playAnimate = true,
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement> & { playAnimate?: boolean }) {
  return (
    <span
      className={cx(
        "block animate-pulse rounded-md bg-gray-300 dark:bg-gray-800",
        playAnimate ? "animate-pulse" : "animate-none",
        className,
      )}
      {...props}
    />
  );
}

export { Skeleton };
