import fs from "fs";
import path from "path";

function copyRecursive(src, dest) {
  if (!fs.existsSync(src)) return;

  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      copyRecursive(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

try {
  copyRecursive("public", ".next/standalone/public");
  copyRecursive(".next/static", ".next/standalone/.next/static");
  console.log("✅ Files copied successfully");
} catch (err) {
  console.error("❌ Copy failed:", err);
  process.exit(1);
}
