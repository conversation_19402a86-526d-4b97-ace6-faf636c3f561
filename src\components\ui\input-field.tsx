import React from "react";
import { Input } from "./input";
import { OctagonAlert } from "lucide-react";
import { cn } from "@/lib/utils";

type InputFieldProps = {
  name: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  errorMessage?: string | undefined;
  className?: string;
  type?: React.HTMLInputTypeAttribute;
  dir?: "rtl" | "ltr";
  disabled?: boolean;
  defaultValue?: string;
  containerClass?: string;
  value?: string | number | undefined;
  autoComplete?: React.HTMLInputAutoCompleteAttribute | undefined;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
};

export default function InputField(props: InputFieldProps) {
  return (
    <div className={cn("grid gap-2", props.containerClass)}>
      <label htmlFor={props.name} className={cn("text-sm", !props.label && "sr-only")}>
        {props.label ?? "حقل"}
      </label>
      <div className={props.className}>
        <Input
          id={props.name}
          dir={props.dir}
          name={props.name}
          type={props.type}
          value={props.value}
          onChange={props.onChange}
          disabled={props.disabled}
          required={props.required}
          placeholder={props.placeholder}
          autoComplete={props.autoComplete}
          defaultValue={props.defaultValue}
          className={cn("w-full", props.dir === "ltr" && "text-right")}
        />
        {props.errorMessage && (
          <div className="mt-1.5 flex items-center gap-2 text-xs text-destructive">
            <OctagonAlert className="size-4" />
            <p>{props.errorMessage}</p>
          </div>
        )}
      </div>
    </div>
  );
}
