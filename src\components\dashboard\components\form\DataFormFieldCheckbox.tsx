import React from "react";
import FieldAttachments from "../ui/FieldAttachments";
import { Checkbox } from "../ui/Checkbox";
import { DataFormFieldProps } from "./DataFormField";
import { Label } from "../ui/Label";

export default function DataFormFieldCheckbox<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>,
) {
  return (
    <FieldAttachments
      label={""}
      required={false}
      htmlFor={""}
      description={props.description}
      errorMessage={props.errorMessage}
    >
      <div className="flex items-center gap-2">
        <Checkbox
          disabled={props.isPending}
          id={props.accessorKey}
          name={props.accessorKey}
          defaultChecked={props.defaultValue}
        />
        <Label htmlFor={props.accessorKey}>{props.label}</Label>
      </div>
    </FieldAttachments>
  );
}
