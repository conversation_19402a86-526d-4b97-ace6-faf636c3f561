import Link from "next/link";

export function ConsultationsListFilterBar({ mode }: { mode?: "unRead" | "all" }) {
  return (
    <div className="sticky top-16 w-full bg-gradient-to-b from-white from-85% py-5 @min-3xl:top-0 @min-3xl:pt-0 @min-3xl:pb-5 dark:from-gray-950">
      <ul className="grid w-full grid-cols-2 place-items-center items-center justify-center gap-1 rounded-md bg-gray-200 p-1 text-gray-600 dark:bg-gray-900 dark:text-gray-400">
        <li
          data-state={mode === "all"}
          // onClick={() => props.toggleUnreadFilter(false)}
          className="inline-flex w-full cursor-pointer items-center justify-center rounded-sm text-center text-sm font-medium whitespace-nowrap select-none data-[state=true]:bg-white data-[state=true]:text-gray-800 data-[state=true]:shadow-sm dark:data-[state=true]:bg-gray-950 dark:data-[state=true]:text-gray-100"
        >
          <Link href="/admin/inbox" className="w-full px-3 py-1">
            الكل
          </Link>
        </li>
        <li
          data-state={mode === "unRead"}
          className="inline-flex w-full cursor-pointer items-center justify-center rounded-sm text-center text-sm font-medium whitespace-nowrap select-none data-[state=true]:bg-white data-[state=true]:text-gray-800 data-[state=true]:shadow-sm dark:data-[state=true]:bg-gray-950 dark:data-[state=true]:text-gray-100"
          // onClick={() => props.toggleUnreadFilter(true)}
        >
          <Link href="/admin/inbox/unread" className="w-full px-3 py-1">
            غير مقروءة
          </Link>
        </li>
      </ul>
    </div>
  );
}
