"use client";

import { formatTimeFunction } from "@/functions/utils-fonctions";
import { useEffect, useState } from "react";
import { Skeleton } from "./skeleton";

type Props = {
  dateInput: string | Date;
  className?: string;
};

export default function FormatTime({ dateInput, className }: Props) {
  const [time, setTime] = useState<string | undefined>(undefined);

  useEffect(() => {
    setTime(formatTimeFunction(dateInput));
  }, [dateInput]);

  if (!time) return <Skeleton className="h-3 w-6" />;
  return <span className={className}>{time}</span>;
}
