"use client"

import React from "react"

import { useAdminSession } from "./auth/useAdminSession"
import { Skeleton } from "./ui/Skeleton"

export default function WelcomeAdmin() {
  const session = useAdminSession()

  if (!session?.name) {
    return <Skeleton className="h-9 w-60 rounded-full" />
  }

  return (
    <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
      مرحباً، {session?.name}
    </h1>
  )
}
