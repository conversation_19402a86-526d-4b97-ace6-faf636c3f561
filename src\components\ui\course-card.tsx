import Image from "next/image";
import { Button } from "./button";
import { Course } from "@prisma/client";

import {
  Workflow,
  CheckCircle,
  CircleDollarSign,
  SquareArrowOutUpLeft,
} from "lucide-react";

import Link from "next/link";
import BadgePrice from "./badge-price";

export default function CourseCard(props: Course) {
  return (
    <div className="relative flex flex-col items-center gap-7 py-6 text-balance sm:gap-10 md:flex-row md:justify-center md:gap-10 md:px-4 md:py-10 lg:gap-16">
      {/* الخلفية */}
      <div className="absolute top-[15%] bottom-0 w-full max-w-[620px] px-3 md:top-0 md:right-[18%] md:bottom-0 lg:right-[26%]">
        <div className="h-full w-full rounded-2xl bg-linear-to-b from-secondary from-25% mix-blend-color-burn md:bg-linear-to-l"></div>
      </div>

      {/* الصورة */}
      <div className="relative aspect-square w-full max-w-[70%] drop-shadow-[0_2px_8px_#00000065] sm:max-w-md md:max-w-sm lg:max-w-md">
        <Image
          className="h-auto rounded-2xl bg-muted object-cover"
          src={props.posterUrl}
          alt={props.title}
          sizes="(max-width: 640px) 80vw, 40vw"
          fill
        />
      </div>

      {/* النصوص */}
      <div className="relative flex max-w-xl flex-col items-center gap-6 px-10 text-center text-pretty md:w-full md:max-w-md md:px-0 md:text-start lg:gap-10">
        <div className="flex flex-col items-center gap-3 md:items-start w-full lg:gap-5">
          <h3 className="line-clamp-3 max-w-fit bg-primary-gradient-x bg-clip-text py-2 text-2xl font-bold text-transparent drop-shadow-[0_2px_2px_#00000050] sm:text-3xl md:text-2xl lg:text-3xl">
            {props.title}
          </h3>
          <p className="text-sm sm:text-base md:line-clamp-5 md:text-xs lg:text-sm">
            {props.description}
          </p>
        </div>

        {/* المميزات */}
        <div className="w-full text-start text-xs font-semibold text-muted">
          <div className="grid grid-cols-2 gap-y-3 border-y py-3">
            {props.features.map((feature, index) => (
              <div key={index} className="flex flex-nowrap items-start gap-2 pl-6">
                <CheckCircle className="size-4 shrink-0 text-green-600" />
                <span className="line-clamp-2">{feature}</span>
              </div>
            ))}
          </div>
          <div className="mt-4 grid w-full grid-cols-2 text-start text-sm">
            <div className="flex flex-nowrap items-center gap-2">
              <Workflow className="size-4 text-green-600" />
              <span>المحاور:</span> {props.modulesCount}
            </div>
            <div className="flex flex-nowrap items-center gap-2">
              <CircleDollarSign className="size-4 text-green-600" />
              <span>السعر:</span>
              <BadgePrice price={props.price} />
            </div>
          </div>
        </div>
        <div className="w-full">
          <Button asChild variant="outline" className="w-full">
            <Link href={`/courses/${props.id}`}>
              مشاهدة الكورس
              <SquareArrowOutUpLeft />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
