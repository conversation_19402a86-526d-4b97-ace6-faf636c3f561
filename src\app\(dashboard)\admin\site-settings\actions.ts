"use server";

import { SiteSettings } from "@prisma/client";
import { z } from "zod";
import prisma from "@/lib/prisma";
import { revalidateTag } from "next/cache";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";

// Define a schema for site settings validation
const siteSettingsSchema = z.object({
  myStoryVideoUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  phone: z.string().trim().optional(),
  email: z.string().trim().email().optional(),
  facebookUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  twitterUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  instagramUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  youtubeUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  tiktokUrl: z.string().trim().url().or(z.string().trim().length(0)).optional(),
  subscriptionInstructions: z.string().trim().optional(),
});

// Schema for my story video file
const myStorySchema = z.object({
  myStoryVideoUrl: z.any(),
  myStoryVideoThumbnailUrl: z.any(),
});

// Server action to update site settings
export async function updateSiteSettings(updatedSettings: Partial<SiteSettings>) {
  try {
    // Validate the input data
    const validationResult = siteSettingsSchema.safeParse(updatedSettings);

    if (!validationResult.success) {
      return {
        success: false,
        message: "بيانات غير صالحة",
        errors: validationResult.error.formErrors.fieldErrors,
      };
    }

    // Update the site settings in the database
    // Using upsert to handle the case where the record doesn't exist yet
    await prisma.siteSettings.upsert({
      where: { id: 1 },
      update: validationResult.data,
      create: {
        id: 1,
        ...validationResult.data,
      },
    });

    // Revalidate the cache
    revalidateTag("siteSettings");

    return {
      success: true,
      message: "تم تحديث إعدادات الموقع بنجاح",
    };
  } catch (error) {
    console.error("Error updating site settings:", error);
    return {
      success: false,
      message: "حدث خطأ أثناء تحديث إعدادات الموقع",
    };
  }
}

// Server action to update my story video file
export async function updateMyStoryVideoUrl(params: { data: { myStoryVideoUrl: any, myStoryVideoThumbnailUrl: any } }) {
  // Validate the input data
  const validationResult = myStorySchema.safeParse(params.data);

  if (!validationResult.success) {
    return {
      success: false,
      message: "بيانات غير صالحة",
      errors: validationResult.error.formErrors.fieldErrors,
    };
  }

  try {
    // الحصول على إعدادات الموقع الحالية
    const currentSettings = await prisma.siteSettings.findUnique({ where: { id: 1 } });

    // معالجة ملف الفيديو والصورة المصغرة المرفوعة ونقلها من المجلد المؤقت إلى المجلد النهائي
    const [myStoryVideoUrl, myStoryVideoThumbnailUrl] = await Promise.all([
      handleFileUpdate(
        currentSettings?.myStoryVideoUrl || null,
        params.data.myStoryVideoUrl,
        "site-settings/videos/"
      ),
      handleFileUpdate(
        currentSettings?.myStoryVideoThumbnailUrl || null,
        params.data.myStoryVideoThumbnailUrl,
        "site-settings/images/"
      )
    ]);

    // تحديث إعدادات الموقع في قاعدة البيانات
    await prisma.siteSettings.upsert({
      where: { id: 1 },
      update: {
        myStoryVideoUrl,
        myStoryVideoThumbnailUrl
      },
      create: {
        id: 1,
        myStoryVideoUrl,
        myStoryVideoThumbnailUrl
      },
    });

    // إعادة التحقق من الكاش
    revalidateTag("siteSettings");

    return {
      success: true,
      message: "تم تحديث فيديو القصة والصورة المصغرة بنجاح",
    };
  } catch (error) {
    console.error("Error updating myStoryVideoUrl:", error);
    return {
      success: false,
      message: "حدث خطأ أثناء تحديث فيديو القصة",
    };
  }
}
