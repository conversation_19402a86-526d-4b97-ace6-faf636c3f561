import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

// إعداد Faker للغة العربية
faker.locale = 'ar';

// مواضيع متنوعة للمنشورات
const topics = [
  'العلاج النفسي',
  'الصحة النفسية',
  'التطوير الذاتي',
  'العلاقات الإنسانية',
  'إدارة الضغوط',
  'القلق والاكتئاب',
  'الثقة بالنفس',
  'التأمل والاسترخاء',
  'العلاج السلوكي المعرفي',
  'الذكاء العاطفي',
  'التواصل الفعال',
  'حل المشاكل',
  'الإبداع والابتكار',
  'إدارة الوقت',
  'التحفيز الذاتي'
];

// كلمات مفتاحية متنوعة
const keywords = [
  'علاج نفسي',
  'صحة نفسية',
  'تطوير ذاتي',
  'علاقات',
  'ضغوط',
  'قلق',
  'اكتئاب',
  'ثقة',
  'تأمل',
  'استرخاء',
  'سلوكي',
  'معرفي',
  'ذكاء عاطفي',
  'تواصل',
  'حلول',
  'إبداع',
  'وقت',
  'تحفيز',
  'نجاح',
  'سعادة'
];

// عناوين جذابة للمنشورات
const titleTemplates = [
  'كيفية التعامل مع {topic}',
  'أسرار النجاح في {topic}',
  'دليلك الشامل لفهم {topic}',
  'خطوات عملية لتحسين {topic}',
  'أهمية {topic} في حياتنا اليومية',
  'تقنيات فعالة لتطوير {topic}',
  'أخطاء شائعة في {topic} وكيفية تجنبها',
  'رحلتي مع {topic}: تجربة شخصية',
  'علامات تدل على ضرورة الاهتمام بـ {topic}',
  'نصائح الخبراء حول {topic}'
];

// محتوى متنوع للمنشورات
const contentTemplates = [
  `في عالمنا المعاصر، أصبح موضوع {topic} من أهم المواضيع التي تشغل بال الكثيرين. 

إن فهم {topic} بشكل صحيح يتطلب منا النظر إليه من زوايا متعددة. فمن ناحية، نجد أن الأبحاث الحديثة تشير إلى أهمية كبيرة لهذا الموضوع في تحسين جودة الحياة.

من خلال تجربتي المهنية، لاحظت أن الأشخاص الذين يولون اهتماماً خاصاً بـ {topic} يحققون نتائج أفضل في حياتهم الشخصية والمهنية.

هناك عدة استراتيجيات يمكن اتباعها:
• التركيز على الجوانب الإيجابية
• وضع أهداف واقعية وقابلة للتحقيق
• البحث عن الدعم المناسب عند الحاجة
• الممارسة المستمرة والصبر

في النهاية، أود أن أؤكد على أن رحلة التحسن في {topic} تحتاج إلى وقت وجهد، لكن النتائج تستحق العناء.`,

  `يسألني الكثير من الأشخاص عن أفضل الطرق للتعامل مع {topic}. الحقيقة أن هذا الموضوع معقد ومتشعب، لكن يمكن تبسيطه من خلال فهم الأساسيات.

أولاً، من المهم أن نفهم أن {topic} ليس مجرد مفهوم نظري، بل هو جزء أساسي من تجربتنا الإنسانية. كل شخص يواجه تحديات مختلفة في هذا المجال.

من خلال سنوات من الممارسة والبحث، توصلت إلى أن هناك عوامل أساسية تؤثر على {topic}:

1. البيئة المحيطة وتأثيرها
2. التجارب السابقة وكيفية التعامل معها
3. الدعم الاجتماعي المتاح
4. الأدوات والتقنيات المستخدمة

إن الاستثمار في تطوير فهمنا لـ {topic} هو استثمار في مستقبل أفضل. لا تترددوا في طلب المساعدة المهنية عند الحاجة.`,

  `في هذا المقال، سأشاركم تجربتي الشخصية والمهنية مع {topic}. هذا الموضوع الذي غيّر نظرتي للحياة بشكل جذري.

بدأت رحلتي مع {topic} منذ سنوات عديدة، عندما واجهت تحديات شخصية جعلتني أبحث عن حلول فعالة. في البداية، كان الأمر صعباً ومربكاً.

لكن مع الوقت والممارسة، اكتشفت أن {topic} يمكن أن يكون مفتاحاً لفهم أعمق للذات وللآخرين. هناك لحظات مفصلية في هذه الرحلة:

• اللحظة التي أدركت فيها أهمية الموضوع
• التحديات الأولى وكيفية التغلب عليها
• النتائج الإيجابية التي بدأت في الظهور
• التطبيق العملي في الحياة اليومية

اليوم، أستطيع القول بثقة أن فهم {topic} أصبح جزءاً لا يتجزأ من هويتي المهنية والشخصية. أتمنى أن تجدوا في هذه التجربة ما يفيدكم في رحلتكم الخاصة.`
];

// صور وهمية للمنشورات
const sampleImages = [
  'https://images.unsplash.com/photo-1544027993-37dbfe43562a?w=800&h=600',
  'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=600',
  'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=600',
  'https://images.unsplash.com/photo-1586297135537-94bc9ba060aa?w=800&h=600',
  'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=800&h=600',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600',
  'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?w=800&h=600',
  'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=800&h=600',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600',
  'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=800&h=600'
];

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function generateTitle() {
  const template = getRandomElement(titleTemplates);
  const topic = getRandomElement(topics);
  return template.replace('{topic}', topic);
}

function generateContent() {
  const template = getRandomElement(contentTemplates);
  const topic = getRandomElement(topics);
  return template.replace(/{topic}/g, topic);
}

function generateKeywords() {
  const shuffled = keywords.sort(() => 0.5 - Math.random());
  const count = Math.floor(Math.random() * 5) + 3; // 3-7 كلمات مفتاحية
  return shuffled.slice(0, count);
}

function generateSeoDescription(title) {
  const descriptions = [
    `اكتشف كل ما تحتاج لمعرفته حول ${title}. نصائح عملية ومفيدة من خبراء العلاج النفسي.`,
    `دليل شامل حول ${title} مع أحدث الأبحاث والتقنيات المثبتة علمياً.`,
    `تعلم كيفية تطبيق مبادئ ${title} في حياتك اليومية لتحقيق نتائج أفضل.`,
    `مقال متخصص يتناول ${title} بأسلوب علمي مبسط ونصائح عملية.`
  ];
  return getRandomElement(descriptions);
}

async function createBlogPosts() {
  console.log('بدء إنشاء 100 منشور وهمي...');
  
  const blogPosts = [];
  
  for (let i = 0; i < 100; i++) {
    const title = generateTitle();
    const content = generateContent();
    const image = getRandomElement(sampleImages);
    const seoKeywords = generateKeywords();
    const seoDescription = generateSeoDescription(title);
    
    // تاريخ عشوائي خلال السنة الماضية
    const randomDate = faker.date.between({
      from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      to: new Date()
    });
    
    blogPosts.push({
      title,
      content,
      image,
      seoDescription,
      seokeywords: seoKeywords,
      createdAt: randomDate
    });
    
    if ((i + 1) % 10 === 0) {
      console.log(`تم إنشاء ${i + 1} منشور...`);
    }
  }
  
  try {
    // إدراج جميع المنشورات في قاعدة البيانات
    await prisma.blogPost.createMany({
      data: blogPosts
    });
    
    console.log('✅ تم إنشاء 100 منشور وهمي بنجاح!');
    
    // عرض إحصائيات
    const totalPosts = await prisma.blogPost.count();
    console.log(`📊 إجمالي المنشورات في قاعدة البيانات: ${totalPosts}`);
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء المنشورات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
createBlogPosts();
