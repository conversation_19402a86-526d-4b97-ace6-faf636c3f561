"use client";

import { UserPayload } from "@/auth/session";
import { SessionContext } from "./create-context";
import { useEffect, useState } from "react";
import { getSession } from "@/auth/get-session";

export function SessionContextProvidar({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<UserPayload | undefined | null>(null);

  useEffect(() => {
    (async () => {
      const res = await getSession();
      setSession(res);
    })();
  }, []);

  return <SessionContext.Provider value={session}>{children}</SessionContext.Provider>;
}
