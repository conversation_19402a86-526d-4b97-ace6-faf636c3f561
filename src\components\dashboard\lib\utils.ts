/**
 * تقوم هذه الدالة بتحويل كائن `FormData` إلى كائن JavaScript عادي،
 * مع تطبيق عمليات معالجة على بعض المفاتيح حسب الشروط التالية:
 *
 * 1. يتم استبعاد أي مفتاح يبدأ بعلامة `$` بحيث لا يتم تضمينه في الكائن الناتج.
 * 2. إذا كان المفتاح يبدأ بـ `@parse`، يتم إزالة هذا الجزء (`@parse`)
 *    من اسم المفتاح، ويتم تحليل (`parse`) قيمته كنص JSON.
 * 3. جميع المفاتيح الأخرى تُحفظ كما هي دون تعديل.
 *
 * @param {FormData} formData - كائن `FormData` الذي سيتم تحويله.
 * @returns {{ [K: string]: any }} كائن JavaScript يحتوي على البيانات بعد المعالجة.
 */
export function fromEntries<TData = { [K: string]: any }>(formData: FormData): TData {
  const entries: [string, any][] = [];

  for (const [key, value] of formData.entries()) {
    if (key.startsWith("$")) continue;

    if (key.startsWith("@parse")) {
      entries.push([key.slice(6), safeParse(value)]);
    } else {
      entries.push([key, value]);
    }
  }
  return Object.fromEntries(entries) as TData;
}

export function safeParse(value: any) {
  if (typeof value !== "string") return null;

  try {
    return JSON.parse(value);
  } catch {
    return value;
  }
}


export function convertYoutubeUrlToEmbed(url: string) {
  const match = url.match(/(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
  return match ? `https://www.youtube-nocookie.com/embed/${match[1]}` : url;
}
