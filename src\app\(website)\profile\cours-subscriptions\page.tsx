import UnsubscribeDialog from "@/components/profile-components/unsubscribe-dialog";
import FormatTime from "@/components/ui/format-time";
import BadgePrice from "@/components/ui/badge-price";
import Image from "next/image";

import { unsubscribeFunction } from "@/components/profile-components/unsubscribe-dialog-action";
import { BotMessageSquare, ChevronLeft } from "lucide-react";
import { getUser } from "@/utils/get-data-from-db";
import { Button } from "@/components/ui/button";
import { redirect } from "next/navigation";
import { Prisma } from "@prisma/client";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";

export default function CoursSubscriptionsPage() {
  const user = getUser();

  const loading = (
    <div className="grid grid-cols-1 place-items-center place-content-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {Array.from({ length: 3 }).map((_, i) => (
        <Skeleton key={i} className="h-[550px] w-full max-w-72 rounded-xl" />
      ))}
    </div>
  );

  return (
    <div>
      <h1 className="text-2xl font-semibold">اشتراكات الكورسات</h1>
      <p className="mt-2 text-sm text-muted">
        هذه الصفحة تعرض كل الكورسات الخاصة بك التي قمت بالاشتراك فيها بطريقة مدفوعة او
        مجانية.
      </p>
      <hr className="my-6 border-dashed border-muted/50" />
      <Suspense fallback={loading}>
        <ContentsPage user={user} />
      </Suspense>
    </div>
  );
}

async function ContentsPage(props: { user: ReturnType<typeof getUser> }) {
  const user = await props.user;
  if (!user) redirect("/auth/login");

  if (!user?.courseOrders.length) {
    return (
      <div className="flex w-full flex-col items-center justify-center gap-2 text-balance rounded-xl border border-dashed border-muted/50 bg-muted/5 px-3 py-6 text-center text-zinc-400">
        <BotMessageSquare className="size-28" />
        <span className="text-xl font-semibold">ليس لديك اشتراكات حتا الآن</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 place-items-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {user?.courseOrders.map((order) => (
        <CourseCard key={order.id} courseOrder={order} userPassword={user.password} />
      ))}
    </div>
  );
}

type CourseOrder = Prisma.CourseOrderGetPayload<{ include: { course: true } }>;

async function CourseCard({
  courseOrder,
  userPassword,
}: {
  courseOrder: CourseOrder;
  userPassword: string;
}) {
  async function unsubscribeAction(formData: FormData) {
    "use server";

    return await unsubscribeFunction({
      formData,
      orderId: courseOrder.id,
      unsubscribeId: courseOrder.courseId,
      unsubscribeType: "courseOrders",
      userPassword: userPassword,
    });
  }
  return (
    <div className="flex w-full max-w-80 flex-col items-center gap-6 rounded-xl border border-muted/10 bg-white p-4 shadow-lg">
      <div className="relative aspect-square w-full overflow-clip rounded-lg border bg-muted shadow-xs">
        <Image
          src={courseOrder.course.posterUrl}
          alt={courseOrder.course.title}
          fill
          className="h-auto bg-muted object-cover"
          sizes="(max-width: 640px) 80vw, 40vw"
        />
      </div>
      <p className="w-full text-balance text-center text-xl font-semibold text-muted">
        {courseOrder.course.title}
      </p>
      <div className="grid w-full grid-cols-5 gap-x-1 gap-y-1 rounded-lg border border-muted/10 bg-white p-3 shadow-xs *:flex *:items-center *:gap-1 *:text-nowrap *:rounded-sm *:bg-muted/5 *:px-3 *:py-1 *:text-xs *:text-muted [&_span]:text-xs [&_span]:font-medium [&_svg]:mr-auto [&_svg]:size-4 [&_svg]:shrink-0">
        <p className="col-span-3">
          سعر الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">
          <BadgePrice price={courseOrder.price} />
        </p>
        <p className="col-span-3">
          تاريخ الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">
          <FormatTime dateInput={courseOrder.createdAt} />
        </p>
        <p className="col-span-3">
          عدد المحاور <ChevronLeft />
        </p>
        <p className="col-span-2">{courseOrder.course.modulesCount}</p>
      </div>
      <div className="flex w-full flex-col gap-3">
        <UnsubscribeDialog unsubscribeAction={unsubscribeAction}>
          <Button variant="outline">إلغاء الاشتراك</Button>
        </UnsubscribeDialog>
      </div>
    </div>
  );
}
