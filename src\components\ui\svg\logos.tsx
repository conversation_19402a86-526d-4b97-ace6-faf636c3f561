export function LogoLarge({ className }: { className?: string }) {
  return (
    <svg
      width={172}
      height={188}
      viewBox="0 0 172 188"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_48_134)" filter="url(#filter0_d_48_134)">
        <path
          d="M108.985 76.054c-1.056 3.538-4.801 5.418-8.235 6.692-14.61 5.414-30.246 7.11-45.711 7.567-3.2.094-6.402.137-9.602.148-1.558.007-3.117.003-4.68-.004-1.365-.007-3.445-.396-4.686.09 1.06 4.655 6.695 8.795 11.267 8.935 1.959.062 3.88-.457 5.785-.918 11.371-2.757 23.17-3.664 34.323-7.207a130.417 130.417 0 01-39.74 9.803c.802 3.65 3.887 6.494 7.383 7.726 3.499 1.227 7.344 1.036 10.967.266 6.117-1.296 11.799-4.154 17.199-7.337 3.599-2.12 7.105-4.399 10.607-6.674 4.113-2.671 8.279-5.386 11.56-9.047 2.472-2.743 4.469-6.448 3.563-10.04z"
          fill="url(#paint0_linear_48_134)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M134.383 47.44a12.157 12.157 0 014.259 3.831c-5.546-.32-11.06 3.564-12.669 8.925-.642 2.136-.708 4.385-.773 6.626-.015.534-.031 1.068-.054 1.6-.196 4.514-.985 9.057-2.889 13.143-3.071 6.588-8.86 11.57-15.248 14.955-6.388 3.38-13.407 5.331-20.362 7.254 3.078-3.673 6.855-6.674 10.625-9.668 4.499-3.575 8.988-7.14 12.266-11.832 1.227-1.756 2.272-3.668 2.875-5.73.642-2.204.766-4.537.567-6.823-.332-3.82-1.559-7.527-3.317-10.926-1.149-2.224-2.524-4.319-3.894-6.408l-.062-.093c-1.954-2.974-3.923-5.97-6.405-8.51l-.33-.335c-.31-.316-.624-.633-.919-.965-.424-.48-.83-.987-1.127-1.56-.16-.312-.292-.928-.292-.928s.32.112.599.241c.315.146.619.298.92.449.827.413 1.63.814 2.572 1.049 2.443.608 5.086.453 7.411-.537.627-.264 1.228-.584 1.83-.904.438-.234.877-.467 1.327-.68 6.42-3.03 14.791-1.026 19.174 4.598.**************.322.422.398.529.798 1.059 1.305 1.482.502.422 1.091.717 1.678 *************.41.206.611.314zm-10.108-.6c0 1.612-1.295 2.92-2.892 2.92-1.598 0-2.893-1.308-2.893-2.92 0-1.613 1.295-2.92 2.893-2.92 1.597 0 2.892 1.307 2.892 2.92z"
          fill="url(#paint1_linear_48_134)"
        />
        <path
          d="M106 84.51c-.471.594-1.052 1.123-1.648 1.674-4.084 3.758-9.27 6.426-14.01 9.234-5.15 3.05-10.368 5.98-15.711 8.669-4.22 2.124-8.421 4.68-13.008 5.907-.82.22-1.826.209-1.527 1.296.496 1.8 2.162 3.003 4.245 3.68 3.738 1.209 8.813.727 10.893-1.041 1.469-1.253 2.778-2.649 4.29-3.859 6.977-5.58 14.12-10.955 20.83-16.866 2.436-2.146 4.758-4.45 6.726-7.042 0 0 1.795-2.85 1.795-5.943-.004-.004-.832 1.713-2.875 4.291z"
          fill="url(#paint2_linear_48_134)"
        />
        <path
          d="M47.338.299c6.577 12.197 16.243 22.392 26.33 31.831a356.484 356.484 0 0019.345 16.837c3.963 3.204 8.036 6.369 11.182 10.393 3.142 4.025 5.311 9.098 4.794 14.199-2.362-5.602-6.524-10.264-11.203-14.105-4.676-3.841-9.894-6.944-14.984-10.206-8.924-5.717-17.545-11.984-25.217-19.325-3.934-3.765-7.672-7.895-9.961-12.852C45.188 11.797 44.56 5.533 47.338.3z"
          fill="url(#paint3_linear_48_134)"
        />
        <path
          d="M38.66 10.778c3.004 4.432 5.286 9.062 8.885 13.155 3.57 4.06 7.59 7.704 11.774 11.11a159.114 159.114 0 0018.55 13.017c9.82 5.897 20.537 10.94 27.949 19.717 2.083 2.466 3.955 5.663 3.11 8.791-6.951 1.466-13.9 2.93-20.85 4.4-6.589 1.39-13.337 2.786-20.007 1.868-6.67-.918-13.364-4.615-16.05-10.847 16.307 3.964 33.335 3.057 50.08 2.124-6.73-1.274-13.642-.839-20.487-1.033a111.701 111.701 0 01-22.773-2.999c-4.772-1.137-9.577-2.642-13.525-5.576-3.948-2.938-6.944-7.546-6.88-12.496 15.098 9.663 33.249 12.924 50.91 15.693 6.078.954 12.342 1.936 17.585 5.176-2.754-3.996-7.972-5.223-12.709-6.138C79.3 63.857 64.166 60.98 50.46 54.356c-4.854-2.343-9.605-5.248-12.893-9.543-3.289-4.295-4.88-10.235-2.929-15.297 7.473 9.908 18.19 16.874 29.404 22.058 11.217 5.184 23.08 8.773 34.608 13.215-4.858-3.949-10.914-5.997-16.714-8.316a159.5 159.5 0 01-26.429-13.557C49.8 39.28 44.186 35.136 40.544 29.4c-1.766-2.779-2.978-5.958-3.41-9.237-.303-2.376-.6-7.841 1.527-9.386z"
          fill="url(#paint4_linear_48_134)"
        />
        <path
          d="M139.826 47.34c-.264-.684-.531-1.364-.799-2.016-8.506-20.815-24.703-34.243-44.437-36.846-9.69-1.278-19.927.263-28.83 4.338l-.874-1.948c9.256-4.237 19.903-5.839 29.979-4.51 10.536 1.39 20.026 5.659 28.212 12.697 7.572 6.509 13.764 15.31 17.908 25.452.271.666.546 1.36.817 2.059l-1.976.774z"
          fill="url(#paint5_linear_48_134)"
        />
        <path
          d="M35.151 87.361a65.683 65.683 0 01-6.534-25.985l2.115-.086c.342 8.662 2.525 17.348 6.316 25.124l-1.897.947z"
          fill="url(#paint6_linear_48_134)"
        />
        <path
          d="M85.63 116.676h-.363l.01-2.138h.353c14.791 0 29.112-5.346 39.334-14.703 10.871-9.943 17.052-24.797 16.136-38.757l2.111-.14c.482 7.329-.86 14.975-3.881 22.114-2.946 6.962-7.422 13.316-12.947 18.371-10.603 9.702-25.441 15.253-40.753 15.253z"
          fill="url(#paint7_linear_48_134)"
        />
        <path
          d="M27.243 164.619h-6.222v-.84h.398c.195 0 .296-.192.296-.577 0-.115-.024-.246-.069-.385l-2.078-6.088h-7.573c-.796 2.261-1.198 3.945-1.198 5.055 0 .607.191 1.09.573 1.45.381.361.872.541 1.473.541h.917v.84H6v-.84c1.307-.069 2.484-1.462 3.535-4.178l7.107-18.279h1.023l6.133 17.873c.426 1.258.823 2.204 1.197 2.835.67 1.131 1.42 1.712 2.252 1.749v.844h-.004zm-8.056-8.955l-3.154-9.304-3.64 9.304h6.794zM48.178 160.355c-3.66 3.183-7.001 4.776-10.017 4.776-2.853 0-5.187-1.073-7.001-3.22-1.709-2.04-2.565-4.522-2.565-7.452 0-4.616 1.952-8.234 5.857-10.843 1.858-1.25 4.099-1.872 6.725-1.872 2.289 0 4.448.36 6.482 1.085v5.281h-1.23c-.15-1.692-.913-3.015-2.29-3.97-1.237-.864-2.739-1.294-4.504-1.294-2.553 0-4.57.979-6.048 2.937-1.34 1.786-2.009 4.006-2.009 6.665 0 2.786.75 5.137 2.253 7.05 1.615 2.065 3.77 3.097 6.465 3.097 2.553 0 5.183-1.102 7.886-3.306v1.066h-.004zM70.692 164.619H64.47v-.84h.397c.195 0 .297-.192.297-.577 0-.115-.025-.246-.07-.385l-2.077-6.088h-7.574c-.795 2.261-1.197 3.945-1.197 5.055 0 .607.19 1.09.572 1.45.382.361.873.541 1.473.541h.918v.84h-7.765v-.84c1.307-.069 2.484-1.462 3.535-4.178l7.107-18.279h1.023l6.133 17.873c.426 1.258.824 2.204 1.197 2.835.67 1.131 1.42 1.712 2.253 1.749v.844zm-8.057-8.955l-3.154-9.304-3.64 9.304h6.794zM94.59 151.084c0 3.998-1.287 7.255-3.856 9.766-2.57 2.511-5.845 3.769-9.819 3.769h-9.079v-.84c1.027-.012 1.725-.217 2.086-.61.366-.397.544-1.118.544-2.167v-15.15c0-1.048-.182-1.765-.544-2.15-.365-.385-1.059-.59-2.086-.611v-.839h13.621c6.088 0 9.132 2.941 9.132 8.832zm-3.26 2.273c0-6.878-2.877-10.319-8.629-10.319-2.02 0-3.697.451-5.024 1.348v17.472c1.846 1.086 3.648 1.626 5.406 1.626 2.658 0 4.736-1.048 6.238-3.146 1.34-1.868 2.01-4.195 2.01-6.981zM112.561 158.183l-1.42 7.206h-.865c-.012-.512-.231-.77-.657-.77h-12.63v-.84c1.038-.012 1.736-.213 2.097-.602.358-.389.536-1.118.536-2.179v-15.129c0-1.049-.182-1.77-.544-2.159-.365-.389-1.059-.598-2.086-.623v-.839h12.371c.751 0 1.137-.291 1.161-.877h.816v6.681h-1.267c-.044-3.31-2.021-4.969-5.925-4.969h-1.315a31.945 31.945 0 00-.207 3.413c0 3.883 1.623 5.825 4.87 5.825h1.697v1.225l-6.36.176v10.057h1.838c3.88 0 6.105-1.864 6.673-5.596h1.217zM144.065 164.619h-8.527v-.84c.901-.024 1.522-.209 1.863-.561.341-.348.511-.975.511-1.872 0-.164-.012-.361-.032-.594l-1.352-13.904c-1.977 3.544-3.385 6.313-4.229 8.308l-4.351 10.282h-.779l-8.11-20.36-.588 6.284c-.264 2.835-.398 5.08-.398 6.735 0 3.687.913 5.58 2.74 5.686v.84h-7.834v-.84c.995-.233 1.79-1.085 2.391-2.552.264-.631.601-1.831 1.007-3.601.495-2.179.86-4.522 1.091-7.03l.435-4.653c.012-.127.016-.369.016-.717 0-.84-.179-1.405-.536-1.696-.312-.266-.897-.414-1.749-.439v-.839h5.666l7.435 18.504 4.262-10.164c1.607-3.834 2.662-6.616 3.17-8.344h6.185v.839c-.937.025-1.595.164-1.976.422-.487.328-.727.934-.727 1.819 0 .139.004.275.016.402l1.315 13.957c.138 1.516.337 2.495.589 2.937.406.733 1.238 1.119 2.496 1.155v.836zM165.706 143.087c-1.258.025-2.638 1.332-4.14 3.917l-5.252 9.024v4.97c0 1.794.877 2.724 2.635 2.781v.84h-8.475v-.84c1.027-.012 1.725-.217 2.086-.61.365-.397.544-1.118.544-2.167v-4.969l-4.331-8.779c-.588-1.188-1.091-2.057-1.505-2.606-.763-1.024-1.555-1.544-2.375-1.557v-.839h6.291v.839c-.438.013-.657.168-.657.471 0 .115.024.222.069.316l5.442 10.913c2.809-4.432 4.209-7.66 4.209-9.688 0-.815-.284-1.377-.848-1.68-.406-.209-1.063-.319-1.977-.332v-.839h8.284v.835z"
          fill="#AA2556"
        />
        <path
          d="M12.837 130.258c0 1.515-.486 2.748-1.457 3.701-.972.953-2.206 1.427-3.71 1.427H4.241v-.317c.388-.003.651-.082.79-.231.138-.15.205-.426.205-.821v-5.74c0-.399-.067-.671-.206-.814-.138-.146-.401-.221-.789-.232v-.316H9.39c2.297-.004 3.447 1.113 3.447 3.343zm-1.231.862c0-2.608-1.086-3.909-3.262-3.909-.762 0-1.397.17-1.9.511v6.618c.699.412 1.38.617 2.045.617 1.005 0 1.791-.399 2.358-1.192.506-.708.759-1.59.759-2.645zM22.91 135.386h-1.573c-.317 0-.596-.191-.836-.569l-2.594-4.082v3.279c0 .681.337 1.031 1.009 1.055v.317H15.7v-.317c.388-.003.651-.082.79-.231.138-.15.205-.426.205-.821v-5.74c0-.399-.067-.671-.206-.814-.138-.146-.401-.221-.79-.232v-.316h4.167c1.234 0 1.852.487 1.852 1.457 0 1.083-.695 2.026-2.082 2.829l1.461 2.288c.654 1.025 1.258 1.553 1.815 1.583v.314h-.004zm-2.534-6.074c0-.525-.111-.984-.334-1.379-.266-.473-.64-.708-1.126-.708a1.72 1.72 0 00-1.009.33v2.765c.51.374.948.561 1.316.561.769 0 1.153-.524 1.153-1.569zM26.552 134.79c0 .16-.057.299-.172.419a.55.55 0 01-.408.177.582.582 0 01-.418-.177.577.577 0 01-.176-.419c0-.16.058-.3.176-.419a.566.566 0 01.826 0 .59.59 0 01.172.419zM43.63 127.228c-.799.075-1.197 1.403-1.197 3.984v4.48h-.307l-5.65-7.729v2.465c0 1.144.04 2.006.118 2.578.176 1.304.54 1.992 1.093 2.06v.316h-2.843v-.316c.796-.123 1.19-1.645 1.19-4.566v-2.288c0-.371-.07-.626-.212-.766s-.398-.215-.77-.221v-.317h2.15l4.76 6.476v-1.968c0-2.707-.399-4.103-1.192-4.188v-.317h2.86v.317zM53.801 135.386H51.45v-.317h.152c.074 0 .111-.071.111-.218a.497.497 0 00-.027-.146L50.9 132.4h-2.86c-.3.858-.452 1.494-.452 1.917 0 .228.07.412.215.548.146.136.331.204.557.204h.348v.317h-2.935v-.317c.492-.027.938-.555 1.336-1.583l2.685-6.925h.384l2.318 6.772c.161.476.31.834.452 1.072.253.429.536.65.85.664v.317h.003zm-3.046-3.391l-1.19-3.524-1.377 3.524h2.567zM64.632 135.386h-3.2v-.317c.384-.003.643-.082.785-.235.138-.153.21-.425.21-.817v-3.054h-3.995v3.054c0 .684.331 1.035.995 1.055v.317h-3.2v-.317c.387-.003.65-.081.789-.231.138-.15.205-.426.205-.821v-5.74c0-.398-.067-.671-.205-.814-.139-.146-.402-.221-.79-.231v-.317h3.202v.317c-.388.01-.651.085-.79.231-.138.147-.206.419-.206.814v2.274h3.994v-2.274c0-.398-.067-.671-.206-.814-.138-.146-.4-.221-.789-.231v-.317h3.201v.317c-.388.01-.65.085-.789.231-.138.147-.206.419-.206.814v5.74c0 .402.068.678.206.824.138.147.401.225.79.228v.314zM73.359 132.948l-.537 2.73h-.327c-.003-.194-.088-.292-.25-.292h-4.773v-.317c.392-.003.658-.082.793-.228.135-.15.203-.422.203-.824v-5.733c0-.399-.068-.671-.206-.818-.138-.146-.401-.224-.79-.235v-.316h4.676c.283 0 .428-.109.438-.33h.307v2.533h-.479c-.017-1.257-.762-1.883-2.24-1.883h-.495c-.054.48-.078.912-.078 1.294 0 1.47.614 2.206 1.838 2.206h.641v.463l-2.401.065v3.809h.695c1.467 0 2.307-.708 2.52-2.121h.465v-.003zM84.504 130.258c0 1.515-.486 2.748-1.457 3.701-.972.953-2.206 1.427-3.71 1.427h-3.431v-.317c.387-.003.65-.082.789-.231.138-.15.206-.426.206-.821v-5.74c0-.399-.068-.671-.206-.814-.138-.146-.402-.221-.79-.232v-.316h5.148c2.304-.004 3.45 1.113 3.45 3.343zm-1.231.862c0-2.608-1.087-3.909-3.262-3.909-.763 0-1.397.17-1.9.511v6.618c.699.412 1.38.617 2.045.617 1.005 0 1.79-.399 2.358-1.192.509-.708.759-1.59.759-2.645zM99.09 132.407c0 .834-.358 1.552-1.073 2.148-.678.555-1.437.834-2.28.834h-3.07v-.317c.388-.003.65-.081.79-.231.138-.15.205-.426.205-.821v-5.74c0-.398-.068-.671-.206-.814-.138-.146-.401-.221-.79-.231v-.317h3.961c.64 0 1.123.089 1.44.266.422.235.635.643.635 1.232 0 .909-.618 1.733-1.852 2.472.695.01 1.204.092 1.531.252.472.228.708.65.708 1.267zm-1.559-3.109c0-.545-.132-1.011-.398-1.406-.3-.446-.708-.671-1.224-.671-.432 0-.776.106-1.036.317v3.081h1.278c.918.004 1.38-.439 1.38-1.321zm.3 3.915c0-.694-.209-1.249-.627-1.664-.418-.416-.975-.623-1.663-.623h-.668v3.493c.449.357.917.538 1.407.538.489 0 .873-.171 1.16-.511.263-.313.391-.725.391-1.233zM109.644 135.386h-2.351v-.317h.152c.074 0 .111-.071.111-.218a.494.494 0 00-.027-.146l-.786-2.305h-2.86c-.3.858-.452 1.494-.452 1.917 0 .228.071.412.216.548a.78.78 0 00.556.204h.348v.317h-2.935v-.317c.492-.027.938-.555 1.336-1.583l2.685-6.925h.388l2.317 6.772c.162.476.31.834.452 1.072.253.429.536.65.85.664v.317zm-3.042-3.391l-1.191-3.524-1.376 3.524h2.567zM117.315 132.914c0 .742-.338 1.382-1.016 1.917-.627.5-1.318.749-2.074.749-.574 0-1.214-.14-1.926-.419v-2.387h.465c0 .61.169 1.144.51 1.604.371.504.85.756 1.433.756.462 0 .84-.136 1.134-.416.293-.275.438-.647.438-1.11 0-.48-.219-.932-.661-1.351-.179-.167-.6-.477-1.265-.923-.56-.374-.975-.681-1.245-.916a1.843 1.843 0 01-.452-.623 1.615 1.615 0 01-.158-.694c0-.695.317-1.277.954-1.75a3.176 3.176 0 011.933-.637c.388 0 .908.112 1.552.337v2.08h-.479c-.03-.524-.186-.973-.466-1.345-.31-.415-.715-.623-1.21-.623-.361 0-.665.113-.911.331-.25.235-.375.531-.375.895 0 .449.253.906.759 1.365.189.174.624.491 1.309.954.604.405.995.704 1.177.895.385.392.574.827.574 1.311zM128.494 135.386h-3.202v-.317c.385-.003.648-.082.786-.235.139-.153.209-.425.209-.817v-3.054h-3.993v3.054c0 .684.33 1.035.995 1.055v.317h-3.201v-.317c.387-.003.651-.081.789-.231.138-.15.206-.426.206-.821v-5.74c0-.398-.068-.671-.206-.814-.138-.146-.402-.221-.789-.231v-.317h3.201v.317c-.388.01-.651.085-.79.231-.138.147-.205.419-.205.814v2.274h3.993v-2.274c0-.398-.067-.671-.205-.814-.139-.146-.402-.221-.79-.231v-.317h3.202v.317c-.388.01-.652.085-.79.231-.138.147-.206.419-.206.814v5.74c0 .402.068.678.206.824.138.147.402.225.79.228v.314zM147.032 129.131h-.465c-.014-1.266-.678-1.903-1.99-1.903h-.355v6.786c0 .684.331 1.035.995 1.055v.317h-3.201v-.317c.388-.003.651-.082.79-.231.138-.15.205-.426.205-.821v-6.786h-.452c-1.207 0-1.831.634-1.872 1.904h-.458v-2.52h.286c.058.198.142.3.257.3h5.721c.148 0 .222-.099.222-.3h.314v2.516h.003zM157.169 135.386h-2.352v-.317h.152c.074 0 .112-.071.112-.218a.529.529 0 00-.027-.146l-.786-2.305h-2.861c-.3.858-.452 1.494-.452 1.917 0 .228.071.412.216.548a.782.782 0 00.557.204h.347v.317h-2.935v-.317c.493-.027.938-.555 1.336-1.583l2.685-6.925h.388l2.318 6.772c.161.476.31.834.452 1.072.253.429.536.65.85.664v.317zm-3.046-3.391l-1.191-3.524-1.376 3.524h2.567zM138.338 135.386h-2.351v-.317h.151c.075 0 .112-.071.112-.218a.494.494 0 00-.027-.146l-.786-2.305h-2.861c-.3.858-.452 1.494-.452 1.917 0 .228.071.412.216.548a.782.782 0 00.557.204h.347v.317h-2.935v-.317c.493-.027.938-.555 1.336-1.583l2.685-6.925h.388l2.318 6.772c.162.476.31.834.452 1.072.253.429.536.65.85.664v.317zm-3.046-3.391l-1.191-3.524-1.376 3.524h2.567zM168 135.386h-3.201v-.317c.384-.003.647-.082.786-.235.138-.153.209-.425.209-.817v-3.054H161.8v3.054c0 .684.331 1.035.995 1.055v.317h-3.201v-.317c.388-.003.651-.081.789-.231.139-.15.206-.426.206-.821v-5.74c0-.398-.067-.671-.206-.814-.138-.146-.401-.221-.789-.231v-.317h3.201v.317c-.388.01-.651.085-.789.231-.138.147-.206.419-.206.814v2.274h3.994v-2.274c0-.398-.068-.671-.206-.814-.138-.146-.401-.221-.789-.231v-.317H168v.317c-.388.01-.651.085-.789.231-.139.147-.206.419-.206.814v5.74c0 .402.067.678.206.824.138.147.401.225.789.228v.314zM35.953 173.049h-.499c0-.742-.22-1.275-.656-1.599-.378-.281-.952-.421-1.723-.421h-.524c-.05.508-.075.968-.075 1.375 0 .846.132 1.455.396 1.829.314.45.867.673 1.655.673h.574v.483l-2.547.057v2.761c0 .72.35 1.091 1.053 1.116v.335h-3.392v-.335c.41-.003.688-.086.834-.244.147-.159.218-.45.218-.868v-6.07c0-.421-.071-.709-.218-.86-.146-.155-.424-.234-.834-.245v-.335h5.243c.107 0 .171-.104.192-.309h.303v2.657zM47.795 174.751c0 .925-.257 1.807-.774 2.646s-1.184 1.469-2.001 1.89a5.033 5.033 0 01-2.333.576c-1.18 0-2.175-.407-2.978-1.22-.802-.814-1.205-1.818-1.205-3.013 0-1.365.495-2.56 1.49-3.593.996-1.03 2.166-1.545 3.507-1.545 1.547 0 2.753.634 3.62 1.905.45.655.674 1.44.674 2.354zm-1.17 1.231c0-.86-.175-1.666-.528-2.422-.389-.828-.924-1.44-1.612-1.844-.66-.381-1.277-.576-1.847-.576-.91 0-1.638.324-2.187.976-.517.616-.774 1.393-.774 2.333 0 1.245.357 2.336 1.066 3.265.774 1.015 1.762 1.519 2.965 1.519.863 0 1.572-.327 2.132-.983.521-.622.785-1.378.785-2.268zM58.452 179.654H56.79c-.335 0-.631-.201-.884-.601l-2.743-4.316v3.467c0 .72.357 1.09 1.066 1.116v.334H50.83v-.334c.41-.004.689-.087.835-.245.146-.159.217-.45.217-.868v-6.069c0-.422-.071-.71-.217-.861-.146-.155-.425-.234-.835-.245v-.334h4.405c1.306 0 1.958.514 1.958 1.54 0 1.145-.735 2.142-2.2 2.992l1.544 2.419c.692 1.084 1.33 1.642 1.919 1.674v.331h-.004zm-2.682-6.422c0-.554-.118-1.04-.353-1.458-.282-.5-.678-.749-1.191-.749-.378 0-.735.115-1.067.349v2.924c.539.396 1.002.594 1.391.594.817 0 1.22-.555 1.22-1.66zM73.18 173.041h-.493c-.014-1.339-.717-2.012-2.104-2.012h-.375v7.175c0 .723.35 1.094 1.052 1.116v.334h-3.385v-.334c.41-.004.689-.087.835-.245.146-.159.218-.45.218-.868v-7.175h-.478c-1.277 0-1.94.67-1.98 2.013h-.485v-2.664h.303c.06.209.15.317.271.317h6.05c.156 0 .235-.105.235-.317h.332v2.66h.003zM84.66 179.654h-3.385v-.334c.407-.004.682-.087.831-.249.147-.162.222-.45.222-.864v-3.229h-4.223v3.229c0 .724.35 1.095 1.052 1.116v.335h-3.385v-.335c.41-.003.688-.086.835-.245.146-.158.217-.45.217-.867v-6.07c0-.421-.071-.709-.217-.86-.147-.155-.425-.234-.835-.245v-.335h3.385v.335c-.41.011-.688.09-.835.245-.146.155-.217.443-.217.86v2.405h4.223v-2.405c0-.421-.072-.709-.218-.86-.146-.155-.424-.234-.835-.245v-.335h3.385v.335c-.41.011-.688.09-.834.245-.147.155-.218.443-.218.86v6.07c0 .425.071.716.218.871.146.155.424.238.834.241v.331zM93.89 177.077l-.566 2.887h-.346c-.004-.205-.093-.31-.264-.31h-5.047v-.334c.414-.004.695-.087.838-.242.143-.158.214-.446.214-.871v-6.062c0-.421-.071-.709-.217-.864-.147-.155-.425-.238-.835-.249v-.334h4.943c.3 0 .453-.116.464-.35h.325v2.679h-.507c-.018-1.329-.806-1.991-2.368-1.991H90a12.31 12.31 0 00-.082 1.368c0 1.555.649 2.333 1.943 2.333h.678v.489l-2.54.069v4.028h.735c1.552 0 2.44-.749 2.665-2.243h.492v-.003zM108.397 177.077l-.568 2.887h-.345c-.004-.205-.093-.31-.264-.31h-5.047v-.334c.413-.004.695-.087.838-.242.143-.158.214-.446.214-.871v-6.062c0-.421-.071-.709-.218-.864-.146-.155-.424-.238-.834-.249v-.334h4.943c.3 0 .453-.116.464-.35h.324v2.679h-.506c-.018-1.329-.806-1.991-2.368-1.991h-.525c-.057.508-.082.965-.082 1.368 0 1.555.649 2.333 1.944 2.333h.678v.489l-2.54.069v4.028h.735c1.552 0 2.44-.749 2.664-2.243h.493v-.003zM117.328 177.091l-.596 2.895h-.325c-.003-.22-.103-.328-.296-.328H111.1v-.335c.41-.003.688-.086.835-.245.146-.158.217-.45.217-.867v-6.063c0-.421-.071-.709-.217-.864-.147-.154-.425-.237-.835-.248v-.335h3.399v.335c-.421.011-.702.09-.845.241-.143.151-.214.443-.214.871v7.175h.899c1.398 0 2.229-.741 2.493-2.228h.496v-.004zM123.313 179.654h-3.385v-.334c.41-.004.688-.087.834-.245.147-.159.218-.45.218-.868v-6.069c0-.422-.071-.71-.218-.861-.146-.155-.424-.234-.834-.245v-.334h3.385v.334c-.418.011-.696.09-.839.245-.142.155-.214.443-.214.861v6.069c0 .72.35 1.091 1.053 1.116v.331zM133.178 173.041h-.492c-.015-1.339-.717-2.012-2.105-2.012h-.374v7.175c0 .723.349 1.094 1.052 1.116v.334h-3.385v-.334c.41-.004.689-.087.835-.245.146-.159.217-.45.217-.868v-7.175h-.478c-1.276 0-1.936.67-1.979 2.013h-.485v-2.664h.303c.061.209.15.317.271.317h6.049c.157 0 .236-.105.236-.317h.331v2.66h.004zM142.016 177.077l-.567 2.887h-.346c-.003-.205-.093-.31-.264-.31h-5.05v-.334c.414-.004.695-.087.838-.242.143-.158.214-.446.214-.871v-6.062c0-.421-.071-.709-.218-.864-.146-.155-.424-.238-.834-.249v-.334h4.943c.3 0 .453-.116.464-.35h.324v2.679h-.506c-.018-1.329-.806-1.991-2.368-1.991h-.528c-.057.508-.082.965-.082 1.368 0 1.555.649 2.333 1.944 2.333h.677v.489l-2.539.069v4.028h.735c1.551 0 2.439-.749 2.664-2.243h.499v-.003zM26.923 176.479H6.239a.304.304 0 01-.303-.306v-.079c0-.169.136-.306.303-.306h20.684c.014 0 .025.011.025.025v.645c0 .01-.011.021-.025.021zM165.699 176.479h-20.683a.025.025 0 01-.025-.025v-.644c0-.015.011-.026.025-.026h20.683c.168 0 .303.137.303.306v.08a.305.305 0 01-.303.309z"
          fill="#3E384D"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_48_134"
          x={0}
          y={0}
          width={172}
          height={188}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_48_134" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_48_134" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_48_134"
          x1={81.001}
          y1={103.6}
          x2={69.31}
          y2={76.311}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={0.114} stopColor="#9A205B" />
          <stop offset={0.54} stopColor="#63106B" />
          <stop offset={0.747} stopColor="#4E0971" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_48_134"
          x1={120.079}
          y1={87.116}
          x2={88.18}
          y2={46.665}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.085} stopColor="#67106A" />
          <stop offset={0.197} stopColor="#7F1863" />
          <stop offset={0.322} stopColor="#921E5D" />
          <stop offset={0.468} stopColor="#A02259" />
          <stop offset={0.652} stopColor="#A82457" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_48_134"
          x1={60.039}
          y1={97.924}
          x2={108.87}
          y2={97.924}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_48_134"
          x1={86.535}
          y1={19.596}
          x2={69.531}
          y2={54.136}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_48_134"
          x1={26.356}
          y1={32.661}
          x2={108.323}
          y2={77.675}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_48_134"
          x1={64.886}
          y1={26.622}
          x2={141.802}
          y2={26.622}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_48_134"
          x1={28.62}
          y1={74.325}
          x2={37.052}
          y2={74.325}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_48_134"
          x1={85.268}
          y1={88.806}
          x2={143.312}
          y2={88.806}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <clipPath id="clip0_48_134">
          <path fill="#fff" transform="translate(4)" d="M0 0H164V180H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function LogoLargeFooter({ className }: { className?: string }) {
  return (
    <svg
      width={324}
      height={270}
      viewBox="0 0 324 270"
      xmlns="http://www.w3.org/2000/svg"
      className={`fill-background ${className}`}
    >
      <g filter="url(#filter0_d_166_6)">
        <path d="M189.052 86.832c-1.209 4.056-5.498 6.21-9.433 7.67-16.733 6.207-34.642 8.15-52.356 8.674-3.664.107-7.333.157-10.997.169-1.785.009-3.571.004-5.36-.004-1.565-.008-3.946-.454-5.368.103 1.213 5.336 7.668 10.081 12.905 10.242 2.243.07 4.445-.524 6.627-1.052 13.023-3.161 26.537-4.201 39.312-8.261a149.305 149.305 0 01-45.518 11.236c.919 4.184 4.453 7.444 8.456 8.855 4.008 1.407 8.412 1.188 12.563.305 7.006-1.485 13.513-4.761 19.698-8.409 4.122-2.431 8.138-5.043 12.15-7.65 4.71-3.062 9.482-6.173 13.24-10.37 2.831-3.144 5.119-7.39 4.081-11.508z" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M218.143 54.035a13.936 13.936 0 014.878 4.39c-6.353-.367-12.668 4.086-14.511 10.23-.735 2.449-.81 5.027-.885 7.595-.018.612-.036 1.224-.063 1.834-.224 5.174-1.127 10.382-3.309 15.065-3.517 7.551-10.147 13.262-17.464 17.141-7.317 3.875-15.356 6.111-23.322 8.315 3.525-4.21 7.851-7.649 12.169-11.082 5.154-4.097 10.295-8.184 14.049-13.561 1.406-2.014 2.603-4.205 3.293-6.57.736-2.524.878-5.198.65-7.819-.38-4.378-1.786-8.628-3.8-12.523-1.316-2.55-2.89-4.95-4.46-7.345l-.07-.108c-2.239-3.408-4.494-6.841-7.337-9.754l-.377-.384a28.652 28.652 0 01-1.053-1.106c-.486-.549-.952-1.13-1.291-1.787-.184-.359-.335-1.064-.335-1.064s.368.128.686.276c.361.168.71.342 1.055.514.947.474 1.867.934 2.945 1.203 2.798.697 5.825.52 8.489-.615.718-.303 1.407-.67 2.096-1.037a36.203 36.203 0 011.519-.779c7.354-3.474 16.942-1.176 21.963 ***********.246.322.368.484.457.606.915 1.214 1.494 1.699.576.483 1.25.821 1.923 *************.47.235.7.36zm-11.577-.689c0 1.848-1.484 3.347-3.313 3.347-1.83 0-3.314-1.499-3.314-3.347 0-1.848 1.484-3.346 3.314-3.346 1.829 0 3.313 1.498 3.313 3.346z"
        />
        <path d="M185.633 96.524c-.539.681-1.205 1.287-1.887 1.919-4.678 4.308-10.618 7.365-16.047 10.584-5.899 3.495-11.876 6.854-17.996 9.936-4.832 2.435-9.645 5.365-14.898 6.772-.94.251-2.092.239-1.749 1.485.568 2.063 2.476 3.442 4.862 4.217 4.281 1.387 10.094.834 12.476-1.192 1.683-1.436 3.182-3.037 4.914-4.424 7.991-6.395 16.174-12.556 23.858-19.332 2.79-2.459 5.45-5.1 7.705-8.07 0 0 2.055-3.269 2.055-6.813-.004-.005-.952 1.964-3.293 4.918zM118.443 0c7.533 13.98 18.604 25.666 30.157 36.485a408.605 408.605 0 0022.159 19.3c4.538 3.672 9.204 7.299 12.807 11.912 3.599 4.614 6.083 10.428 5.49 16.275-2.704-6.42-7.472-11.765-12.831-16.167-5.356-4.403-11.333-7.96-17.162-11.699-10.222-6.552-20.096-13.736-28.883-22.15-4.506-4.316-8.787-9.05-11.41-14.731C115.98 13.18 115.261 6 118.443 0z" />
        <path d="M108.504 12.012c3.44 5.08 6.054 10.386 10.176 15.078 4.09 4.654 8.694 8.83 13.486 12.734a182.206 182.206 0 0021.247 14.92c11.246 6.76 23.523 12.54 32.012 22.6 2.385 2.827 4.53 6.491 3.562 10.077-7.962 1.68-15.92 3.36-23.882 5.043-7.546 1.592-15.275 3.194-22.914 2.141-7.64-1.052-15.308-5.29-18.384-12.433 18.678 4.544 38.181 3.504 57.361 2.435-7.709-1.46-15.626-.961-23.466-1.184a127.863 127.863 0 01-26.084-3.438c-5.466-1.303-10.969-3.028-15.491-6.391-4.522-3.367-7.954-8.65-7.88-14.323 17.292 11.075 38.082 14.814 58.312 17.987 6.961 1.094 14.135 2.22 20.14 5.934-3.154-4.58-9.13-5.988-14.555-7.036-17.093-3.305-34.427-6.602-50.126-14.195-5.56-2.686-11.002-6.016-14.768-10.938-3.767-4.923-5.589-11.732-3.354-17.534 8.558 11.356 20.835 19.34 33.678 25.283 12.848 5.942 26.436 10.056 39.639 15.148-5.564-4.527-12.5-6.875-19.143-9.532a182.626 182.626 0 01-30.271-15.54c-6.537-4.168-12.967-8.917-17.138-15.49-2.022-3.186-3.411-6.83-3.905-10.589-.348-2.723-.687-8.987 1.748-10.757zM224.377 53.92c-.302-.785-.609-1.565-.915-2.312-9.743-23.858-28.294-39.25-50.898-42.233-11.099-1.465-22.824.301-33.021 4.972l-1-2.232c10.601-4.857 22.795-6.693 34.336-5.17 12.068 1.592 22.938 6.486 32.314 14.553 8.673 7.46 15.765 17.55 20.512 29.174.31.763.625 1.56.935 2.36l-2.263.887zM104.484 99.792c-4.49-9.218-7.08-19.517-7.484-29.784l2.422-.099c.393 9.928 2.893 19.885 7.235 28.798l-2.173 1.085zM162.302 133.393h-.416l.012-2.451h.404c16.942 0 33.344-6.128 45.052-16.852 12.452-11.397 19.532-28.423 18.482-44.425l2.418-.16c.552 8.4-.984 17.165-4.445 25.348-3.374 7.98-8.501 15.263-14.829 21.057-12.145 11.12-29.14 17.483-46.678 17.483z" />
      </g>
      <g filter="url(#filter1_d_166_6)">
        <path d="M46.19 238.2H33.891v-1.658h.786c.386 0 .586-.38.586-1.14 0-.226-.048-.485-.136-.76l-4.108-12.018h-14.97c-1.573 4.464-2.367 7.788-2.367 9.98 0 1.197.377 2.151 1.13 2.863.755.711 1.726 1.067 2.913 1.067h1.813v1.658H4.198v-1.658c2.584-.137 4.91-2.887 6.988-8.249L25.235 192.2h2.021l12.123 35.285c.842 2.482 1.629 4.351 2.367 5.596 1.323 2.232 2.808 3.38 4.452 3.453v1.666h-.008zm-15.925-17.678l-6.234-18.366-7.197 18.366h13.43zM87.19 229.805c-7.289 6.261-13.94 9.395-19.944 9.395-5.681 0-10.328-2.111-13.94-6.333-3.402-4.013-5.108-8.895-5.108-14.656 0-9.081 3.887-16.196 11.662-21.329 3.7-2.457 8.162-3.682 13.39-3.682 4.558 0 8.857.709 12.906 2.135v10.386h-2.449c-.299-3.327-1.818-5.93-4.557-7.807-2.465-1.7-5.455-2.546-8.97-2.546-5.084 0-9.1 1.925-12.042 5.777-2.666 3.513-4 7.88-4 13.109 0 5.479 1.495 10.104 4.485 13.867 3.217 4.061 7.508 6.092 12.873 6.092 5.084 0 10.32-2.168 15.702-6.503v2.095h-.008zM133.198 238.2h-12.006v-1.658h.768c.376 0 .571-.38.571-1.14 0-.226-.047-.485-.133-.76l-4.01-12.018h-14.614c-1.535 4.464-2.31 7.788-2.31 9.98 0 1.197.368 2.151 1.104 2.863.736.711 1.684 1.067 2.843 1.067h1.77v1.658H92.198v-1.658c2.522-.137 4.794-2.887 6.822-8.249l13.714-36.085h1.973l11.834 35.285c.823 2.482 1.59 4.351 2.311 5.596 1.292 2.232 2.741 3.38 4.346 3.453v1.666zm-15.546-17.678l-6.085-18.366-7.026 18.366h13.111zM180.198 211.575c0 7.865-2.488 14.271-7.456 19.211-4.968 4.94-11.302 7.414-18.986 7.414h-17.558v-1.652c1.986-.024 3.336-.427 4.035-1.201.706-.781 1.051-2.2 1.051-4.263v-29.8c0-2.063-.353-3.474-1.051-4.231-.707-.758-2.049-1.161-4.035-1.201V194.2h26.341c11.773 0 17.659 5.786 17.659 17.375zm-6.302 4.472c0-13.53-5.565-20.3-16.687-20.3-3.908 0-7.15.887-9.716 2.652v34.37c3.571 2.135 7.056 3.199 10.454 3.199 5.141 0 9.16-2.063 12.064-6.189 2.59-3.675 3.885-8.252 3.885-13.732zM215.198 225.099l-2.736 14.101h-1.666c-.023-1.002-.445-1.507-1.266-1.507h-24.332v-1.643c2.002-.024 3.347-.417 4.043-1.179.688-.761 1.032-2.188 1.032-4.264v-29.605c0-2.052-.352-3.463-1.048-4.224-.704-.762-2.041-1.171-4.019-1.219v-1.643h23.831c1.447 0 2.19-.569 2.236-1.716h1.572v13.075h-2.439c-.086-6.477-3.894-9.724-11.416-9.724h-2.533c-.266 2.485-.399 4.714-.399 6.678 0 7.599 3.128 11.399 9.383 11.399h3.268v2.397l-12.252.345v19.68h3.542c7.475 0 11.759-3.648 12.854-10.951h2.345zM277.198 237.61h-16.733v-1.63c1.768-.047 2.987-.405 3.656-1.089.669-.676 1.003-1.892 1.003-3.633 0-.318-.024-.7-.064-1.153l-2.652-26.984c-3.878 6.877-6.642 12.251-8.299 16.123l-8.538 19.956h-1.529l-15.913-39.514-1.155 12.196c-.517 5.502-.78 9.859-.78 13.071 0 7.155 1.792 10.828 5.376 11.035v1.63h-15.372v-1.63c1.952-.453 3.513-2.107 4.691-4.953.518-1.224 1.179-3.554 1.976-6.989.971-4.229 1.688-8.777 2.142-13.643l.852-9.031c.024-.247.032-.716.032-1.392 0-1.63-.35-2.727-1.051-3.291-.613-.517-1.76-.803-3.433-.851v-1.63h11.119l14.591 35.913 8.362-19.726c3.154-7.441 5.225-12.84 6.22-16.195h12.138v1.63c-1.839.048-3.13.318-3.878.819-.956.636-1.426 1.813-1.426 3.53 0 .27.008.533.032.779l2.58 27.088c.271 2.941.661 4.841 1.155 5.7.797 1.423 2.429 2.171 4.898 2.242v1.622zM319.198 195.844c-2.478.048-5.197 2.619-8.155 7.704l-10.346 17.753v9.775c0 3.53 1.727 5.359 5.189 5.472v1.652h-16.694v-1.652c2.023-.024 3.398-.427 4.11-1.201.719-.781 1.071-2.2 1.071-4.263v-9.775l-8.531-17.269c-1.159-2.337-2.151-4.046-2.966-5.126-1.503-2.014-3.063-3.038-4.678-3.062V194.2h12.393v1.652c-.863.024-1.295.331-1.295.927 0 .226.048.435.136.62l10.722 21.469c5.532-8.72 8.291-15.07 8.291-19.059 0-1.604-.56-2.708-1.671-3.304-.8-.411-2.095-.629-3.894-.653V194.2h16.318v1.644z" />
      </g>
      <path d="M17.087 168.846c0 3.014-.966 5.466-2.897 7.363-1.931 1.897-4.386 2.838-7.377 2.838H0v-.63c.771-.007 1.294-.162 1.57-.46.274-.298.408-.847.408-1.633v-11.42c0-.793-.134-1.335-.409-1.619-.275-.291-.798-.44-1.569-.461v-.63h10.233c4.567-.006 6.854 2.215 6.854 6.652zm-2.448 1.714c0-5.189-2.16-7.776-6.484-7.776-1.516 0-2.777.338-3.776 1.016v13.168c1.388.819 2.743 1.226 4.064 1.226 1.998 0 3.56-.793 4.687-2.371 1.006-1.409 1.51-3.163 1.51-5.263zM37.111 179.047h-3.125c-.63 0-1.187-.379-1.663-1.131l-5.157-8.122v6.523c0 1.355.67 2.053 2.005 2.1v.63h-6.39v-.63c.77-.007 1.294-.162 1.569-.46.275-.299.409-.847.409-1.633v-11.42c0-.793-.134-1.335-.41-1.619-.274-.291-.797-.44-1.569-.461v-.63h8.282c2.455 0 3.682.969 3.682 2.899 0 2.154-1.382 4.031-4.138 5.629l2.904 4.552c1.301 2.039 2.501 3.089 3.608 3.15v.623h-.007zm-5.036-12.084c0-1.043-.221-1.958-.664-2.743-.53-.942-1.274-1.409-2.24-1.409-.71 0-1.381.216-2.005.657v5.5c1.013.745 1.884 1.118 2.616 1.118 1.529 0 2.293-1.044 2.293-3.123zM44.354 177.862c0 .318-.114.596-.342.833a1.092 1.092 0 01-.812.352c-.322 0-.596-.121-.831-.352a1.143 1.143 0 01-.349-.833c0-.318.114-.596.349-.833s.51-.359.818-.359c.322 0 .597.122.825.359.228.237.342.521.342.833zM78.306 162.817c-1.589.149-2.38 2.791-2.38 7.925v8.915h-.61L64.082 164.28v4.905c0 2.275.08 3.989.235 5.127.348 2.594 1.073 3.963 2.172 4.098v.63h-5.653v-.63c1.583-.244 2.367-3.271 2.367-9.083v-4.552c0-.738-.14-1.246-.422-1.524s-.791-.427-1.53-.44v-.63h4.273l9.462 12.883v-3.915c0-5.385-.791-8.162-2.367-8.332v-.63h5.686v.63zM98.525 179.047h-4.674v-.63h.302c.147 0 .221-.142.221-.434 0-.088-.02-.182-.054-.291l-1.562-4.586H87.07c-.597 1.707-.898 2.974-.898 3.814 0 .454.14.82.429 1.091.288.271.657.406 1.106.406h.691v.63h-5.834v-.63c.979-.054 1.864-1.104 2.655-3.15l5.338-13.777h.765l4.607 13.472c.322.949.617 1.66.898 2.134.503.854 1.067 1.294 1.69 1.321v.63h.007zM92.47 172.3l-2.367-7.01-2.736 7.01h5.103zM120.058 179.047h-6.364v-.63c.765-.007 1.281-.162 1.563-.467.275-.305.416-.847.416-1.626v-6.076h-7.94v6.076c0 1.362.657 2.059 1.978 2.1v.63h-6.364v-.63c.771-.007 1.294-.163 1.569-.461.275-.298.409-.846.409-1.632v-11.42c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.569-.461v-.63h6.364v.63c-.771.02-1.294.169-1.569.461-.275.291-.409.833-.409 1.619v4.524h7.94v-4.524c0-.793-.135-1.335-.41-1.619-.275-.292-.798-.441-1.569-.461v-.63h6.364v.63c-.771.02-1.294.169-1.569.461-.275.291-.409.833-.409 1.619v11.42c0 .799.134 1.348.409 1.639.275.291.798.447 1.569.454v.623zM137.407 174.197l-1.066 5.433h-.651c-.007-.386-.174-.583-.496-.583h-9.489v-.63c.778-.006 1.308-.162 1.576-.453.268-.298.402-.84.402-1.64v-11.406c0-.793-.134-1.335-.409-1.626-.275-.291-.798-.447-1.569-.467v-.63h9.294c.564 0 .852-.217.872-.657h.61v5.039h-.952c-.033-2.499-1.515-3.746-4.453-3.746h-.985a23.035 23.035 0 00-.155 2.574c0 2.927 1.221 4.39 3.655 4.39h1.274v.921l-4.774.129v7.579h1.381c2.917 0 4.587-1.409 5.009-4.22h.926v-.007zM159.564 168.846c0 3.014-.966 5.466-2.897 7.363-1.932 1.897-4.386 2.838-7.377 2.838h-6.82v-.63c.771-.007 1.294-.162 1.569-.46.275-.298.409-.847.409-1.633v-11.42c0-.793-.134-1.335-.409-1.619-.275-.291-.798-.44-1.569-.461v-.63h10.233c4.581-.006 6.861 2.215 6.861 6.652zm-2.448 1.714c0-5.189-2.159-7.776-6.485-7.776-1.515 0-2.776.338-3.775 1.016v13.168c1.388.819 2.742 1.226 4.064 1.226 1.998 0 3.56-.793 4.687-2.371 1.013-1.409 1.509-3.163 1.509-5.263zM188.561 173.12c0 1.66-.711 3.089-2.133 4.274-1.348 1.104-2.857 1.66-4.533 1.66h-6.103v-.63c.772-.007 1.295-.163 1.57-.461.275-.298.409-.846.409-1.632v-11.42c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.57-.461v-.63h7.873c1.274 0 2.233.176 2.864.529.838.467 1.26 1.28 1.26 2.452 0 1.808-1.227 3.447-3.681 4.917 1.381.021 2.394.183 3.044.501.939.454 1.409 1.294 1.409 2.52zm-3.099-6.184c0-1.084-.261-2.012-.791-2.798-.597-.887-1.408-1.334-2.434-1.334-.859 0-1.543.21-2.059.63v6.13h2.542c1.824.007 2.742-.874 2.742-2.628zm.597 7.79c0-1.382-.415-2.486-1.247-3.313-.832-.826-1.938-1.239-3.306-1.239h-1.328v6.949c.892.712 1.824 1.071 2.797 1.071.972 0 1.736-.339 2.306-1.016.523-.624.778-1.443.778-2.452zM209.544 179.047h-4.674v-.63h.301c.148 0 .222-.142.222-.434 0-.088-.021-.182-.054-.291l-1.563-4.586h-5.686c-.597 1.707-.899 2.974-.899 3.814 0 .454.141.82.429 1.091.289.271.657.406 1.107.406h.69v.63h-5.834v-.63c.979-.054 1.865-1.104 2.656-3.15l5.338-13.777h.771l4.607 13.472c.322.949.617 1.66.899 2.134.503.854 1.066 1.294 1.69 1.321v.63zm-6.049-6.747l-2.367-7.01-2.737 7.01h5.104zM224.793 174.129c0 1.477-.67 2.75-2.018 3.814-1.247.995-2.622 1.49-4.124 1.49-1.14 0-2.414-.278-3.829-.833v-4.749h.925c0 1.213.335 2.276 1.013 3.191.737 1.002 1.69 1.504 2.85 1.504.918 0 1.669-.271 2.253-.827.583-.548.872-1.287.872-2.208 0-.955-.436-1.856-1.315-2.689-.355-.332-1.193-.948-2.514-1.836-1.114-.745-1.939-1.355-2.475-1.822a3.678 3.678 0 01-.899-1.239 3.203 3.203 0 01-.315-1.382c0-1.382.631-2.54 1.898-3.482a6.31 6.31 0 013.843-1.267c.771 0 1.803.224 3.084.671v4.139h-.952c-.06-1.043-.369-1.938-.925-2.676-.617-.826-1.422-1.239-2.408-1.239-.717 0-1.321.223-1.81.657-.497.467-.745 1.056-.745 1.781 0 .894.503 1.802 1.509 2.716.376.346 1.241.976 2.602 1.897 1.2.806 1.978 1.402 2.34 1.781.765.779 1.14 1.646 1.14 2.608zM247.017 179.047h-6.364v-.63c.765-.007 1.288-.162 1.563-.467.275-.305.415-.847.415-1.626v-6.076h-7.94v6.076c0 1.362.658 2.059 1.979 2.1v.63h-6.364v-.63c.771-.007 1.294-.163 1.569-.461.275-.298.409-.846.409-1.632v-11.42c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.569-.461v-.63h6.364v.63c-.771.02-1.295.169-1.569.461-.275.291-.41.833-.41 1.619v4.524h7.94v-4.524c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.569-.461v-.63h6.364v.63c-.771.02-1.294.169-1.569.461-.275.291-.409.833-.409 1.619v11.42c0 .799.134 1.348.409 1.639.275.291.798.447 1.569.454v.623zM282.315 166.604h-.925c-.027-2.519-1.348-3.786-3.957-3.786h-.704v13.5c0 1.361.657 2.059 1.978 2.1v.629h-6.364v-.629c.772-.007 1.295-.163 1.57-.461.275-.298.409-.847.409-1.633v-13.499h-.899c-2.401 0-3.641 1.26-3.722 3.786h-.912v-5.012h.57c.114.393.282.596.51.596h11.373c.295 0 .443-.197.443-.596h.624v5.005h.006zM302.467 179.047h-4.674v-.63h.302c.147 0 .221-.142.221-.434a.988.988 0 00-.054-.291l-1.562-4.586h-5.687c-.597 1.707-.898 2.974-.898 3.814 0 .454.14.82.429 1.091.288.271.657.406 1.106.406h.691v.63h-5.834v-.63c.979-.054 1.864-1.104 2.655-3.15l5.338-13.777h.772l4.607 13.472c.321.949.617 1.66.898 2.134.503.854 1.066 1.294 1.69 1.321v.63zm-6.055-6.747l-2.368-7.01-2.736 7.01h5.104zM267.034 179.047h-4.674v-.63h.302c.148 0 .221-.142.221-.434 0-.088-.02-.182-.053-.291l-1.563-4.586h-5.686c-.597 1.707-.899 2.974-.899 3.814 0 .454.141.82.429 1.091.288.271.657.406 1.107.406h.69v.63h-5.834v-.63c.979-.054 1.864-1.104 2.656-3.15l5.338-13.777h.771l4.607 13.472c.322.949.617 1.66.899 2.134.503.854 1.066 1.294 1.689 1.321v.63zm-6.055-6.747l-2.367-7.01-2.736 7.01h5.103zM324 179.047h-6.364v-.63c.765-.007 1.288-.162 1.563-.467.274-.305.415-.847.415-1.626v-6.076h-7.94v6.076c0 1.362.658 2.059 1.979 2.1v.63h-6.364v-.63c.771-.007 1.294-.163 1.569-.461.275-.298.409-.846.409-1.632v-11.42c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.569-.461v-.63h6.364v.63c-.772.02-1.295.169-1.57.461-.275.291-.409.833-.409 1.619v4.524h7.94v-4.524c0-.793-.134-1.335-.409-1.619-.275-.292-.798-.441-1.569-.461v-.63H324v.63c-.771.02-1.294.169-1.569.461-.275.291-.409.833-.409 1.619v11.42c0 .799.134 1.348.409 1.639.275.291.798.447 1.569.454v.623zM62.84 255.708h-.989c0-1.471-.438-2.528-1.3-3.17-.75-.557-1.887-.835-3.414-.835h-1.039a28.199 28.199 0 00-.148 2.727c0 1.677.261 2.884.784 3.626.622.893 1.718 1.335 3.28 1.335h1.138v.957l-5.047.114v5.476c0 1.428.693 2.163 2.085 2.213v.664H51.47v-.664c.812-.007 1.364-.171 1.654-.485.29-.314.43-.893.43-1.721v-12.036c0-.836-.14-1.407-.43-1.707-.29-.307-.842-.464-1.654-.485v-.664h10.39c.212 0 .339-.207.381-.614h.6v5.269zM86.306 259.084c0 1.835-.509 3.584-1.533 5.247-1.025 1.664-2.347 2.913-3.965 3.749-1.47.756-3.012 1.142-4.623 1.142-2.34 0-4.311-.807-5.902-2.42-1.59-1.614-2.389-3.606-2.389-5.976 0-2.706.983-5.076 2.955-7.125 1.972-2.041 4.29-3.062 6.947-3.062 3.068 0 5.457 1.256 7.174 3.776.891 1.3 1.336 2.856 1.336 4.669zm-2.318 2.442c0-1.706-.346-3.306-1.046-4.805-.77-1.642-1.83-2.855-3.195-3.655-1.307-.757-2.53-1.142-3.66-1.142-1.803 0-3.245.642-4.334 1.934-1.024 1.221-1.533 2.763-1.533 4.627 0 2.47.707 4.633 2.113 6.475 1.534 2.013 3.492 3.012 5.874 3.012 1.71 0 3.116-.649 4.226-1.949 1.032-1.235 1.555-2.734 1.555-4.497zM107.425 268.808h-3.293c-.665 0-1.251-.4-1.753-1.192l-5.435-8.56v6.875c0 1.428.706 2.163 2.113 2.213v.664H92.32v-.664c.813-.007 1.364-.171 1.654-.485.29-.315.431-.893.431-1.721v-12.036c0-.836-.141-1.407-.43-1.707-.29-.307-.842-.464-1.655-.485v-.664h8.729c2.587 0 3.88 1.021 3.88 3.055 0 2.271-1.456 4.248-4.361 5.933l3.061 4.798c1.371 2.148 2.636 3.255 3.802 3.319v.657h-.007zm-5.315-12.736c0-1.1-.233-2.063-.7-2.892-.558-.992-1.342-1.484-2.36-1.484-.75 0-1.456.228-2.114.692v5.797c1.068.785 1.987 1.178 2.757 1.178 1.619 0 2.417-1.099 2.417-3.291zM136.609 255.694h-.976c-.028-2.656-1.42-3.991-4.17-3.991h-.742v14.228c0 1.435.693 2.171 2.085 2.213v.664h-6.707v-.664c.812-.007 1.364-.171 1.654-.485.289-.314.431-.892.431-1.721V251.71h-.947c-2.531 0-3.845 1.328-3.923 3.991h-.961v-5.283h.6c.121.414.297.628.538.628h11.987c.311 0 .466-.207.466-.628h.658v5.276h.007zM159.36 268.808h-6.707v-.664c.806-.007 1.35-.171 1.647-.492.289-.322.438-.893.438-1.714v-6.404h-8.369v6.404c0 1.435.693 2.171 2.086 2.213v.664h-6.708v-.664c.813-.007 1.364-.171 1.654-.485.29-.314.431-.892.431-1.721v-12.036c0-.835-.141-1.407-.431-1.706-.29-.307-.841-.464-1.654-.486v-.664h6.708v.664c-.813.022-1.365.179-1.654.486-.29.307-.432.878-.432 1.706v4.769h8.369v-4.769c0-.835-.141-1.407-.431-1.706-.29-.307-.841-.464-1.654-.486v-.664h6.707v.664c-.813.022-1.364.179-1.654.486-.289.307-.431.878-.431 1.706v12.036c0 .843.142 1.421.431 1.728.29.307.841.471 1.654.478v.657zM177.652 263.697l-1.124 5.725h-.685c-.007-.407-.184-.614-.523-.614h-10.002v-.664c.82-.007 1.379-.171 1.661-.478.283-.314.425-.885.425-1.728v-12.022c0-.835-.142-1.406-.432-1.713-.289-.307-.841-.472-1.654-.493v-.664h9.797c.593 0 .897-.228.918-.692h.644v5.311h-1.004c-.035-2.634-1.597-3.948-4.693-3.948h-1.039a24.463 24.463 0 00-.163 2.713c0 3.084 1.287 4.626 3.852 4.626h1.343v.971l-5.032.136v7.988h1.456c3.074 0 4.834-1.485 5.28-4.447h.975v-.007zM206.397 263.697l-1.124 5.725h-.685c-.007-.407-.184-.614-.523-.614h-10.001v-.664c.819-.007 1.378-.171 1.661-.478.282-.314.424-.885.424-1.728v-12.022c0-.835-.142-1.406-.432-1.713-.289-.307-.841-.472-1.653-.493v-.664h9.796c.593 0 .897-.228.919-.692h.643v5.311h-1.004c-.035-2.634-1.597-3.948-4.693-3.948h-1.039a24.463 24.463 0 00-.163 2.713c0 3.084 1.287 4.626 3.852 4.626h1.343v.971l-5.032.136v7.988h1.456c3.075 0 4.834-1.485 5.28-4.447h.975v-.007zM224.095 263.725l-1.18 5.74h-.643c-.007-.436-.205-.65-.587-.65h-9.93v-.664c.812-.007 1.364-.171 1.654-.485.289-.314.431-.892.431-1.721v-12.022c0-.835-.142-1.406-.431-1.713-.29-.307-.842-.471-1.654-.493v-.664h6.735v.664c-.834.022-1.392.179-1.675.478-.282.3-.424.879-.424 1.728v14.228h1.781c2.771 0 4.418-1.47 4.941-4.419h.982v-.007zM235.955 268.808h-6.707v-.664c.812-.007 1.364-.171 1.654-.485.289-.315.431-.893.431-1.721v-12.036c0-.836-.142-1.407-.431-1.707-.29-.307-.842-.464-1.654-.485v-.664h6.707v.664c-.827.021-1.378.178-1.661.485-.283.307-.424.878-.424 1.707v12.036c0 1.428.693 2.163 2.085 2.213v.657zM255.505 255.694h-.975c-.029-2.656-1.421-3.991-4.17-3.991h-.743v14.228c0 1.435.693 2.171 2.085 2.213v.664h-6.707v-.664c.813-.007 1.364-.171 1.654-.485.29-.314.431-.892.431-1.721V251.71h-.947c-2.53 0-3.838 1.328-3.923 3.991h-.961v-5.283h.601c.12.414.297.628.537.628h11.987c.311 0 .467-.207.467-.628h.657v5.276h.007zM273.019 263.697l-1.124 5.725h-.685c-.007-.407-.184-.614-.523-.614h-10.008v-.664c.82-.007 1.378-.171 1.661-.478.282-.314.424-.885.424-1.728v-12.022c0-.835-.142-1.406-.431-1.713-.29-.307-.841-.472-1.654-.493v-.664h9.796c.594 0 .897-.228.919-.692h.643v5.311h-1.004c-.035-2.634-1.597-3.948-4.693-3.948h-1.046a24.463 24.463 0 00-.163 2.713c0 3.084 1.287 4.626 3.853 4.626h1.342v.971l-5.032.136v7.988h1.456c3.075 0 4.834-1.485 5.28-4.447h.989v-.007zM44.945 262.511H3.958a.603.603 0 01-.6-.606v-.158c0-.335.268-.606.6-.606h40.987c.028 0 .05.021.05.05v1.278c0 .021-.022.042-.05.042zM319.95 262.512h-40.987a.049.049 0 01-.049-.05v-1.278c0-.029.021-.05.049-.05h40.987c.332 0 .601.271.601.607v.157a.606.606 0 01-.601.614z" />
      <defs>
        <filter
          id="filter0_d_166_6"
          x={93}
          y={0}
          width={139.364}
          height={141.393}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_166_6" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_166_6" result="shape" />
        </filter>
        <filter
          id="filter1_d_166_6"
          x={0.198}
          y={192.2}
          width={323}
          height={55}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_166_6" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_166_6" result="shape" />
        </filter>
      </defs>
    </svg>
  );
}

export function LogoSmall({ className }: { className?: string }) {
  return (
    <svg
      width={64}
      height={64}
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g filter="url(#filter0_d_58_545)">
        <path
          d="M42.673 36.453c-.508 1.703-2.31 2.607-3.963 3.22-7.03 2.605-14.554 3.421-21.996 3.641-1.54.045-3.08.066-4.62.071-.75.004-1.5.002-2.252-.001-.657-.004-1.658-.19-************ 2.24 3.221 4.232 5.422 4.3.942.029 1.867-.22 2.783-.442 5.472-1.327 11.15-1.764 16.516-3.468a62.76 62.76 0 01-19.123 4.717c.386 1.756 1.871 3.125 3.553 3.717 1.684.591 3.534.5 5.278.129 2.943-.624 5.677-2 8.275-3.53 1.732-1.021 3.42-2.118 5.105-3.213 1.978-1.285 3.983-2.591 5.562-4.353 1.19-1.32 2.15-3.102 1.715-4.831z"
          fill="url(#paint0_linear_58_545)"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M54.894 22.684a5.855 5.855 0 012.05 1.844c-2.67-.155-5.323 1.715-6.097 4.294-.309 1.028-.34 2.11-.372 3.189-.007.257-.015.514-.026.77-.094 2.172-.474 4.358-1.39 6.324-1.478 3.17-4.263 5.568-7.337 7.196-3.074 1.627-6.452 2.565-9.798 3.49 1.48-1.767 3.298-3.21 5.112-4.652 2.165-1.72 4.325-3.435 5.903-5.693.59-.845 1.093-1.765 1.383-2.758.309-1.06.369-2.182.273-3.282-.16-1.838-.75-3.623-1.596-5.258-.553-1.07-1.214-2.078-1.874-3.083l-.03-.045c-.94-1.431-1.887-2.873-3.082-4.096a34.098 34.098 0 00-.158-.16c-.15-.152-.3-.305-.443-.465a3.75 3.75 0 01-.542-.75c-.077-.15-.14-.447-.14-.447s.154.054.288.116c.151.07.298.143.442.216.398.199.785.392 1.238.505 1.175.293 2.447.218 3.566-.258.302-.128.591-.282.88-.436.212-.112.423-.224.64-.327 3.088-1.458 7.117-.493 9.226 2.213l.155.203c.191.254.384.51.627.713.242.203.525.345.808.487.**************.294.15zm-4.864-.289c0 .776-.623 1.405-1.392 1.405a1.398 1.398 0 01-1.391-1.405c0-.776.623-1.405 1.392-1.405.768 0 1.391.63 1.391 1.405z"
          fill="url(#paint1_linear_58_545)"
        />
        <path
          d="M41.236 40.522c-.226.286-.506.54-.793.805-1.965 1.809-4.46 3.093-6.741 4.444-2.479 1.467-4.99 2.877-7.56 4.171-2.03 1.022-4.053 2.252-6.26 2.843-.395.106-.879.1-.734.623.238.867 1.04 1.445 2.042 1.77 1.799.583 4.24.35 5.241-.5.708-.603 1.337-1.275 2.065-1.857 3.357-2.685 6.795-5.271 10.023-8.116 1.172-1.032 2.29-2.14 3.237-3.388 0 0 .863-1.372.863-2.86-.001-.002-.4.825-1.383 2.065z"
          fill="url(#paint2_linear_58_545)"
        />
        <path
          d="M13.009 0c3.165 5.869 7.816 10.775 12.67 15.317a171.534 171.534 0 009.308 8.102c1.907 1.542 3.867 3.064 5.38 5.001 1.513 1.937 2.556 4.378 2.307 6.832-1.136-2.695-3.139-4.939-5.39-6.787-2.25-1.848-4.761-3.341-7.21-4.91-4.295-2.752-8.443-5.768-12.134-9.3-1.893-1.812-3.692-3.799-4.794-6.184-1.172-2.538-1.474-5.552-.137-8.071z"
          fill="url(#paint3_linear_58_545)"
        />
        <path
          d="M8.833 5.043c1.445 2.132 2.543 4.36 4.275 6.33 1.718 1.954 3.652 3.707 5.666 5.345a76.576 76.576 0 008.926 6.264c4.725 2.838 9.882 5.265 13.449 9.488 1.002 1.187 1.903 2.725 1.496 4.23-3.345.706-6.688 1.41-10.033 2.117-3.17.669-6.417 1.341-9.627.9-3.21-.442-6.43-2.221-7.723-5.22 7.847 1.907 16.04 1.47 24.098 1.022-3.238-.613-6.565-.404-9.858-.497a53.754 53.754 0 01-10.958-1.443c-2.297-.547-4.609-1.272-6.508-2.684-1.9-1.413-3.342-3.63-3.311-6.012 7.265 4.65 15.999 6.219 24.498 7.55 2.924.46 5.938.933 8.461 2.492-1.325-1.923-3.836-2.514-6.115-2.954-7.18-1.387-14.463-2.771-21.059-5.959-2.335-1.128-4.622-2.526-6.204-4.592-1.582-2.067-2.348-4.925-1.409-7.36 3.596 4.767 8.753 8.119 14.149 10.613 5.398 2.495 11.106 4.222 16.653 6.36-2.338-1.9-5.252-2.887-8.042-4.002a76.744 76.744 0 01-12.718-6.524c-2.746-1.75-5.447-3.744-7.2-6.503-.85-1.337-1.433-2.867-1.64-4.445-.146-1.143-.289-3.773.734-4.516z"
          fill="url(#paint4_linear_58_545)"
        />
        <path
          d="M57.513 22.636c-.127-.33-.255-.657-.384-.97C53.035 11.65 45.242 5.188 35.746 3.936c-4.663-.615-9.59.126-13.873 2.087l-.42-.937c4.453-2.039 9.576-2.81 14.425-2.17 5.07.668 9.637 2.723 13.576 6.11 3.643 3.131 6.623 7.367 8.617 12.247.13.32.263.654.393.99l-.95.373z"
          fill="url(#paint5_linear_58_545)"
        />
        <path
          d="M7.144 41.894A31.606 31.606 0 014 29.39l1.018-.041a30.553 30.553 0 003.04 12.09l-.914.455z"
          fill="url(#paint6_linear_58_545)"
        />
        <path
          d="M31.434 56h-.175l.006-1.029h.17c7.117 0 14.008-2.572 18.927-7.075 5.23-4.784 8.205-11.932 7.764-18.65l1.016-.067c.232 3.527-.414 7.206-1.867 10.641a25.552 25.552 0 01-6.23 8.84C45.942 53.33 38.802 56 31.435 56z"
          fill="url(#paint7_linear_58_545)"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_58_545"
          x={0}
          y={0}
          width={63.188}
          height={64}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={4} />
          <feGaussianBlur stdDeviation={2} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_58_545" />
          <feBlend in="SourceGraphic" in2="effect1_dropShadow_58_545" result="shape" />
        </filter>
        <linearGradient
          id="paint0_linear_58_545"
          x1={29.207}
          y1={49.708}
          x2={23.581}
          y2={36.577}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AA2556" />
          <stop offset={0.114} stopColor="#9A205B" />
          <stop offset={0.54} stopColor="#63106B" />
          <stop offset={0.747} stopColor="#4E0971" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_58_545"
          x1={48.011}
          y1={41.776}
          x2={32.662}
          y2={22.311}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.085} stopColor="#67106A" />
          <stop offset={0.197} stopColor="#7F1863" />
          <stop offset={0.322} stopColor="#921E5D" />
          <stop offset={0.468} stopColor="#A02259" />
          <stop offset={0.652} stopColor="#A82457" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_58_545"
          x1={19.12}
          y1={46.977}
          x2={42.617}
          y2={46.977}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_58_545"
          x1={31.87}
          y1={9.286}
          x2={23.688}
          y2={25.906}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_58_545"
          x1={2.912}
          y1={15.572}
          x2={42.354}
          y2={37.233}
          gradientUnits="userSpaceOnUse"
        >
          <stop offset={0.253} stopColor="#4E0971" />
          <stop offset={0.46} stopColor="#63106B" />
          <stop offset={0.886} stopColor="#9A205B" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_58_545"
          x1={21.453}
          y1={12.666}
          x2={58.464}
          y2={12.666}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_58_545"
          x1={4.001}
          y1={35.621}
          x2={8.059}
          y2={35.621}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_58_545"
          x1={31.26}
          y1={42.589}
          x2={59.191}
          y2={42.589}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#4E0971" />
          <stop offset={0.479} stopColor="#781665" />
          <stop offset={1} stopColor="#AA2556" />
        </linearGradient>
      </defs>
    </svg>
  );
}
