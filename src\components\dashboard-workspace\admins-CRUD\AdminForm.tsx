import {
  DataFormFiledsDef,
  DataFormOnActionDef,
} from "@/components/dashboard/components/form/types";
import { AdminType } from "@/components/dashboard/components/auth/types";
import DataForm from "@/components/dashboard/components/form/DataForm";

export default function AdminForm({
  mode,
  onAction,
  defaultValues,
}: {
  onAction: DataFormOnActionDef<AdminType>;
  defaultValues?: Partial<AdminType>;
  mode: "create" | "update";
}) {
  const updateFields: DataFormFiledsDef<AdminType>[] | [] =
    mode === "update"
      ? [{ accessorKey: "id", label: "", fieldType: "hidden" }]
      : [
          {
            label: "كلمة المرور",
            accessorKey: "password",
            placeholder: "abc123!@#",
            description:
              "تجنّب استخدام كلمات شائعة أو معلومات شخصية، لأن ذلك يجعل من السهل على الآخرين تخمينها.",
          },
        ];

  return (
    <DataForm<AdminType>
      mode={mode}
      defaultValues={defaultValues}
      fields={[
        {
          label: "الاسم",
          accessorKey: "name",
          placeholder: "مثال : خالد علي جابر",
        },
        {
          label: "البريد الإلكتروني",
          accessorKey: "email",
          placeholder: "<EMAIL>",
          inputConfig: { type: "email" },
          description: "يُستخدم كمعرّف دخول، فاختر شيئًا يسهل تذكره.",
        },
        {
          label: "الصورة الرمزية",
          accessorKey: "avatarUrl",
          required: false,
          fieldType: "fileUploader",
        },
        // {
        //   label: "حالة الحساب",
        //   placeholder: "لم يتم تحديد قيمة",
        //   accessorKey: "status",
        //   description: `اختر حالة الحساب. عند التعطيل، يُمنع المشرف من تسجيل الدخول. ويمكن تعديلها لاحقًا من إدارة المشرفين..`,
        //   fieldType: "select",
        //   selectConfig: {
        //     options: [
        //       { label: "مفعل", value: "activated" },
        //       { label: "معطل", value: "disabled" },
        //     ],
        //   },
        // },
        ...updateFields,
      ]}
      callbackUrl="/admin/admins-management"
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      cancelButtonLabel="إلغاء"
      onAction={onAction}
    />
  );
}
