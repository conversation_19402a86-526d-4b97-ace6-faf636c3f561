import { Button } from "../dashboard/components/ui/Button";
import { AudioLinesIcon, FileArchiveIcon, Minus, Video } from "lucide-react";

export default function TableFile({
  href,
  fileType,
}: {
  href?: string | null;
  fileType: "audio" | "video" | "pdf";
}) {
  if (!href)
    return (
      <div className="flex size-8 items-center justify-center">
        <Minus className="size-5 text-gray-500 opacity-90 hover:opacity-100 dark:text-gray-400" />
      </div>
    );

  return (
    <a href={href} target="_blank" className="max-w-fit" rel="noopener noreferrer">
      <Button variant="ghost" className="size-8 px-0 py-0 [&_svg]:size-5">
        {fileType === "video" && (
          <Video className="w-auto text-blue-500 opacity-90 hover:opacity-100 dark:text-blue-400" />
        )}
        {fileType === "audio" && (
          <AudioLinesIcon className="w-auto text-green-500 opacity-90 hover:opacity-100 dark:text-green-400" />
        )}
        {fileType === "pdf" && (
          <FileArchiveIcon className="w-auto text-gray-500 opacity-90 hover:opacity-100 dark:text-gray-400" />
        )}
      </Button>
    </a>
  );
}
