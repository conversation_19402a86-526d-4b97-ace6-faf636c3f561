"use server";

import prisma from "@/lib/prisma";
import { OnAddCommentActionResult } from "./add-comment";
import { revalidatePath } from "next/cache";
import { verifySession } from "@/auth/dal";
import { EntityType } from "@prisma/client";
import { z } from "zod";

export async function addCommentAction({
  entity,
  comment,
  entityId,
  pathRevalidate,
}: {
  comment: string;
  entityId: string;
  entity: EntityType;
  pathRevalidate: string;
}): OnAddCommentActionResult {
  // بيانات الجلسة
  const session = await verifySession();
  if (!session) return { sucess: false, message: "يجب عليك تسجيل الدخول أولاً" };

  const schema = z.object({
    comment: z
      .string()
      .trim()
      .min(5, { message: "التعليق لا يمكن أن يكون فارغًا" })
      .max(500, { message: "التعليق طويل جدًا، الحد الأقصى 500 حرف" }),
  });

  const { success, data, error } = schema.safeParse({ comment });
  if (!success) {
    return {
      sucess: false,
      errors: error?.flatten().fieldErrors.comment?.[0],
    };
  }

  // إضافة التعليق الى قاعدة البيانات
  await prisma.comment.create({
    data: {
      entity: entity,
      entityId: entityId,
      userId: session.id,
      comment: data.comment,
      userName: session.name,
    },
  });

  revalidatePath(pathRevalidate);
  return { sucess: true, message: "تم اضافة التعليق بنجاح" };
}

export async function deleteCommentAction({
  entity,
  entityId,
  commentId,
  pathRevalidate,
}: {
  entityId: string;
  commentId: string;
  entity: EntityType;
  pathRevalidate: string;
}): Promise<{ sucess: boolean; message?: string }> {
  const session = await verifySession();
  if (!session) {
    return { sucess: false, message: "يجب عليك تسجيل الدخول أولاً" };
  }

  // const comment = await prisma.comment.findUnique({
  //   where: { entity, entityId, id: commentId },
  // });
  // if (!comment) {
  //   return { sucess: false, message: "التعليق غير موجود" };
  // }

  // if (comment.userId !== session.id) {
  //   return { sucess: false, message: "لا يمكنك حذف التعليق" };
  // }

  try {
    await prisma.comment.delete({
      where: { id: commentId, entityId, entity, userId: session.id },
    });

    revalidatePath(pathRevalidate);
    return { sucess: true, message: "تم حذف التعليق بنجاح" };

    // eslint-disable-next-line
  } catch (error) {
    return {
      sucess: false,
      message:
        "حدث خطأ. لم يتم العثور على هذا التعليق ربما قد تم حذفة من قبل تأكد من إعادة تحميل هذه الصفحة",
    };
  }
}
