"use client";

import { Card, CardContent } from "../../../../components/ui/card";
import { useRouter } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { Button } from "../../../../components/ui/button";
import { cn } from "@/lib/utils";
import authImage from "@/../public/auth.jpg";
import Image from "next/image";
import { verifyEmailAction } from "@/app/(website)/auth/verify-email/verify-email-action";
import { CheckCircle, XCircle } from "lucide-react";
import Link from "next/link";

export function EmailVerificationForm({ className, token }: { className?: string; token?: string }) {
  const router = useRouter();
  const [, startTransition] = useTransition();
  const [verificationState, setVerificationState] = useState<{
    status: "loading" | "success" | "error";
    message: string;
  }>({
    status: "loading",
    message: "جاري التحقق من البريد الإلكتروني...",
  });

  useEffect(() => {
    if (!token) {
      setVerificationState({
        status: "error",
        message: "رمز التحقق غير صالح أو مفقود. يرجى التحقق من الرابط المرسل إلى بريدك الإلكتروني.",
      });
      return;
    }

    startTransition(async () => {
      try {
        const response = await verifyEmailAction(token);

        if (response.success) {
          setVerificationState({
            status: "success",
            message: response.message,
          });

          window.location.reload();
        } else {
          setVerificationState({
            status: "error",
            message: response.message,
          });
        }

        // eslint-disable-next-line
      } catch (error) {
        setVerificationState({
          status: "error",
          message: "حدث خطأ أثناء التحقق من البريد الإلكتروني. يرجى المحاولة مرة أخرى.",
        });
      }
    });
  }, [token, router]);

  return (
    <div dir="rtl" className={cn("flex w-full max-w-sm flex-col gap-6 md:max-w-3xl", className)}>
      <Card className="overflow-hidden">
        <CardContent className="grid p-0 md:grid-cols-2">
          <div className="p-6 md:p-8">
            <div className="flex flex-col gap-6">
              <div className="flex flex-col items-center text-center">
                <h1 className="text-2xl font-bold">تأكيد البريد الإلكتروني</h1>
              </div>

              <div className="flex flex-col items-center justify-center py-8">
                {verificationState.status === "loading" && (
                  <div className="flex flex-col items-center gap-4">
                    <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
                    <p className="text-center text-secondary">{verificationState.message}</p>
                  </div>
                )}

                {verificationState.status === "success" && (
                  <div className="flex flex-col items-center gap-4">
                    <CheckCircle className="h-16 w-16 text-green-500" />
                    <p className="text-center text-secondary">{verificationState.message}</p>
                  </div>
                )}

                {verificationState.status === "error" && (
                  <div className="flex flex-col items-center gap-4">
                    <XCircle className="h-16 w-16 text-red-500" />
                    <p className="text-center text-secondary">{verificationState.message}</p>
                    <Button className="mt-4 w-full" asChild>
                      <Link href="/auth/login">العودة إلى تسجيل الدخول</Link>
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="relative hidden bg-muted md:block">
            <Image
              src={authImage}
              placeholder="blur"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
