"use client";

import { Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow } from "../ui/Table";
import { cx } from "../../lib/utils-tremor";
import * as React from "react";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  getFacetedUniqueValues,
} from "@tanstack/react-table";
import { ColumnSearchDef, DataTableFiltersDef, DataTableOnDeleteFnDef } from "./types";
import DataTableFilterbar from "./DataTableFilterbar";
import { fuzzySearch } from "./DataTableFilterFns";
import { DataTablePagination } from "./DataTablePagination";
import DataTableBulkDeleteMany from "./DataTableBulkDeleteMany";

interface DataTableTremorProps<TData extends { [K: string]: any }> {
  data: TData[];
  primaryKey?: keyof TData & string;
  columns: ColumnDef<TData>[];
  filters: DataTableFiltersDef<TData>;
  columnSearch?: ColumnSearchDef<TData>;
  enableRowSelection?: boolean;
  defaultPageSize?: number;
  onDelete?: DataTableOnDeleteFnDef<TData>;
  createDataButton?: { label?: string; href: string };
}

export function DataTableTremor<TData extends { [K: string]: any }>({
  createDataButton,
  onDelete,
  columns,
  data,
  filters,
  columnSearch,
  primaryKey = "id",
  defaultPageSize = 10,
  enableRowSelection = true,
}: DataTableTremorProps<TData>) {
  const [rowSelection, setRowSelection] = React.useState({});

  const table = useReactTable({
    data,
    columns,

    state: {
      rowSelection,
    },

    initialState: {
      pagination: {
        pageIndex: 0,
        pageSize: defaultPageSize,
      },
    },

    filterFns: {
      fuzzySearch: fuzzySearch,
    },

    getRowId: (row) => row[primaryKey],
    enableRowSelection,
    enableGlobalFilter: true,
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return (
    <>
      <div className="w-full space-y-3">
        <DataTableFilterbar
          filters={filters}
          createDataButton={createDataButton}
          columnSearch={columnSearch}
          table={table}
        />
        <div className="relative overflow-hidden rounded-md border-x border-b border-gray-200 inset-shadow-sm dark:border-gray-800">
          <div className={cx("relative overflow-hidden overflow-x-auto")}>
            <Table>
              <TableHead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow
                    key={headerGroup.id}
                    className="border-y border-gray-200 dark:border-gray-800"
                  >
                    {headerGroup.headers.map((header) => (
                      <TableHeaderCell
                        key={header.id}
                        className={cx(
                          "py-1 text-right text-sm whitespace-nowrap sm:text-xs",
                          header.column.columnDef.meta?.className,
                        )}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHeaderCell>
                    ))}
                  </TableRow>
                ))}
              </TableHead>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      // onClick={
                      //   enableRowSelection
                      //     ? () => row.toggleSelected(!row.getIsSelected())
                      //     : undefined
                      // }
                      className="group select-none hover:bg-gray-50 hover:dark:bg-gray-900/80"
                    >
                      {row.getVisibleCells().map((cell, index) => (
                        <TableCell
                          key={cell.id}
                          className={cx(
                            row.getIsSelected() ? "bg-gray-50 dark:bg-gray-900" : "",
                            "relative py-1 text-right whitespace-nowrap text-gray-600 first:w-10 dark:text-gray-400",
                            cell.column.columnDef.meta?.className,
                          )}
                        >
                          {index === 0 && row.getIsSelected() && (
                            <div className="absolute inset-y-0 right-0 w-0.5 bg-blue-600 dark:bg-blue-500" />
                          )}
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      لا توجد نتائج.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <DataTableBulkDeleteMany onDelete={onDelete} table={table} />
          </div>
          <div className="pointer-events-none absolute inset-y-0 left-0 z-10 w-3 rounded-r-full bg-gradient-to-r from-gray-950/5 mix-blend-multiply blur-xs dark:from-gray-950/50" />
          <div className="pointer-events-none absolute inset-y-0 right-0 z-10 w-3 rounded-r-full bg-gradient-to-l from-gray-950/5 mix-blend-multiply blur-xs dark:from-gray-950/50" />
        </div>
        <DataTablePagination table={table} />
      </div>
    </>
  );
}
