"use client";
import React from "react";
import ConsultationsList from "./ConsultationsList";
import { Consultation } from "@prisma/client";
import ConsultationDetails from "./ConsultationDetails";
import { useRouter } from "next/navigation";
import { markConsultationAsRead } from "./db-queries";

export default function Consultations({
  nextCursor,
  serverData,
  mode = "all",
}: {
  mode?: "unRead" | "all";
  serverData: Consultation[];
  nextCursor: string | null;
}) {
  const [activatedConsultation, setActivatedConsultation] = React.useState<ConsultationStateType>(undefined);
  const [currentCursor, setCurrentCursor] = React.useState(nextCursor);
  const [data, setData] = React.useState(serverData);
  const [, startTransition] = React.useTransition();
  const router = useRouter();

  // This effect is used to set the activated consultation in the DOM
  React.useEffect(() => {
    const previousElement = document.querySelector("[data-activated='true']");
    if (previousElement) previousElement.removeAttribute("data-activated");

    if (activatedConsultation?.id) {
      document.getElementById(activatedConsultation.id)?.setAttribute("data-activated", "true");
      if (activatedConsultation.read === false) {
        startTransition(async () => {
          await markConsultationAsRead(activatedConsultation.id);

          startTransition(() => {
            setData((prev) =>
              prev.map((c) => {
                if (c.id === activatedConsultation.id) {
                  return { ...c, read: true };
                }
                return c;
              }),
            );
            router.refresh();
          });
        });
      }
    }
  }, [activatedConsultation?.id, activatedConsultation?.read, router]);

  return (
    <div className="mt-4 mb-5 flex flex-col gap-y-8 px-3 sm:px-4">
      <h1 className="text-3xl font-medium">حجوزات الاستشارة</h1>
      <div className="grid grid-cols-1 gap-3 @min-3xl:grid-cols-5">
        <ConsultationsList
          mode={mode}
          data={data}
          setData={setData}
          currentCursor={currentCursor}
          setCurrentCursor={setCurrentCursor}
          setActivatedConsultation={setActivatedConsultation}
        />
        <ConsultationDetails
          setData={setData}
          activatedConsultation={activatedConsultation}
          setActivatedConsultation={setActivatedConsultation}
        />
      </div>
    </div>
  );
}

export type ConsultationStateType = Consultation | undefined;
export type SetConsultationStateType = React.Dispatch<React.SetStateAction<ConsultationStateType>>;
