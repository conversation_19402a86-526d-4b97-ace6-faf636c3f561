import AdminForm from "@/components/dashboard-workspace/admins-CRUD/AdminForm";
import { adminSchema } from "@/components/dashboard-workspace/admins-CRUD/AdminSchema";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import { hashPassword } from "@/components/dashboard/components/auth/admin-session";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

export default function CreatePage() {
  return (
    <PageLayout title="إضافة مشرف جديد" description="إملأ الحقول بالقيم المناسبة لإنشاء مشرف جديد">
      <AdminForm
        mode="create"
        defaultValues={{ status: "activated" }}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = adminSchema.omit({ id: true }).safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          // لتأكد من عدم وجود مشرف بنفس عنوان البريد
          const isNotUnique = await prisma.admin.findUnique({
            where: { email: data.email },
          });

          if (isNotUnique) {
            return {
              success: false,
              errors: { email: ["البريد الإلكتروني مستخدم من قبل"] },
            };
          }

          // انشاء المشرف
          const password = await hashPassword(data.password);
          const avatarUrl = await handleFileUpdate(null, data.avatarUrl, "admins/");

          await prisma.admin.create({ data: { ...data, password, avatarUrl } });

          revalidatePath("/admin/admins-management");
          return { success: true, message: "تم إنشاء المشرف بنجاح" };
        }}
      />
    </PageLayout>
  );
}
