import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Lecture } from "@prisma/client";

export default function LecturesForm({
  mode,
  courseId,
  defaultValues,
  onAction,
}: {
  courseId: string;
  mode: "create" | "update";
  defaultValues?: Partial<Lecture>;
  onAction?: DataFormOnActionDef<Lecture>;
}) {
  return (
    <DataForm<Lecture>
      mode={mode}
      defaultValues={defaultValues}
      fields={[
        { accessorKey: "title", label: "اسم المحور", placeholder: "ادخل اسم المحور" },
        {
          accessorKey: "posterUrl",
          label: "صورة المحور",
          fieldType: "fileUploader",
          description: "احرص على ان تكون الوان الصورة مناسبة مع ثيم الموقع",
        },
        {
          accessorKey: "video",
          label: "فيديو",
          required: false,
          fieldType: "fileUploader",
          fileUploaderConfig: { acceptedFileTypes: ["video/*"] },
        },
        {
          accessorKey: "audio",
          label: "صوت",
          required: false,
          fieldType: "fileUploader",
          fileUploaderConfig: { acceptedFileTypes: ["audio/*"] },
        },
        {
          accessorKey: "pdf",
          label: "ملف PDF",
          required: false,
          fieldType: "fileUploader",
          fileUploaderConfig: { acceptedFileTypes: ["application/pdf"] },
        },

        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف المحور لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description:
            "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },
      ]}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl={`/admin/courses/lectures/${courseId}`}
      cancelButtonLabel="إلغاء"
      onAction={onAction}
    />
  );
}
