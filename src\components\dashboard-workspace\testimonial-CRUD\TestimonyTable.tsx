"use client";
import PageLayout from "../PageLayout";
import DataTable from "@/components/dashboard/components/table/DataTable";
import { Testimony } from "@prisma/client";

import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types";

export default function TestimonysTable({
  data,
  onDelete,
}: {
  data: Testimony[];
  onDelete?: DataTableOnDeleteFnDef<Testimony>;
}) {
  return (
    <PageLayout
      title="شهادات العملاء"
      description="إدارة شهادات العملاء, الحد الأقصى لإجمالي الشهادات هو ثلاث شهادات فقط."
    >
      <DataTable<Testimony>
        data={data}
        defaultPageSize={5}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/testimonys/update", label: "تعديل" }],
          },
        }}
        columnSearch={{ columnKey: "testimony" }}
        createDataButton={{ href: "/admin/testimonys/create", label: "إضافة شهادة" }}
        createColumns={[
          {
            accessorKey: "testimony",
            columnLabel: "شهادة العميل",
          },
        ]}
      />
    </PageLayout>
  );
}
