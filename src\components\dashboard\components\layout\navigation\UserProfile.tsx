"use client";

import { <PERSON><PERSON> } from "@/components/dashboard/components/ui/Button";
import { cx, focusRing } from "@/components/dashboard/lib/utils-tremor";
import { ChevronsUpDown } from "lucide-react";
import defualtAvatar from "@/../public/dashboard-files/defualt-avatar.jpg";

import { DropdownUserProfile } from "./DropdownUserProfile";
import Image from "next/image";
import { useAdminSession } from "../../auth/useAdminSession";
import { Skeleton } from "../../ui/Skeleton";

export function UserProfile() {
  const adminSession = useAdminSession();

  return (
    <DropdownUserProfile>
      <Button
        aria-label="User settings"
        variant="ghost"
        className={cx(
          "group flex w-full items-center justify-between rounded-md px-1 py-2 text-sm font-medium text-gray-900 hover:bg-gray-200/50 data-[state=open]:bg-gray-200/50 dark:hover:bg-gray-800/50 dark:data-[state=open]:bg-gray-900",
          focusRing,
        )}
      >
        <span className="flex items-center gap-3">
          <span
            className="relative flex size-8 shrink-0 items-center justify-center overflow-clip rounded-full border border-gray-300 bg-white text-xs text-gray-700 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300"
            aria-hidden="true"
          >
            {!adminSession ? (
              <Skeleton className="size-8" />
            ) : (
              <Image
                className="h-auto min-h-full w-auto min-w-full object-cover"
                src={adminSession?.avatarUrl ?? defualtAvatar}
                sizes="32px"
                alt=""
                fill
              />
            )}
          </span>
          {!adminSession ? (
            <Skeleton className="h-3.5 w-28" />
          ) : (
            <span className="line-clamp-1 text-start text-wrap">
              {adminSession?.name}
            </span>
          )}
        </span>
        <ChevronsUpDown
          className="size-4 shrink-0 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-400"
          aria-hidden="true"
        />
      </Button>
    </DropdownUserProfile>
  );
}
