"use client";

import { useEffect, useMemo, useState, useTransition } from "react";
import { CommentCard } from "./comment-card";
import { useOnView } from "@/hooks/useOnScreen";
import { Skeleton } from "../skeleton";
import { PaginationProps } from "../pagination";
import { Comment, EntityType } from "@prisma/client";

type CommentProps = {
  entity: EntityType;
  entityId: string;
  pathRevalidate: string;
};

// ==================================================================================
// مكون يعرض تعليقات المستخدمين على صفحة معينة ومنشور معين
// ==================================================================================
export default function ClientSideComments({ entity, entityId, pathRevalidate }: CommentProps) {
  const [isPending, startTransition] = useTransition();
  const [data, setData] = useState<Comment[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(2);
  const { ref, isOnView } = useOnView();

  useEffect(() => {
    const timeLimit = setTimeout(() => {
      if (isOnView && !isPending && hasMore) {
        startTransition(async () => {
          const { data, pagination } = await getComments({
            entityId,
            entity,
            page,
          });

          startTransition(() => {
            setHasMore(pagination.totalPages > pagination.currentPage);
            setData((prev) => [...prev, ...data]);
            setPage((prev) => prev + 1);
          });
        });
      }
    }, 200);

    return () => clearTimeout(timeLimit);
  }, [entity, entityId, isOnView, isPending, hasMore, page]);

  return (
    <>
      {useMemo(
        () => data.map((comment) => <CommentCard key={comment.id} comment={comment} pathRevalidate={pathRevalidate} />),
        [data, pathRevalidate],
      )}

      <SkeletonComments clientLimit={5} isPending={isPending} />
      <div className="absolute right-1/2 bottom-0 size-px" ref={ref}></div>
    </>
  );
}

// ==================================================================================
// هيكل التحميل يتم عرضه عند التمرير الى الأسفل اثناء انتضار التحميل
// ==================================================================================
function SkeletonComments({ isPending, clientLimit }: { isPending: boolean; clientLimit: number }) {
  if (!isPending) return;

  return (
    <div className="duration-300 animate-in fade-in slide-in-from-top-2">
      {[...Array(clientLimit)].map((_, indx) => (
        <div key={indx} className="my-4 flex flex-nowrap items-start gap-5 py-4">
          <Skeleton className="size-12 shrink-0 rounded-full" />
          <div className="w-full space-y-2">
            <div className="flex flex-nowrap items-center gap-2">
              <Skeleton className="h-4 w-32 sm:w-56" />
            </div>
            <Skeleton className="h-4 w-full" />
          </div>
        </div>
      ))}
    </div>
  );
}

type GetCommentParams = {
  page: number;
  entityId: string;
  entity: EntityType;
};

type ResponseComments = { data: Comment[]; pagination: PaginationProps };
async function getComments({ page, entity, entityId }: GetCommentParams) {
  const url = `/api/comments?entity=${entity}&entity-id=${entityId}&page=${page}`;
  const response = await fetch(url);

  return (await response.json()) as ResponseComments;
}
