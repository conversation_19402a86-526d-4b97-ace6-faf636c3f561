"use client";

import { useContext, createContext } from "react";
import { useLayoutEffect, useState } from "react";
import { getAdminSession } from "./server-actions";
import { AdminSession } from "./types";

export const CreateAdminContext = createContext<AdminSession | undefined>(undefined!);

export function AdminSessionProvider({ children }: { children: React.ReactNode }) {
  const [adminSession, setAdminSession] = useState<AdminSession | undefined>(undefined);

  useLayoutEffect(() => {
    let condition = true;
    if (condition) {
      getAdminSession().then((session) => setAdminSession(session));
    }

    return () => {
      condition = false;
    };
  }, []);

  return <CreateAdminContext.Provider value={adminSession}>{children}</CreateAdminContext.Provider>;
}

export function useAdminSession() {
  const adminSession = useContext(CreateAdminContext);
  return adminSession;
}
