import "server-only";

import { SignJWT, jwtVerify } from "jose";
import { compare, hash } from "bcryptjs";
import { AdminSession } from "./types";
import { cookies } from "next/headers";
import { cache } from "react";

const secretKey = process.env.DASHBOARD_SESSION_SECRET;
const encodedKey = new TextEncoder().encode(secretKey);

// ================================================================================== //
// إنشاء توقيع جلسة جديد
// ================================================================================== //
async function encrypt(payload: AdminSession) {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("365d")
    .sign(encodedKey);
}

// ================================================================================== //
// التحقق من توقيع الجلسة
// ================================================================================== //
async function decrypt(adminSession: string | undefined = "") {
  try {
    const { payload } = await jwtVerify(adminSession, encodedKey, {
      algorithms: ["HS256"],
    });
    return payload;

    // eslint-disable-next-line
  } catch (error) {
    return;
  }
}

// ================================================================================== //
// إنشاء جلسة جديدة او تحديث جلسة سابقة
// ================================================================================== //
export async function createAdminSession(admin: AdminSession) {
  const expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);
  const adminSession = await encrypt(admin);
  const cookieStore = await cookies();

  cookieStore.set("adminSession", adminSession, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: expiresAt,
    sameSite: "lax",
    path: "/",
  });
}

// ================================================================================== //
// حذف الجلسة
// ================================================================================== //
export async function deleteAdminSession() {
  const cookieStore = await cookies();
  cookieStore.delete("adminSession");
}

// ================================================================================== //
// التحقق من صلاحية جلسة المشرف
// ================================================================================== //
export const verifyAdminSession = cache(async () => {
  const cookie = (await cookies()).get("adminSession")?.value;
  const session = await decrypt(cookie);

  return session as AdminSession | undefined;
});

// ================================================================================== //
// تشفير كلمة المرور
// ================================================================================== //
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  const hashedPassword = await hash(password, saltRounds);
  return hashedPassword;
}

// ================================================================================== //
// فك تشفير كلمة المرور
// ================================================================================== //
export async function comparePasswords(password: string, hashedPassword: string): Promise<boolean> {
  const isMatch = await compare(password, hashedPassword);
  return isMatch;
}
