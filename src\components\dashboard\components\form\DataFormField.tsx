import React from "react"

import DataFormField<PERSON>heckbox from "./DataFormFieldCheckbox"
import DataFormFieldCheckboxArray from "./DataFormFieldCheckboxArray"
import DataFormFieldFileUploader from "./DataFormFieldFileUploader"
import DataFormFieldInput from "./DataFormFieldInput"
import DataFormFieldInputArray from "./DataFormFieldInputArray"
import DataFormFieldSelect from "./DataFormFieldSelect"
import DataFormFieldTextarea from "./DataFormFieldTextarea"
import TextEditor from "./DataFormFieldTextEditor"
import { DataFormFiledsDef } from "./types"

export type DataFormFieldProps<TData extends { [K: string]: any }> =
  DataFormFiledsDef<TData> & {
    defaultValue: any
    errorMessage?: string[]
    isPending: boolean
  }

export default function DataFormField<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>
) {
  switch (props.fieldType) {
    case undefined:
      return <DataFormFieldInput {...props} />

    case "input":
      return <DataFormFieldInput {...props} />

    case "textarea":
      return <DataFormFieldTextarea {...props} />

    case "select":
      return <DataFormFieldSelect {...props} />

    case "inputArray":
      return <DataFormFieldInputArray {...props} />

    case "checkboxArray":
      return <DataFormFieldCheckboxArray {...props} />

    case "fileUploader":
      return <DataFormFieldFileUploader {...props} />

    case "checkbox":
      return <DataFormFieldCheckbox {...props} />
    case "textEditor":
      return <TextEditor {...props} />

    case "hidden":
      return (
        <input
          defaultValue={props.defaultValue}
          type="hidden"
          name={props.accessorKey}
        />
      )
  }
}
