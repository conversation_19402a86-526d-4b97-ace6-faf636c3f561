"use client";

import { But<PERSON> } from "../ui/Button";
import { Row } from "@tanstack/react-table";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuIconWrapper,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuSubMenu,
  DropdownMenuSubMenuContent,
  DropdownMenuSubMenuTrigger,
  DropdownMenuTrigger,
} from "../ui/DropdownMenu";
import {
  DataTableRowActionLinksDef,
  DataTableOptionsDef,
  DataTableRowActionsDef,
  DataTableOnOptionChangeFnDef,
} from "./types";
import Link from "next/link";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/Dialog";
import { toast } from "sonner";
import { MoreH<PERSON>zon<PERSON>, Trash } from "lucide-react";

export function DataTableRowActions<TData extends { [K: string]: any }>({
  row,
  actions,
}: {
  row: Row<TData>;
  actions: DataTableRowActionsDef<TData>;
}) {
  const dataRow = row.original;
  return (
    <DropdownMenu dir="rtl">
      <DropdownMenuTrigger asChild>
        <Button
          onClick={(e) => e.stopPropagation()}
          variant="ghost"
          className="group aspect-square p-1.5 hover:border hover:border-gray-300 data-[state=open]:border-gray-300 data-[state=open]:bg-gray-50 hover:dark:border-gray-700 data-[state=open]:dark:border-gray-700 data-[state=open]:dark:bg-gray-900"
        >
          <MoreHorizontal
            className="size-4 shrink-0 text-gray-500 group-hover:text-gray-700 group-data-[state=open]:text-gray-700 group-hover:dark:text-gray-300 group-data-[state=open]:dark:text-gray-300"
            aria-hidden="true"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent sticky="always" align="end" className="min-w-40">
        <LinkActions dataRow={dataRow} id={row.id} links={actions.links} />
        <OptionActions dataRow={dataRow} onOptionChange={actions.onOptionChange} options={actions.options} />
        <DeleteAction dataRow={dataRow} onDelete={actions.onDelete} />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// ==========================================================================
// الروابط
// ==========================================================================
function LinkActions<TData extends { [K: string]: any }>(props: {
  dataRow: TData;
  id: string;
  links?: DataTableRowActionLinksDef<TData>;
}) {
  if (!props.links) return null;
  const { items } = props.links;
  // const dynamicSegment = props.dataRow?.[primaryKey ?? "id"];

  return items.map((item) => (
    <DropdownMenuGroup key={item.basePath}>
      <LinkActionItem
        basePath={item.basePath}
        dynamicSegment={encodeURIComponent(props.id)}
        label={item.label}
        icon={item.icon}
      />
    </DropdownMenuGroup>
  ));
}
function LinkActionItem(props: {
  dynamicSegment: string;
  basePath?: string;
  label: string;
  icon?: React.ComponentType<{ className?: string; [K: string]: any }>;
}) {
  if (!props.basePath) return null;

  return (
    <Link href={`${props.basePath}/${props.dynamicSegment}`} prefetch={true}>
      <DropdownMenuItem className="cursor-default">
        {props.label}
        {props.icon && (
          <DropdownMenuIconWrapper>
            <props.icon />
          </DropdownMenuIconWrapper>
        )}
      </DropdownMenuItem>
    </Link>
  );
}

// ==========================================================================
// الخيارات
// ==========================================================================
function OptionActions<TData extends { [K: string]: any }>(props: {
  onOptionChange?: DataTableOnOptionChangeFnDef<TData>;
  options?: DataTableOptionsDef<TData>[];
  dataRow: TData;
}) {
  if (!props.options) return null;
  const { dataRow, options } = props;

  return (
    <>
      <DropdownMenuSeparator className="first:hidden" />
      {options.map((item) => {
        const defaultValue = String(dataRow?.[item.columnKey]);
        if (!defaultValue) return null;

        return (
          <DropdownMenuGroup key={item.columnKey}>
            <DropdownMenuSubMenu>
              <DropdownMenuSubMenuTrigger>{item.label}</DropdownMenuSubMenuTrigger>
              <DropdownMenuSubMenuContent>
                <DropdownMenuRadioGroup
                  defaultValue={defaultValue}
                  value={defaultValue}
                  onValueChange={async (value) => {
                    if (props.onOptionChange) {
                      const { success, message } = await props.onOptionChange({
                        where: props.dataRow,
                        data: { ...props.dataRow, [item.columnKey]: value },
                      });
                      if (success && message) toast.success(message);
                      if (!success && message) toast.error(message);
                    }
                  }}
                >
                  <RadioOptions options={item.options} />
                </DropdownMenuRadioGroup>
              </DropdownMenuSubMenuContent>
            </DropdownMenuSubMenu>
          </DropdownMenuGroup>
        );
      })}
      <DropdownMenuSeparator className="last:hidden" />
    </>
  );
}
function RadioOptions<TData extends { [K: string]: any }>(props: { options: DataTableOptionsDef<TData>["options"] }) {
  return props.options.map((option) => (
    <DropdownMenuRadioItem key={option.value} aria-label={option.label} value={String(option.value)} iconType="check">
      {option.label}
    </DropdownMenuRadioItem>
  ));
}

function DeleteAction<TData extends { [K: string]: any }>(props: {
  dataRow: TData;
  onDelete?: DataTableRowActionsDef<TData>["onDelete"];
}) {
  const { onDelete, dataRow } = props;
  if (!onDelete) return null;

  return (
    <Dialog>
      <DialogTrigger asChild>
        <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="font-medium text-red-600 dark:text-red-500">
          حذف
          <DropdownMenuIconWrapper>
            <Trash className="text-red-600 opacity-70 dark:text-red-500" />
          </DropdownMenuIconWrapper>
        </DropdownMenuItem>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>تأكيد الحذف</DialogTitle>
          <DialogDescription>
            هذا الإجراء سيكون نهائيًا ولا رجعة فيه. لن يكون بإمكانك استرجاع أي من هذه البيانات، هل أنت متأكد من أنك ترغب
            في المتابعة؟
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button autoFocus={false} asChild variant="light">
              <DropdownMenuItem>إلغاء</DropdownMenuItem>
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              autoFocus={false}
              variant="destructive"
              onClick={async () => {
                if (onDelete) {
                  const deleteData = await onDelete({ data: [dataRow] });

                  if (deleteData.success && deleteData.message) toast.success(deleteData.message);
                  if (!deleteData.success && deleteData.message) toast.error(deleteData.message);
                }
              }}
            >
              حــــذف
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
