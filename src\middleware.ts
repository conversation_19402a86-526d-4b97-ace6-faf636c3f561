import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { verifySession } from "./auth/dal";
import { dashboardMiddleware } from "./components/dashboard/components/auth/dashboard-middleware";

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // ===========================================================================
  // خاص بصفحة تجربتي الإعلامية
  // ===========================================================================
  if (pathname === "/media-experience") {
    return NextResponse.redirect(new URL("/media-experience/articles/1", request.url));
  }

  // ===========================================================================
  // خاص بـأمين صفحات محاور الكورس
  // ===========================================================================
  if (pathname.startsWith("/courses/")) {
    const session = await verifySession();

    const callbackUrl = encodeURIComponent(pathname);

    if (!session) {
      const newUrl = new URL(`/auth/login?callback-url=${callbackUrl}`, request.url);
      return NextResponse.redirect(newUrl);
    }

    // تحويل الرابط الى مصفوفة
    const arrayUrl = pathname.split("/").filter((e) => e !== "");

    // استخراج معرف الكورس
    const courseId = arrayUrl.at(1);

    // التأكد من ان المستخدم قام بالإشتراك في الكورس
    const isSubscribe = session.purchasesIds.includes(String(courseId));
    if (!isSubscribe) {
      return NextResponse.redirect(new URL(`/courses/${courseId}`, request.url));
    }
  }

  // ===========================================================================
  // خاص بصفحات المصادقة اذا كان المستخدم مسجل الدخول يتم منعة من الدخول الى الصفحات
  // ===========================================================================
  if (pathname.startsWith("/auth")) {
    const session = await verifySession();
    if (!session) return NextResponse.next();
    const callbackUrl = searchParams.get("callback-url");
    return NextResponse.redirect(new URL(callbackUrl ?? "/", request.url));
  }

  // ===========================================================================
  // خاص بصفحات الملف الشخصي يتم منع المستخدم من الدخول اذا لم يقم بتسجيل الدخول
  // ===========================================================================
  if (pathname.startsWith("/profile")) {
    const session = await verifySession();
    if (!session) {
      return NextResponse.redirect(new URL("/auth/login", request.url));
    }
  }

  if (pathname.startsWith("/dashboard-login") || pathname.startsWith("/admin")) {
    // دالة البرامج الوسيطة الخاصة بلوحة التحكم
    return await dashboardMiddleware(request);
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    "/media-experience",
    "/courses/(.*)/(.*)/:path*",
    "/auth/(.*)",
    "/profile/:path*",
    "/admin/:path*",
    "/dashboard-login",
  ],
};
