import FormatTime from "@/components/ui/format-time";
import { Consultation } from "@prisma/client";

export function ConsultationCard({ consultation, onClick }: { consultation: Consultation; onClick: () => void }) {
  return (
    <div
      onClick={onClick}
      id={consultation.id}
      className="mb-3 w-full rounded-lg border p-3 transition-colors **:cursor-default **:select-none last:mb-0 hover:border-transparent hover:bg-gray-100 data-activated:border-transparent data-activated:bg-gray-100 dark:hover:bg-gray-900 dark:data-activated:bg-gray-900"
    >
      {/* اسم الاستشارة */}
      <div className="flex items-center justify-between gap-x-2">
        <div className="flex w-full max-w-max items-center gap-2 overflow-hidden text-ellipsis">
          <p className="max-w-max overflow-hidden text-base font-medium text-nowrap text-ellipsis">
            {consultation.name}
          </p>
          {!consultation.read && <span className="size-2 shrink-0 rounded-full bg-blue-600" />}
        </div>
        <p className="min-w-fit text-xs text-nowrap text-gray-600 dark:text-gray-400">
          <FormatTime dateInput={consultation.createdAt} />
        </p>
      </div>

      {/* رقم الهاتف */}
      <p dir="ltr" className="mt-1.5 text-right text-sm">
        {consultation.phone}
      </p>

      {/* نص الرسالة */}
      <p className="mt-2 w-full overflow-hidden pl-2 text-sm text-nowrap text-ellipsis text-gray-600 dark:text-gray-400">
        {consultation.message}
      </p>
    </div>
  );
}
