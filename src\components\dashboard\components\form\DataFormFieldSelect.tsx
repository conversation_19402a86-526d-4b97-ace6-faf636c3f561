import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/Select";

import { DataFormFieldProps } from "./DataFormField";
import FieldAttachments from "../ui/FieldAttachments";

export default function DataFormFieldSelect<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>,
) {
  if (!props.selectConfig) {
    throw new Error(
      `When the fieldType property value is "select" it will be necessary to provide the selectConfig property and set it correctly.`,
    );
  }

  return (
    <FieldAttachments
      label={props.label}
      description={props.description}
      required={props.required}
      htmlFor={props.accessorKey}
    >
      <Select
        defaultValue={String(props.defaultValue)}
        required={props.required}
        name={props.accessorKey}
        disabled={props.isPending}
      >
        <SelectTrigger hasError={!!props.errorMessage} id={props.accessorKey}>
          <SelectValue placeholder={props.placeholder} />
        </SelectTrigger>
        <SelectContent>
          {props.selectConfig.options.map((item) => (
            <SelectItem key={String(item.value)} value={String(item.value)}>
              <span className="flex items-center gap-x-2 [&_svg]:!size-4 [&_svg]:shrink-0 [&_svg]:text-gray-500">
                {item.icon}
                {item.label}
              </span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </FieldAttachments>
  );
}
