import { z } from "zod";

export const adminSchema = z.object({
  id: z.string(),
  name: z.string().max(20).min(2),
  email: z.string().email(),
  password: z.string().max(40).min(6),
  status: z.enum(["activated", "disabled"]).optional().default("activated"),
  role: z.enum(["superadmin", "admin"]).optional().default("superadmin"),
  accessiblePages: z.array(z.string()).optional().default([]),

  avatarUrl: z.preprocess((val) => {
    if (typeof val !== "string" || val.trim() === "") return null;
    return z.string().url().safeParse(val).success ? val : null;
  }, z.string().url().nullable().default(null)),
});
