"use server"

import webpush from "web-push"

import prisma from "@/lib/prisma"
import { verifyAdminSession } from "@/components/dashboard/components/auth/admin-session"

// تعيين تفاصيل VAPID
webpush.setVapidDetails(
  "mailto:<EMAIL>",
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!,
  process.env.VAPID_PRIVATE_KEY!
)

// حفظ اشتراك المشرف
export async function subscribeUser(subscription: PushSubscription) {
  try {
    // تحقق مما إذا كان المستخدم مشرفًا
    const adminId = await verifyAdminSession()
    if (!adminId) return { success: false, error: "Unauthorized" }

    await prisma.admin.update({
      where: { id: adminId.id },
      data: {
        pushSubscription: JSON.stringify(subscription),
      },
    })

    return { success: true }
  } catch (error) {
    console.error("خطأ في حفظ اشتراك الإشعارات:", error)
    return { success: false, error }
  }
}

// إلغاء اشتراك المشرف
export async function unsubscribeUser() {
  try {
    const adminId = await verifyAdminSession()
    if (!adminId) return { success: false, error: "Unauthorized" }

    await prisma.admin.update({
      where: { id: adminId.id },
      data: {
        pushSubscription: null,
      },
    })

    return { success: true }
  } catch (error) {
    console.error("خطأ في إلغاء اشتراك الإشعارات:", error)
    return { success: false, error }
  }
}

// إرسال إشعار للمشرفين
export async function sendNotificationToAdmins(title: string, message: string) {
  try {
    // جلب جميع المشرفين الذين لديهم اشتراكات إشعارات
    const admins = await prisma.admin.findMany({
      where: {
        pushSubscription: { not: null },
      },
      select: {
        pushSubscription: true,
      },
    })

    const results = await Promise.all(
      admins.map(async (admin) => {
        if (!admin.pushSubscription) return { success: false }

        const subscription = JSON.parse(admin.pushSubscription)

        try {
          await webpush.sendNotification(
            subscription,
            JSON.stringify({
              title,
              body: message,
              icon: "/icon-512x512.png",
            })
          )
          return { success: true }
        } catch (error) {
          console.error("خطأ في إرسال الإشعار:", error)
          return { success: false, error }
        }
      })
    )

    return { success: true, results }
  } catch (error) {
    console.error("خطأ في إرسال الإشعارات:", error)
    return { success: false, error }
  }
}
