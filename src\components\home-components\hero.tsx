import Image from "next/image";
import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import heroImage from "@/../public/home-files/hero-image.png";
import heroImagePhone from "@/../public/home-files/hero-image-phone.png";
import { LogoLarge } from "../ui/svg/logos";
import { DividerShadow_Y, HeroImageFrame, HeroImageFramePhone } from "../ui/svg/icons";

export default function Hero() {
  return (
    <div className="relative flex w-full flex-col overflow-clip max-lg:pb-28 lg:flex-row">
      {/* صورة الهيرو الخاصة بالشاشات الصغيرة */}
      <div className="relative mt-12 w-full lg:hidden">
        <Image
          placeholder="blur"
          fetchPriority="high"
          src={heroImagePhone}
          alt="الدكتورة ناهد باشطح"
          className="h-auto w-full border-2 border-x-secondary border-y-background lg:hidden"
        />
        <HeroImageFramePhone className="absolute top-0 h-auto w-full" />
      </div>

      {/* نصوص الهيرو */}
      <div className="relative z-20 flex max-w-full flex-1 flex-col items-center text-balance px-6 max-lg:min-h-96 max-lg:justify-center lg:w-[60%] lg:pt-28">
        <div className="relative flex w-full max-w-xl flex-col items-center justify-center text-center">
          {/* صورة الشعار الخاص بالشاشات الكبيرة */}
          <LogoLarge className="hidden lg:block" />
          <h1 className="mt-5 bg-primary-gradient-x bg-clip-text py-1 text-[40px] font-bold text-transparent drop-shadow-[0_4px_2px_#00000050]">
            اكاديمية د.ناهد باشطح
          </h1>
          <p className="mt-2 text-lg font-bold drop-shadow-[0_2px_3px_#00000030] lg:text-xl">
            خبيرة في دعم جودة الحياة للقيادات وصناع القرار.
          </p>
          <p className="mt-7 w-full text-balance text-center text-base">
            مرحبًا بك في عالم العلاج الشعوري ،نحن هنا لمساعدة القادة ونخب المجتمع
            والمشاهير على تحرير مشاعرهم السلبية، وتجاوز ضغوط الأضواء والمسؤوليات.
          </p>
          <div className="mt-8 flex gap-4">
            <div>
              <Button asChild>
                <Link href="/consultation">
                  حجز استشارة <ChevronLeft />
                </Link>
              </Button>
            </div>
            <div>
              <Button variant="outline" asChild>
                <Link href="/about-me">
                  نبذة عني <ChevronLeft />
                </Link>
              </Button>
            </div>
          </div>
          <DividerShadow_Y className="pointer-events-none absolute -right-20 -top-14 scale-y-90 select-none object-fill sm:-top-28 sm:scale-75 lg:top-1 lg:scale-y-100" />
          <DividerShadow_Y className="pointer-events-none absolute -left-20 -top-14 rotate-180 scale-y-90 select-none sm:-top-28 sm:scale-75 lg:top-1 lg:scale-y-100" />
        </div>
      </div>

      {/* صورة الهيرو الخاصة بالشاشات الكبيرة */}
      <div className="relative hidden h-[750px] w-[514px] bg-secondary lg:block">
        <Image
          src={heroImage}
          className="hidden lg:block"
          placeholder="blur"
          fetchPriority="high"
          alt="الموقع الرسمي للدكتورة ناهد باشطح"
        />
        <HeroImageFrame className="absolute top-0 hidden lg:block" />
      </div>
    </div>
  );
}
