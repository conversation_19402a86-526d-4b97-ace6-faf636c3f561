import ClientSideDynamicComment from "./client-side-dynamic-comments";
import { CommentCard } from "./comment-card";
import { AddComment } from "./add-comment";
import { EntityType } from "@prisma/client";
import { getComments } from "@/utils/get-data-from-db";

/**
 *
 * المكون الرئيسي للتعليقات الخاصة بجميع المجالات //=============================================== Comments
 * يشمل كل الأكواد المطلوبة لتمكين العميل من اضافة تعليق // =================================== AddComment
 * يقوم بإستيراد مكون المنطق البرمجي الخاص بالتمرير الانهائي بشكل ديناميكي ومشروط //== ClientSideDynamicComment
 * بطاقة التعليق يستخدم هنا لإنشاء التعليقات التي يتم جلبها في الخادم // =========================== CommentCard
 *
 */

type CommentProps = { entity: EntityType; entityId: string; pathRevalidate: string };

export default async function Comments({
  entity,
  entityId,
  pathRevalidate,
}: CommentProps) {
  const { data, pagination } = await getComments({ entity, entityId, page: 1 });
  const hasMore = pagination.totalPages > pagination.currentPage;

  return (
    <div className="relative">
      <AddComment entity={entity} entityId={entityId} pathRevalidate={pathRevalidate} />

      {data.length ? (
        <div>
          {data.map((comment) => (
            <CommentCard
              usingAnimate
              key={comment.id}
              comment={comment}
              pathRevalidate={pathRevalidate}
            />
          ))}

          <ClientSideDynamicComment
            entity={entity}
            hasMore={hasMore}
            entityId={entityId}
            pathRevalidate={pathRevalidate}
          />
        </div>
      ) : (
        <p className="pr-16 text-xs md:text-sm">
          لا يوجد تعليقات حتى الآن، كن أنت أول من يضيف تعليق
        </p>
      )}
    </div>
  );
}
