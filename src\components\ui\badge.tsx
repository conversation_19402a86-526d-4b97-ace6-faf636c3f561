import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md px-3 py-0.5 pb-1 text-xs font-semibold transition-colors",
  {
    variants: {
      variant: {
        green: "border-transparent bg-green-100 text-green-700 hover:bg-green-200",
        paid: "border-transparent bg-paid text-paid-foreground hover:bg-paid/80",
        destructive: "border-transparent bg-red-500 text-zinc-50 hover:bg-red-500/80",
        outline: "text-zinc-950",
      },
    },
    defaultVariants: {
      variant: "green",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <span className={cn(badgeVariants({ variant }), className,)} {...props} />;
}

export { Badge, badgeVariants };
