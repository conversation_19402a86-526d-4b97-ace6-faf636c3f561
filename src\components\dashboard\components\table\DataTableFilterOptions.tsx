import React from "react";
import { DataTableOptionsDef } from "./types";
import DataTableFilterTrigger from "./DataTableFilterTrigger";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "../ui/Command";

import { Popover, PopoverClose, PopoverContent } from "../ui/Popover";
import { Column } from "@tanstack/react-table";
import { Checkbox } from "../ui/Checkbox";

type DataTableFilterOptionsProps<TData extends { [K: string]: any }> = {
  optionFilters?: DataTableOptionsDef<TData>[];
  getColumn: (columnId: string) => Column<TData, unknown> | undefined;
};

export default function DataTableFilterOptions<TData extends { [K: string]: any }>(
  props: DataTableFilterOptionsProps<TData>,
) {
  const { optionFilters, getColumn } = props;

  return optionFilters?.map((option) => (
    <FilterOption key={option.columnKey} {...option} getColumn={getColumn} />
  ));
}

function FilterOption<TData extends { [K: string]: any }>(
  props: DataTableOptionsDef<TData> & {
    getColumn: (columnId: string) => Column<TData, unknown> | undefined;
  },
) {
  const { columnKey, label, options, getColumn } = props;
  const column = getColumn(columnKey);
  const facets = column?.getFacetedUniqueValues();
  const selectedValues = new Set(column?.getFilterValue() as (string | number)[]);

  return (
    <Popover key={columnKey}>
      <DataTableFilterTrigger
        onClearFilter={() => column?.setFilterValue(undefined)}
        selectedValues={selectedValues}
        options={options}
      >
        {label}
      </DataTableFilterTrigger>
      <PopoverContent className="p-0" sideOffset={4} align="start" dir="rtl">
        <Command>
          <CommandInput autoFocus={false} placeholder={label} name={label} id={label} />
          <CommandList>
            <CommandEmpty>لم يتم العثور على نتائج.</CommandEmpty>
            <CommandGroup>
              {options?.map((option) => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      if (isSelected) {
                        selectedValues.delete(option.value);
                      } else {
                        selectedValues.add(option.value);
                      }
                      const filterValues = Array.from(selectedValues);
                      column?.setFilterValue(filterValues.length ? filterValues : undefined);
                    }}
                  >
                    <Checkbox checked={isSelected} className="!cursor-default" />
                    {option?.icon && (
                      <div className="text-gray-600 dark:text-gray-400 [&_svg]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0 ">
                        {option.icon}
                      </div>
                    )}
                    <span>{option.label}</span>
                    {facets?.get(option.value) && (
                      <span className="mr-auto flex h-4 items-center pt-px text-sm">
                        {facets.get(option.value)}
                      </span>
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <PopoverClose asChild>
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => column?.setFilterValue(undefined)}
                      className="justify-center text-center"
                    >
                      إلغاء الفلتر
                    </CommandItem>
                  </CommandGroup>
                </PopoverClose>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
