import { rankItem } from "@tanstack/match-sorter-utils";
import { FilterFn } from "@tanstack/react-table";

export const fuzzySearch: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta({
    itemRank,
  });
  return itemRank.passed;
};


// type NumberFilter = {
//   condition: "is-equal-to" | "is-between" | "is-greater-than" | "is-less-than";
//   value: [number | string, number | string];
// };

// const numberFilter: FilterFn<any> = (row, columnId, filterValue: NumberFilter) => {
//   const value = row.getValue(columnId) as number;
//   const [min, max] = filterValue.value as [number, number];

//   switch (filterValue.condition) {
//     case "is-equal-to":
//       return value == min;
//     case "is-between":
//       return value >= min && value <= max;
//     case "is-greater-than":
//       return value > min;
//     case "is-less-than":
//       return value < min;
//     default:
//       return true;
//   }
// };
