import React from "react";
import FieldAttachments from "../ui/FieldAttachments";

import { Textarea } from "../ui/Textarea";
import { DataFormFieldProps } from "./DataFormField";

export default function DataFormFieldTextarea<TData extends { [K: string]: any }>(props: DataFormFieldProps<TData>) {
  const [warningMessage, setWarningMessage] = React.useState<string[] | undefined>(undefined);

  function handleOnChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    const validate = props.textareaConfig?.validateWarning?.safeParse(e.target.value);
    if (!validate?.success) {
      setWarningMessage(validate?.error.flatten().formErrors);
    } else if (validate?.success) {
      setWarningMessage((prev) => prev && undefined);
    }
  }

  const textareaRef = React.useRef<HTMLTextAreaElement | null>(null);

  const handleInput = () => {
    const el = textareaRef.current;
    if (el) {
      el.style.height = "auto";
      el.style.height = el.scrollHeight + 5 + "px";
    }
  };

  return (
    <FieldAttachments
      label={props.label}
      htmlFor={props.accessorKey}
      description={props.description}
      required={props.required}
      errorMessage={props.errorMessage}
      warningMessage={warningMessage}
    >
      <Textarea
        className="resize-none"
        onInput={handleInput}
        ref={textareaRef}
        id={props.accessorKey}
        name={props.accessorKey}
        disabled={props.isPending}
        placeholder={props.placeholder}
        defaultValue={props.defaultValue}
        onChange={handleOnChange}
      />
    </FieldAttachments>
  );
}
