"use client";
import "filepond/dist/filepond.min.css";

import React from "react";
import dynamic from "next/dynamic";

import { fileUploaderDeleteFile, fileUploaderS3Presigner } from "./server";
import { FilePondFile, FilePondInitialFile, registerPlugin } from "filepond";
import { FilePondProps } from "react-filepond";
import { safeParse } from "../../lib/utils";
import { cx } from "../../lib/utils-tremor";
import { Skeleton } from "../ui/Skeleton";

import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";

const FilePond = dynamic(() => import("react-filepond").then((e) => e.FilePond), {
  loading: () => (
    <div className="bo flex h-20 w-full items-center justify-center rounded-md border border-gray-300 bg-white dark:border-gray-800 dark:bg-gray-950">
      <Skeleton className="h-4 w-40 sm:w-60" />
    </div>
  ),
  ssr: false,
}) as React.ComponentClass<FilePondProps>;

registerPlugin(FilePondPluginFileValidateType);

export type FileType = {
  fileUrl: string;
  fileName?: string;
  type?: string;
  size?: number;
  createAt?: Date;
  metaData?: { [K: string]: any };
};

export type DataFormFieldFileUploaderProps = {
  dataType?: "object" | "string";
  defaultValue?: FileType[] | string[] | string | FileType;
  acceptedFileTypes?: string[];
  instantUpload?: boolean;
  allowMultiple?: boolean;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  maxFiles?: number;
  name?: string;
};

export default function FileUploader(props: DataFormFieldFileUploaderProps) {
  type FilesStateType = (string | FilePondInitialFile | FilePondFile | Blob | File)[];
  const [files, setFiles] = React.useState<FilesStateType>([]);
  const sources = useSources(files as FilePondFile[], {
    allowMultiple: props.allowMultiple,
    dataType: props.dataType,
  });

  React.useEffect(() => {
    let cancelled = false;

    const timeOut = setTimeout(() => {
      generateDefaultValue({ defaultValue: props.defaultValue }).then((value) => {
        if (!cancelled) setFiles(value); // هذا السطر الحامي
      });
    }, 500);

    return () => {
      cancelled = true; // هذا يمنع setFiles من التنفيذ إذا تغيرت props.defaultValue
      clearTimeout(timeOut);
    };
  }, [props.defaultValue]);

  return React.useMemo(
    () => (
      <div className="relative min-h-20">
        <input
          onChange={() => {}}
          type="text"
          id={props.name}
          name={props.name}
          required={props.required ?? true}
          className="sr-only absolute right-1/2 bottom-0"
          value={sources}
        />
        <FilePond
          id={"$filepond"}
          className={cx([
            "!mb-0",
            // الخلفية
            "[&_.filepond--panel-root]:!bg-white [&_.filepond--panel-root]:dark:!bg-gray-950",
            // الإطار
            "[&_.filepond--panel-root]:border [&_.filepond--panel-root]:!border-gray-300 [&_.filepond--panel-root]:dark:!border-gray-800",
            // خلفية الملف
            "[&_.filepond--item-panel]:!bg-gray-200 [&_.filepond--item-panel]:dark:!bg-gray-600",
            // بيانات الملف
            "[&_.filepond--file]:!text-gray-950 [&_.filepond--file]:dark:!text-gray-50",
            // plas
            "[&_.filepond--drop-label]:!text-gray-400 [&_.filepond--drop-label]:dark:!text-gray-500",
            // دائرة الإسقاط
            "[&_.filepond--drip-blob]:!bg-gray-600 [&_.filepond--drip-blob]:dark:!bg-gray-400",
            // لون أيقونات أزرار الإجراءات السوداء
            "[&_.filepond--file-action-button]:!text-white",
            // الزر
            "[&_.filepond--file-action-button]:!cursor-pointer",
          ])}
          files={files as string[]}
          acceptedFileTypes={props?.acceptedFileTypes ?? ["image/*"]}
          allowFileTypeValidation={true}
          allowReorder={props.allowMultiple}
          allowMultiple={props.allowMultiple}
          maxFiles={props.maxFiles ?? null}
          instantUpload={props.instantUpload ?? true}
          disabled={props.disabled}
          credits={false}
          onupdatefiles={setFiles}
          onprocessfile={() => setFiles((prev) => [...prev])}
          onprocessfilerevert={() => setFiles((prev) => [...prev])}
          onreorderfiles={(files) => setFiles(files)}
          onactivatefile={(file) => {
            const fileUrl = (JSON.parse(file.serverId) as FileType).fileUrl;
            try {
              window.open(fileUrl, "_blank");
              // eslint-disable-next-line
            } catch (error) {
              return;
            }
          }}
          server={{
            process: (fieldName, file, metadata, load, error, progress, abort) => {
              let request: XMLHttpRequest;

              (async () => {
                const { signedUrl, fileUrl } = await fileUploaderS3Presigner({
                  fileName: file.name,
                  fileType: file.type,
                  fileSize: file.size,
                });

                request = new XMLHttpRequest();
                request.open("PUT", signedUrl);

                request.upload.onprogress = (e) => {
                  progress(e.lengthComputable, e.loaded, e.total);
                };

                request.onload = function () {
                  const fileSource: FileType = {
                    fileUrl: fileUrl,
                    fileName: file.name,
                    type: file.type,
                    size: file.size,
                  };
                  load(JSON.stringify(fileSource));
                };

                request.onerror = function () {
                  error("حدث خطأ اثناء رفع الملف");
                };

                request.send(file);
              })();

              return {
                abort: () => {
                  if (request) request.abort();
                  abort();
                },
              };
            },
            revert: async (source: string, load, error) => {
              try {
                const isDefaultFile = !!(files as FilePondInitialFile[]).find(
                  (file) => typeof file !== "string" && file?.source === source,
                );

                if (isDefaultFile) return load();

                const fileUrl = (JSON.parse(source) as FileType).fileUrl;
                (async () => await fileUploaderDeleteFile({ fileUrl }))();

                return load();
              } catch (err: any) {
                return error(err);
              }
            },
            load: null,
            restore: null,
            fetch: null,
            remove: null,
          }}
          name="$filepond"
          labelIdle={props.placeholder ?? `اسحب وأفلِت ملفاتك أو <span class="filepond--label-action">تصفح</span>`}
        />
      </div>
    ),
    [
      files,
      props.acceptedFileTypes,
      props.allowMultiple,
      props.disabled,
      props.instantUpload,
      props.maxFiles,
      props.name,
      props.placeholder,
      props.required,
      sources,
    ],
  );
}

async function getFileInfo(fileUrl: string) {
  const segments = fileUrl.split("/");
  const getFileNameFromUrl = segments[segments.length - 1].split("?")[0];

  try {
    const response = await fetch(fileUrl, { method: "HEAD" });

    if (!response.ok) {
      throw new Error(`Failed to fetch headers: ${response.status}`);
    }

    const contentType = response.headers.get("content-type");
    const contentLength = response.headers.get("content-length");

    return {
      type: contentType ?? undefined,
      size: contentLength ? parseInt(contentLength, 10) : undefined,
      fileName: getFileNameFromUrl,
    };

    // eslint-disable-next-line
  } catch (error) {
    console.log(
      `غير قادر على الوصول الى معلومات الملف من هذا المصدر ${fileUrl} يمكن ان يكون سبب هذه المشكلة هوا ان المصدر لا يسمح بطلبات HEAD او ان الإستجابه لم تتضمن الرأس Access-Control-Allow-Origin`,
    );

    return {
      type: undefined,
      size: undefined,
      fileName: getFileNameFromUrl,
    };
  }
}

async function generateDefaultValue({ defaultValue }: DataFormFieldFileUploaderProps): Promise<FilePondInitialFile[]> {
  if (!defaultValue) return [];

  if (Array.isArray(defaultValue)) {
    return await Promise.all(defaultValue?.map((source) => generateItemDefaultValeu(source)));
  }

  return [await generateItemDefaultValeu(defaultValue)];
}

async function generateItemDefaultValeu(source: string | FileType): Promise<FilePondInitialFile> {
  if (typeof source === "string") {
    const { fileName, size, type } = await getFileInfo(source);
    return {
      source: JSON.stringify({ fileUrl: source, fileName, size, type }),
      options: {
        type: "limbo",
        file: { name: fileName, size, type },
      },
    };
  }

  return {
    source: JSON.stringify(source),
    options: {
      type: "limbo",
      file: { name: source.fileName, size: source.size, type: source.type },
    },
  };
}

function useSources(files: FilePondFile[], props: { allowMultiple?: boolean; dataType?: "object" | "string" }) {
  return React.useMemo(() => {
    const sources = (files ?? []).map((file) => safeParse(file?.serverId)).filter(Boolean) as FileType[];

    if (!sources.length) return "";

    const isMultiple = props.allowMultiple;
    const isObject = props.dataType === "object";

    if (isMultiple) {
      return JSON.stringify(isObject ? sources : sources.map((e) => e.fileUrl));
    }

    const first = sources.at(0);
    return JSON.stringify(isObject ? first : (first?.fileUrl ?? ""));
  }, [files, props.allowMultiple, props.dataType]);
}

/**
 * import FilePondPluginImageExifOrientation from "filepond-plugin-image-exif-orientation";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
// eslint-disable-next-line
// @ts-ignore
import FilePondPluginMediaPreview from "filepond-plugin-media-preview";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css";
import "filepond-plugin-media-preview/dist/filepond-plugin-media-preview.min.css";

registerPlugin(
  FilePondPluginImageExifOrientation,
  FilePondPluginImagePreview,
  FilePondPluginMediaPreview,
);

 */
