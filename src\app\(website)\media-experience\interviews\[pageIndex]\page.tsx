import { getInterviews } from "@/utils/get-data-from-db";
import { Pagination } from "@/components/ui/pagination";
import { CalendarDays } from "lucide-react";
import { Interview } from "@prisma/client";
import { Metadata } from "next";

import FormatTime from "@/components/ui/format-time";
import Image from "next/image";
import Link from "next/link";

type InterviewsPageProps = { params: Promise<{ pageIndex: string }> };

// ==================================================================================
// صفحة تعرض كل المقابات
// ==================================================================================
export async function generateMetadata(props: InterviewsPageProps): Promise<Metadata> {
  const pageIndex = Number((await props.params).pageIndex);

  const { data, pagination } = await getInterviews({ pageIndex });
  const { currentPage, totalPages } = pagination;
  const titleInterviews = data.flatMap((book) => book.title);

  // العلامات الوصفية للترقيم الصفحي
  const isPrevious = currentPage > 1;
  const isNext = totalPages > currentPage;
  const next = isNext ? `/media-experience/interviews/${currentPage + 1}` : undefined;
  const previous = isPrevious
    ? `/media-experience/interviews/${currentPage - 1}`
    : undefined;

  return {
    title: `مقابلات د. ناهد باشطح [${pageIndex}]`,
    description: titleInterviews.join(" || "),
    pagination: { previous, next },
    keywords: titleInterviews,
  };
}

export default async function InterviewsPage(props: InterviewsPageProps) {
  const pageIndex = Number((await props.params).pageIndex);

  const { data, pagination } = await getInterviews({ pageIndex });
  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>;

  return (
    <>
      {data.map((interview) => (
        <InterviewCard key={interview.id} interview={interview} pageIndex={pageIndex} />
      ))}
      <Pagination url="/media-experience/interviews" {...pagination} />
    </>
  );
}

// ==================================================================================
// مكون بطاقة المقابلة
// ==================================================================================
function InterviewCard({
  pageIndex,
  interview,
}: {
  pageIndex: number;
  interview: Interview;
}) {
  return (
    <div className="w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="flex flex-col gap-3 p-3 pb-7 sm:flex-row md:py-5">
        <div className="space-y-2 rounded-md bg-secondary p-2 shadow-md">
          <div className="relative aspect-video w-full shrink-0 overflow-clip rounded-md border border-muted/0 bg-muted/50 shadow-md sm:w-40">
            <Image
              sizes="(max-width: 768px) 80vw, 10vw"
              src={interview.thumbnail}
              className="h-auto w-full object-cover"
              alt={interview.title}
              fill
            />
          </div>
          <div className="flex items-center gap-1 text-xs text-background/70">
            <CalendarDays className="mb-0.5 size-4 text-background/60" />{" "}
            <FormatTime dateInput={interview.createdAt} />
          </div>
        </div>
        <div className="space-y-2 md:space-y-3">
          <Link
            href={`/media-experience/interviews/${pageIndex}/${interview.id}#pagination`}
            className="text-lg font-bold text-primary underline hover:text-primary/90"
          >
            {interview.title}
          </Link>
          <p className="line-clamp-3 text-sm">{interview.description}</p>
        </div>
      </div>
    </div>
  );
}
