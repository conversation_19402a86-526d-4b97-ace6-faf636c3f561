import TitleSection from "../ui/title-section";
import CourseCard from "../ui/course-card";
import Link from "next/link";

import { ArrowLeft } from "lucide-react";
import { getHomeCourses } from "@/utils/get-data-from-db";

export default async function SomeCourses() {
  const courses = await getHomeCourses();

  if (!courses || !courses.length) return null;

  return (
    <div className="mt-20 flex flex-col gap-24 rounded-xl pt-28 shadow-[0_0px_15px_-4px_rgb(0_0_0/0.4)]">
      <TitleSection
        title={<h2>كورسات متخصصة</h2>}
        description="مجموعة دورات شاملة لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات، من خلال تقنيات فعّالة لتحقيق التوازن النفسي والجسدي"
      />
      {courses.map((course) => (
        <CourseCard key={course.id} {...course} />
      ))}
      <div className="mx-auto -mb-6 w-fit rounded-lg bg-background/5 px-5 py-3 backdrop-blur-sm">
        <Link
          className="group flex items-center gap-2 py-0.5 text-primary underline hover:opacity-90"
          href="/courses"
        >
          شاهد المزيد
          <ArrowLeft className="mt-0.5 size-[18px] duration-200 group-hover:-translate-x-1.5" />
        </Link>
      </div>
    </div>
  );
}
