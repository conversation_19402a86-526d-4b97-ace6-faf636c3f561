import { useState, useEffect, useRef } from "react";

export function useOnView(
  // ref: RefObject<Element>,
  rootMargin: string = "0px",
  checkOnce: boolean = false,
) {
  const ref = useRef(null);
  const [isIntersecting, setIntersecting] = useState(false);

  useEffect(() => {
    // Store the current ref value in a variable
    const currentRef = ref.current;

    if (!currentRef) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIntersecting(entry.isIntersecting);
        if (checkOnce && entry.isIntersecting) {
          observer.unobserve(currentRef);
        }
      },
      {
        rootMargin,
      },
    );

    observer.observe(currentRef);

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [ref, rootMargin, checkOnce]);

  return { isOnView: isIntersecting, ref };
}
