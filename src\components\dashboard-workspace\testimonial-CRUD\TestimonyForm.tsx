import React from "react";
import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Testimony } from "@prisma/client";

export default function TestimonyForm({
  mode,
  defaultValues,
  onAction,
}: {
  onAction?: DataFormOnActionDef<Testimony>;
  mode: "create" | "update";
  defaultValues?: Partial<Testimony>;
}) {
  return (
    <DataForm<Testimony>
      fields={[
        {
          label: "شهادة العميل",
          accessorKey: "testimony",
          placeholder: "ادخل شهادة العميل ...",
          fieldType: "textarea",
        },

        { accessorKey: "id", label: "", fieldType: "hidden" },
      ]}
      mode={mode}
      onAction={onAction}
      defaultValues={defaultValues}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/testimonys"
      cancelButtonLabel="إلغاء"
    />
  );
}
