import { DataTableCreateColumnsDef, DataTableFiltersDef, DataTableOptionsDef } from "./types";
type GenerateFiltersProps<TData extends { [K: string]: any }> = {
  createColumns: DataTableCreateColumnsDef<TData>[];
};

export function generateFilters<TData extends { [K: string]: any }>(
  props: GenerateFiltersProps<TData>,
) {
  const { createColumns } = props;

  const filters: DataTableFiltersDef<TData> = {
    options: generateOptionFilters({ createColumns }),
  };

  return filters;
}

/** نمرر لهذا الدالة كل اعمدة الجدول وترجع فلاتر الخيارات  */
function generateOptionFilters<TData extends { [K: string]: any }>(
  props: GenerateFiltersProps<TData>,
) {
  const extractOptions = props.createColumns
    .map(({ accessorKey, columnLabel, valueOptions }) => {
      const { options, enableFilter } = valueOptions ?? {};

      if (!(enableFilter ?? true) || !options) return null;

      return { columnKey: accessorKey as string, label: columnLabel as string, options };
    })
    .filter((option): option is DataTableOptionsDef<TData> => option !== null);

  return extractOptions.length ? extractOptions : undefined;
}
