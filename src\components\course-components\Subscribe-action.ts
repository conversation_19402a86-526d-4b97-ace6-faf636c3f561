"use server";

import prisma from "@/lib/prisma";

import { getCourseById, getUser } from "@/utils/get-data-from-db";
import { createSession } from "@/auth/session";
import { verifySession } from "@/auth/dal";
import { redirect } from "next/navigation";

// ==================================================================================
// دالة خادم متخصصة بالتعامل مع نقر المستخدم على زر اشتراك في الكورس
// ==================================================================================
export async function subscribeAction({
  courseId,
}: {
  courseId: string | undefined;
}): Promise<{
  success: boolean;
  message: string;
}> {
  const user = await getUser();
  const session = await verifySession();

  // اذا كان المستخدم لم يسجل الدخول
  if (!session || !user || !courseId) {
    const callbackUrl = encodeURIComponent(`/courses/${courseId}`);
    redirect(`/auth/login?callback-url=${callbackUrl}`);
  }

  const isSubscribed = user.courseOrders.find((e) => e.courseId === courseId);
  if (isSubscribed) {
    await createSession({
      ...session,
      purchasesIds: [...session.purchasesIds, courseId],
    });
    return { success: true, message: "لقد قمت بالاشتراك في هذا الكورس من قبل" };
  }

  // التأكد من وجود الكورس في قاعدة البيانات وجلب بياناته
  const course = await getCourseById({ courseId });
  if (!course) {
    return {
      success: false,
      message: "حدث خطأ أثناء معالجة البيانات او ربما قد تم حذف هذا الكورس",
    };
  }

  if (course.price === 0) {
    const newOrder = await prisma.courseOrder.create({
      data: {
        status: "PAID",
        price: course.price,
        course: { connect: { id: course.id } },
        user: { connect: { id: user.id } },
      },
    });
    await createSession({
      ...session,
      purchasesIds: [...session.purchasesIds, newOrder.courseId],
    });

    return {
      success: true,
      message: "تم الإشتراك في الكورس بنجاح",
    };
  }

  if (course.price > 0) {
    return { success: false, message: "كورس مدفوع ستم التعامل مع طريقة الشراء لاحقًا" };
  }

  return { success: false, message: "خطأ غير معروف !!" };
}
