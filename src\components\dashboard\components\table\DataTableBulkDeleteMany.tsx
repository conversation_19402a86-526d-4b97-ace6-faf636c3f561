import React from "react";
import { Button } from "../ui/Button";
import { Trash2, X } from "lucide-react";
import { Table } from "@tanstack/react-table";
import { cx } from "../../lib/utils-tremor";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/Dialog";
import { DataTableOnDeleteFnDef } from "./types";
import { toast } from "sonner";

export default function DataTableBulkDeleteMany<TData extends { [K: string]: any }>({
  table,
  onDelete,
}: {
  table: Table<TData>;
  onDelete?: DataTableOnDeleteFnDef<TData>;
}) {
  const rowSelection = table
    .getFilteredSelectedRowModel()
    .rows?.map((row) => row?.original) as TData[];

  const isSelection = rowSelection.length > 0;

  if (!isSelection) return;
  return (
    <div
      className={cx(
        "fixed inset-x-0 bottom-8 z-20 mx-auto hidden w-fit items-center rounded-xl bg-gray-50/70 p-0.5 dark:bg-gray-950/70",
        isSelection && "animate-slideUpAndFade flex",
      )}
    >
      <div className="flex items-center gap-2 rounded-lg border border-dashed border-gray-200 p-1.5 shadow-md shadow-black/40 backdrop-blur-sm dark:border-gray-800">
        <DeleteManyDialog dataRow={rowSelection} table={table} onDelete={onDelete}>
          <Button variant="destructive" className="rounded-lg rounded-l-sm py-1.5">
            حذف
            <Trash2 />
          </Button>
        </DeleteManyDialog>
        <Button
          variant="light"
          onClick={() => table.toggleAllPageRowsSelected(false)}
          className="rounded-lg rounded-r-sm border py-1.5"
        >
          إلغاء
          <X />
        </Button>
        <span className="mx-2 text-sm text-gray-600 dark:text-gray-400">
          تم تحديد {rowSelection.length}
        </span>
      </div>
    </div>
  );
}

function DeleteManyDialog<TData extends { [K: string]: any }>(props: {
  dataRow: TData[];
  onDelete?: DataTableOnDeleteFnDef<TData>;
  children: React.JSX.Element;
  table: Table<TData>;
}) {
  const { onDelete, dataRow, children } = props;

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>تأكيد الحذف</DialogTitle>
          <DialogDescription>
            هذا الإجراء سيكون نهائيًا ولا رجعة فيه. لن يكون بإمكانك استرجاع أي من هذه
            البيانات، هل أنت متأكد من أنك ترغب في المتابعة؟
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <DialogClose asChild>
            <Button autoFocus={false} asChild variant="light">
              <Button variant="light">إلغاء</Button>
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button
              autoFocus={false}
              variant="destructive"
              onClick={async () => {
                if (onDelete) {
                  const deleteData = await onDelete({ data: dataRow });
                  
                  if (deleteData.success) {
                    props.table.toggleAllPageRowsSelected(false);
                    if (deleteData.message) toast.success(deleteData.message);
                  }

                  if (!deleteData.success && deleteData.message)
                    toast.error(deleteData.message);
                }
              }}
            >
              حــــذف
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
