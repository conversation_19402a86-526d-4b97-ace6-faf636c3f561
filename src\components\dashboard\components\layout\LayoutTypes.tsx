/** مصفوفة */
export type Navigation2ChildrenType = {
  label: string;
  href: string;
};

export type NavigationType = {
  label: string;
  href: string;
  icon: React.JSX.Element;
  notifications: boolean | number;
};

export type Navigation2Type = {
  id: string;
  label: string;
  icon: React.JSX.Element;
  children: Navigation2ChildrenType[];
};

export type SidebarItemsType = {
  /** مصفوفة عناصر القائمة الأساسية على سبيل المثال (الصفحة الرئيسية للوحة التحكم, صفحة البريد, صفحة المشرفين) */
  navigation: NavigationType[];
  /** مصفوفة عناصر القائمة الخاصة بصفحات لوحة التحكم على سبيل المثال (صفحات ادارة المحتوى) */
  navigation2: Navigation2Type[];
};
