import prisma from "@/lib/prisma";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import InterviewForm from "@/components/dashboard-workspace/interviews-CRUD/InterviewsForm";

import { interviewsSchema } from "@/components/dashboard-workspace/interviews-CRUD/InterviewsSchema";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import { dataLimits } from "@/utils/siteConfig";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";
import { convertYoutubeUrlToEmbed } from "@/components/dashboard/lib/utils";

export default async function InterviewsUpdatePage(props: { params: Promise<{ interviewId: string }> }) {
  const { interviewId } = await props.params;
  const interview = await prisma.interview.findUnique({ where: { id: interviewId } });
  if (!interview) return notFound();

  return (
    <PageLayout title="تعديل المقابلة">
      <InterviewForm
        mode="update"
        defaultValues={interview}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = interviewsSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const thumbnail = await handleFileUpdate(interview.thumbnail, data.thumbnail, "interviews/image/");

          const [updatedInterview, interviewsBefore] = await prisma.$transaction([
            prisma.interview.update({
              where: { id: data.id },
              data: { ...data, thumbnail,videoUrl: convertYoutubeUrlToEmbed(data.videoUrl) },
            }),

            prisma.interview.count({
              where: {
                createdAt: {
                  gt: interview.createdAt,
                },
              },
            }),
          ]);

          const pageIndex = Math.floor(interviewsBefore / dataLimits.interviews) + 1;

          revalidatePath("/admin/interviews");
          revalidatePath(`/media-experience/interviews/${pageIndex}`);
          revalidatePath(`/media-experience/interviews/${pageIndex}/${updatedInterview.id}`);

          return { success: true, message: "تم تعديل المقابلة بنجاح" };
        }}
      />
    </PageLayout>
  );
}
