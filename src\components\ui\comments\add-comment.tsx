"use client";

import { FormEvent, useState, useTransition } from "react";
import { Textarea } from "../textarea";
import { Button } from "../button";
import { toast } from "sonner";
import { Loader2, SendHorizontal, User } from "lucide-react";
import { Comment, EntityType } from "@prisma/client";
import { addCommentAction } from "./comment-actions";

export type OnAddCommentActionResult = Promise<{
  sucess: boolean;
  message?: string;
  errors?: string | undefined;
  newComment?: Comment;
}>;

type AddCommentProps = { entity: EntityType; entityId: string; pathRevalidate: string };
// ==================================================================================
// مكون متخصص في اضافة تعليق جديد
// ==================================================================================
export function AddComment({ entity, entityId, pathRevalidate }: AddCommentProps) {
  const [commentError, setCommentError] = useState<string | undefined>(undefined);
  const [commentValue, setCommentValue] = useState("");
  const [isPending, startTransition] = useTransition();

  // دالة معالجة ارسال التعليق الى السيرفر
  function handleSubmit(e: FormEvent<HTMLFormElement | HTMLTextAreaElement>) {
    e.preventDefault();

    startTransition(async () => {
      const { sucess, errors, message } = await addCommentAction({
        entity,
        entityId,
        pathRevalidate,
        comment: commentValue,
      });

      if (!sucess) {
        setCommentError(errors);
        if (message) toast.error(message);
        return;
      }

      setCommentValue("");
      if (message) setTimeout(() => toast.success(message), 1000);
      return;
    });
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter") {
      handleSubmit(event);
    }
  };

  return (
    <div className="my-4 flex flex-nowrap items-start gap-5 py-4">
      <div className="flex size-12 shrink-0 items-center justify-center overflow-clip rounded-full bg-secondary">
        <User className="size-7 text-background" />
      </div>
      <form onSubmit={handleSubmit} className="flex w-full flex-col gap-2 md:flex-row">
        <div className="flex-1">
          <label htmlFor="comment" className="sr-only">
            إضافة تعليق
          </label>
          <Textarea
            value={commentValue}
            name="comment"
            id="comment"
            placeholder="إضافة تعليق..."
            onKeyDown={handleKeyDown}
            onChange={(e) => setCommentValue(e.target.value)}
          />
          {commentError && (
            <p className="mt-2 block text-xs text-destructive">{commentError}</p>
          )}
        </div>
        <Button disabled={isPending || !commentValue || commentValue.trim() === ""}>
          تعليق
          {isPending ? (
            <Loader2 className="animate-spin" />
          ) : (
            <SendHorizontal className="rotate-180" />
          )}
        </Button>
      </form>
    </div>
  );
}
