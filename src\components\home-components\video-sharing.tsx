"use client"

import { Share2 } from "lucide-react"

import { But<PERSON> } from "../ui/button"

export default function MyStoryVideoSharing() {
  const domain = process.env.NEXT_PUBLIC_DOMAIN
  return (
    <div className="w-full mt-4">
      <Button
        variant={"outlineDark"}
        className="w-full"
        onClick={async () => {
          if (navigator.share) {
            try {
              await navigator.share({
                url: `${domain}#myStoryVideo`,
              })
              // eslint-disable-next-line
            } catch (error) {
              console.error("خطأ أثناء المشاركة")
            }
          } else {
            alert("المشاركة غير مدعومة على هذا المتصفح.")
          }
        }}
      >
        مشاركة الفيديو <Share2 />
      </Button>
    </div>
  )
}
